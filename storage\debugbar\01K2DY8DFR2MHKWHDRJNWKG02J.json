{"__meta": {"id": "01K2DY8DFR2MHKWHDRJNWKG02J", "datetime": "2025-08-12 08:28:01", "utime": **********.272842, "method": "GET", "uri": "/employer/job", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 37, "messages": [{"message": "[08:28:01] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.059467, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.059904, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-job' limit 1\\n-- \",\n    \"Time:\": 26.86\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.120306, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/employer\\/job', 'http:\\/\\/recland.local\\/employer\\/dashboard', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/dashboard\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImtuMFBSTzdTYjBsVUdlRkp3cnl3ZXc9PSIsInZhbHVlIjoidjZ1UTc3OHVLaUxOTHBHbi9LaWJSMCtaazdiTjh6WGJIeFNvbjYzYlVLUEEvaU5hQkt6dDN3UVI4SlF6a1V4K0RUZXBxY0wzT1NnRXcvU3FLRCtWYW1LM2tDR3J5NXc4aFBLSVBGb2ZROEpZMnVRYUg5cFZoU1RRYzY1bDJZeXgiLCJtYWMiOiJkOWRjZTYxNTZlNDAxOWY4MTU3ZTMyNWQ4OTIxMThkMTE1ZThhOWNiY2NhNmMyYWMxOGVlMmNhNTZkZTlkMTViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImRDbzlYSmhxQmJzVjZNUWdmOWhuTlE9PSIsInZhbHVlIjoiUm1FYnBCdldESWFxdm1QRW9MK3NOSWhSZkUyY1JvbmpoNWNOWTdNRC8renlEQVNzWGZpZm5RODR1SThtcjJhOUUwcHhwVVVqM0E1UDZCWi9QKzVkZ2NtRzg1NEIvcWgrdzA4aXp6TE1DNVhXWEkyTzhqbHBaUndXdUFCYXNYQVkiLCJtYWMiOiJmODI5MTg4N2M3MDkyOTU0MTA1NDA1NjVhZWZjNDAyYjNhMjZlNDdhZWU1ZTA2MzU2MjJjZWFmYjkwNjUzM2Y5IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962079$j15$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:01', '2025-08-12 08:28:01')\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.141494, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.68\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.1467, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.63\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.159369, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.28\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.161818, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 2.85\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.166457, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-job' limit 1\\n-- \",\n    \"Time:\": 0.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.168326, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `employer_id` = '1723' and `company_id` = '84'\\n-- \",\n    \"Time:\": 4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.174786, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `employer_id` = '1723' and `company_id` = '84' order by `id` desc limit 10 offset 0\\n-- \",\n    \"Time:\": 9.89\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.186083, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `submit_cvs` where `submit_cvs`.`job_id` in (483, 1645, 1646, 1647, 1648)\\n-- \",\n    \"Time:\": 5.15\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.193071, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '1723'\\n-- \",\n    \"Time:\": 2.9\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.19803, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `is_active` = '0' and `employer_id` = '1723'\\n-- \",\n    \"Time:\": 3.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.202669, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1648' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')\\n-- \",\n    \"Time:\": 1.48\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.214921, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1647' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')\\n-- \",\n    \"Time:\": 1.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.21786, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1646' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')\\n-- \",\n    \"Time:\": 1.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.220617, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1645' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')\\n-- \",\n    \"Time:\": 1.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.223334, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '483' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')\\n-- \",\n    \"Time:\": 1.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.226028, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.23056, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.232182, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.233785, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.235435, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.237005, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.238582, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.240133, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `meta_data` where `meta_data`.`object_type` = 'App\\\\Models\\\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.243251, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.248349, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.250041, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.25344, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.256649, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.258263, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.259872, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.261398, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.262929, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.264483, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:01] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.266004, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754962080.616296, "end": **********.272905, "duration": 0.656609058380127, "duration_str": "657ms", "measures": [{"label": "Booting", "start": 1754962080.616296, "relative_start": 0, "end": **********.036493, "relative_end": **********.036493, "duration": 0.****************, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.036503, "relative_start": 0.*****************, "end": **********.272908, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.049855, "relative_start": 0.*****************, "end": **********.055127, "relative_end": **********.055127, "duration": 0.*****************, "duration_str": "5.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x frontend.pages.employer.list-job", "param_count": null, "params": [], "start": **********.211967, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/list-job.blade.phpfrontend.pages.employer.list-job", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Flist-job.blade.php&line=1", "ajax": false, "filename": "list-job.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.list-job"}, {"name": "1x frontend.layouts.employer.app", "param_count": null, "params": [], "start": **********.22738, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/app.blade.phpfrontend.layouts.employer.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.employer.app"}, {"name": "2x frontend.layouts.user.avatar", "param_count": null, "params": [], "start": **********.228089, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/user/avatar.blade.phpfrontend.layouts.user.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fuser%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.user.avatar"}, {"name": "2x frontend.layouts.employer.menu", "param_count": null, "params": [], "start": **********.229101, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.phpfrontend.layouts.employer.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.employer.menu"}, {"name": "1x frontend.layouts.modal.employer_confirm", "param_count": null, "params": [], "start": **********.241409, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.phpfrontend.layouts.modal.employer_confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmodal%2Femployer_confirm.blade.php&line=1", "ajax": false, "filename": "employer_confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.modal.employer_confirm"}, {"name": "1x frontend.layouts.login.app", "param_count": null, "params": [], "start": **********.244485, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/login/app.blade.phpfrontend.layouts.login.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Flogin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.login.app"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.245115, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.login.header", "param_count": null, "params": [], "start": **********.245632, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.phpfrontend.inc_layouts.login.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.header"}, {"name": "2x frontend.inc_layouts.notification.drop-notification", "param_count": null, "params": [], "start": **********.251175, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/notification/drop-notification.blade.phpfrontend.inc_layouts.notification.drop-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fnotification%2Fdrop-notification.blade.php&line=1", "ajax": false, "filename": "drop-notification.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.inc_layouts.notification.drop-notification"}, {"name": "1x frontend.inc_layouts.login.footer_v2", "param_count": null, "params": [], "start": **********.267377, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/footer_v2.blade.phpfrontend.inc_layouts.login.footer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Ffooter_v2.blade.php&line=1", "ajax": false, "filename": "footer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.footer_v2"}, {"name": "1x frontend.inc_layouts.login.modal_report", "param_count": null, "params": [], "start": **********.268312, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/modal_report.blade.phpfrontend.inc_layouts.login.modal_report", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fmodal_report.blade.php&line=1", "ajax": false, "filename": "modal_report.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.modal_report"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.269335, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.270309, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET employer/job", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@listJob<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=162\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-job", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=162\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:162-215</a>"}, "queries": {"count": 35, "nb_statements": 35, "nb_visible_statements": 35, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07156, "accumulated_duration_str": "71.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-job' limit 1", "type": "query", "params": [], "bindings": ["employer-job"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.093755, "duration": 0.02686, "duration_str": "26.86ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "54124c3e8e65c71192c70b05b114e1b170665ddc56bb3f0feb56d66438585b66"}, "start_percent": 0, "width_percent": 37.535}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job', 'http://recland.local/employer/dashboard', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/dashboard\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImtuMFBSTzdTYjBsVUdlRkp3cnl3ZXc9PSIsInZhbHVlIjoidjZ1UTc3OHVLaUxOTHBHbi9LaWJSMCtaazdiTjh6WGJIeFNvbjYzYlVLUEEvaU5hQkt6dDN3UVI4SlF6a1V4K0RUZXBxY0wzT1NnRXcvU3FLRCtWYW1LM2tDR3J5NXc4aFBLSVBGb2ZROEpZMnVRYUg5cFZoU1RRYzY1bDJZeXgiLCJtYWMiOiJkOWRjZTYxNTZlNDAxOWY4MTU3ZTMyNWQ4OTIxMThkMTE1ZThhOWNiY2NhNmMyYWMxOGVlMmNhNTZkZTlkMTViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImRDbzlYSmhxQmJzVjZNUWdmOWhuTlE9PSIsInZhbHVlIjoiUm1FYnBCdldESWFxdm1QRW9MK3NOSWhSZkUyY1JvbmpoNWNOWTdNRC8renlEQVNzWGZpZm5RODR1SThtcjJhOUUwcHhwVVVqM0E1UDZCWi9QKzVkZ2NtRzg1NEIvcWgrdzA4aXp6TE1DNVhXWEkyTzhqbHBaUndXdUFCYXNYQVkiLCJtYWMiOiJmODI5MTg4N2M3MDkyOTU0MTA1NDA1NjVhZWZjNDAyYjNhMjZlNDdhZWU1ZTA2MzU2MjJjZWFmYjkwNjUzM2Y5IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962079$j15$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:01', '2025-08-12 08:28:01')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/employer/job", "http://recland.local/employer/dashboard", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/dashboard\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImtuMFBSTzdTYjBsVUdlRkp3cnl3ZXc9PSIsInZhbHVlIjoidjZ1UTc3OHVLaUxOTHBHbi9LaWJSMCtaazdiTjh6WGJIeFNvbjYzYlVLUEEvaU5hQkt6dDN3UVI4SlF6a1V4K0RUZXBxY0wzT1NnRXcvU3FLRCtWYW1LM2tDR3J5NXc4aFBLSVBGb2ZROEpZMnVRYUg5cFZoU1RRYzY1bDJZeXgiLCJtYWMiOiJkOWRjZTYxNTZlNDAxOWY4MTU3ZTMyNWQ4OTIxMThkMTE1ZThhOWNiY2NhNmMyYWMxOGVlMmNhNTZkZTlkMTViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImRDbzlYSmhxQmJzVjZNUWdmOWhuTlE9PSIsInZhbHVlIjoiUm1FYnBCdldESWFxdm1QRW9MK3NOSWhSZkUyY1JvbmpoNWNOWTdNRC8renlEQVNzWGZpZm5RODR1SThtcjJhOUUwcHhwVVVqM0E1UDZCWi9QKzVkZ2NtRzg1NEIvcWgrdzA4aXp6TE1DNVhXWEkyTzhqbHBaUndXdUFCYXNYQVkiLCJtYWMiOiJmODI5MTg4N2M3MDkyOTU0MTA1NDA1NjVhZWZjNDAyYjNhMjZlNDdhZWU1ZTA2MzU2MjJjZWFmYjkwNjUzM2Y5IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962079$j15$l0$h0\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:28:01", "2025-08-12 08:28:01"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.141129, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 37.535, "width_percent": 0.615}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.146189, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 38.15, "width_percent": 0.95}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.1588252, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 39.1, "width_percent": 0.88}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.1616302, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 39.98, "width_percent": 0.391}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 1723 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.1636798, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ef913f8afc9f4d48f551727e02040c91d14047cbc5f6bb1c2d8490bb794fe689"}, "start_percent": 40.372, "width_percent": 3.983}, {"sql": "select * from `seos` where `key` = 'employer-job' limit 1", "type": "query", "params": [], "bindings": ["employer-job"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 165}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.168049, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "54124c3e8e65c71192c70b05b114e1b170665ddc56bb3f0feb56d66438585b66"}, "start_percent": 44.354, "width_percent": 0.489}, {"sql": "select count(*) as aggregate from `job` where `employer_id` = 1723 and `company_id` = 84", "type": "query", "params": [], "bindings": [1723, 84], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 365}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 98}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1708581, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:365", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 365}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=365", "ajax": false, "filename": "JobRepository.php", "line": "365"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `employer_id` = ? and `company_id` = ?", "hash": "bd334e880447f1f28eb01358d46827d9686e4954d40ecd4d09d0b92662f85afb"}, "start_percent": 44.843, "width_percent": 5.59}, {"sql": "select * from `job` where `employer_id` = 1723 and `company_id` = 84 order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1723, 84], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 365}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 98}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1762629, "duration": 0.009890000000000001, "duration_str": "9.89ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:365", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 365}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=365", "ajax": false, "filename": "JobRepository.php", "line": "365"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `employer_id` = ? and `company_id` = ? order by `id` desc limit 10 offset 0", "hash": "da66185835bc1cca2c30adc1fac6233fb28f4c4df0b15c367f3678829bda7934"}, "start_percent": 50.433, "width_percent": 13.821}, {"sql": "select * from `submit_cvs` where `submit_cvs`.`job_id` in (483, 1645, 1646, 1647, 1648)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 365}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 98}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 171}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.187994, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:365", "source": {"index": 20, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 365}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=365", "ajax": false, "filename": "JobRepository.php", "line": "365"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `submit_cvs` where `submit_cvs`.`job_id` in (483, 1645, 1646, 1647, 1648)", "hash": "45a86d498985e8a31633b340ad2ace28028100440268b76a35d37e87cd72998b"}, "start_percent": 64.254, "width_percent": 7.197}, {"sql": "select count(*) as aggregate from `job` where `is_active` = 1 and `employer_id` = 1723", "type": "query", "params": [], "bindings": [1, 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 113}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 173}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1952052, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:389", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=389", "ajax": false, "filename": "JobRepository.php", "line": "389"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `is_active` = ? and `employer_id` = ?", "hash": "45aced023ff853ff47c57e636a155498b907d7716397a9e1e2d6d81b5d3ffb1e"}, "start_percent": 71.451, "width_percent": 4.053}, {"sql": "select count(*) as aggregate from `job` where `is_active` = 0 and `employer_id` = 1723", "type": "query", "params": [], "bindings": [0, 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 113}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 178}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.199348, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:389", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=389", "ajax": false, "filename": "JobRepository.php", "line": "389"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `is_active` = ? and `employer_id` = ?", "hash": "23e5a03146ba5bc3184829d392d83e2c32766274887f8d60310ffdf80a0ca034"}, "start_percent": 75.503, "width_percent": 4.737}, {"sql": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = 1648 and `submit_cvs`.`job_id` is not null and `status` not in (1, 2)", "type": "query", "params": [], "bindings": [1648, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, {"index": 19, "namespace": "view", "name": "frontend.pages.employer.list-job", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/list-job.blade.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.2135189, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Job.php:64", "source": {"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=64", "ajax": false, "filename": "Job.php", "line": "64"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = ? and `submit_cvs`.`job_id` is not null and `status` not in (?, ?)", "hash": "cc03be7f48073b09e3190ec426317db6bd80963ca7c2b662de1fa1c5595fbd4b"}, "start_percent": 80.24, "width_percent": 2.068}, {"sql": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = 1647 and `submit_cvs`.`job_id` is not null and `status` not in (1, 2)", "type": "query", "params": [], "bindings": [1647, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, {"index": 19, "namespace": "view", "name": "frontend.pages.employer.list-job", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/list-job.blade.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.216493, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Job.php:64", "source": {"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=64", "ajax": false, "filename": "Job.php", "line": "64"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = ? and `submit_cvs`.`job_id` is not null and `status` not in (?, ?)", "hash": "c65352b10a45edd7bdc25c680bf0fa32370a5c7550856d6a7e3630adbbbe1119"}, "start_percent": 82.309, "width_percent": 2.012}, {"sql": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = 1646 and `submit_cvs`.`job_id` is not null and `status` not in (1, 2)", "type": "query", "params": [], "bindings": [1646, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, {"index": 19, "namespace": "view", "name": "frontend.pages.employer.list-job", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/list-job.blade.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.2192469, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Job.php:64", "source": {"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=64", "ajax": false, "filename": "Job.php", "line": "64"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = ? and `submit_cvs`.`job_id` is not null and `status` not in (?, ?)", "hash": "ad4903d4c7a490850b651a3e8d6bd0b6736fd5dd1388d3a1ac2e1d9789826d8c"}, "start_percent": 84.321, "width_percent": 2.012}, {"sql": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = 1645 and `submit_cvs`.`job_id` is not null and `status` not in (1, 2)", "type": "query", "params": [], "bindings": [1645, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, {"index": 19, "namespace": "view", "name": "frontend.pages.employer.list-job", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/list-job.blade.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.221973, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Job.php:64", "source": {"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=64", "ajax": false, "filename": "Job.php", "line": "64"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = ? and `submit_cvs`.`job_id` is not null and `status` not in (?, ?)", "hash": "46217f95b09c75aca26c3ed83fc8d378383550b78406a5ef329174a1dc6e2db8"}, "start_percent": 86.333, "width_percent": 1.998}, {"sql": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = 483 and `submit_cvs`.`job_id` is not null and `status` not in (1, 2)", "type": "query", "params": [], "bindings": [483, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, {"index": 19, "namespace": "view", "name": "frontend.pages.employer.list-job", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/list-job.blade.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.224694, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Job.php:64", "source": {"index": 18, "namespace": null, "name": "app/Models/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\Job.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=64", "ajax": false, "filename": "Job.php", "line": "64"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = ? and `submit_cvs`.`job_id` is not null and `status` not in (?, ?)", "hash": "0984151e2fc859f3a935901ff68f4874f85c7bc62374378c35cd4407f60a8a58"}, "start_percent": 88.331, "width_percent": 1.97}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.2302198, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 90.302, "width_percent": 0.573}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.231857, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 90.875, "width_percent": 0.545}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.23346, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 91.42, "width_percent": 0.559}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.2350938, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 91.979, "width_percent": 0.573}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.236692, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 92.552, "width_percent": 0.531}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.2382479, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 93.083, "width_percent": 0.559}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.239819, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 93.642, "width_percent": 0.531}, {"sql": "select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = 1723 and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723, "employer_confirmed_at"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, {"index": 19, "namespace": "view", "name": "frontend.layouts.modal.employer_confirm", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.php", "line": 5}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.2430131, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "User.php:197", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=197", "ajax": false, "filename": "User.php", "line": "197"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `meta_data` where `meta_data`.`object_type` = ? and `meta_data`.`object_id` = ? and `meta_data`.`object_id` is not null and `key` = ? limit 1", "hash": "1e919bf895733b158fa816fe17ad059167697afb952d846aaeabf40228f5fa12"}, "start_percent": 94.173, "width_percent": 0.433}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 1723 and `notifications`.`notifiable_id` is not null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.24804, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:98", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=98", "ajax": false, "filename": "header.blade.php", "line": "98"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null order by `created_at` desc", "hash": "2c9622ba988b61fa8c1f7fbcd422804726d734b2ef6b91781d7446b85a6e43d8"}, "start_percent": 94.606, "width_percent": 0.531}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 1723 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.249752, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:99", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=99", "ajax": false, "filename": "header.blade.php", "line": "99"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "hash": "f6ba9bdd77fc193fe60a0e5b92c8d6d03185ee2af0d5baf1deec78db97c629a0"}, "start_percent": 95.137, "width_percent": 0.503}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.25311, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:243", "source": {"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=243", "ajax": false, "filename": "header.blade.php", "line": "243"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 95.64, "width_percent": 0.559}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.2563179, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 96.199, "width_percent": 0.559}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.257943, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 96.758, "width_percent": 0.545}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.259558, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 97.303, "width_percent": 0.531}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.261078, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 97.834, "width_percent": 0.545}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.2626069, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 98.379, "width_percent": 0.545}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.26416, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 98.924, "width_percent": 0.545}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.265692, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 99.469, "width_percent": 0.531}]}, "models": {"data": {"App\\Models\\EmployerType": {"retrieved": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Job": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "App\\Models\\SubmitCv": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSubmitCv.php&line=1", "ajax": false, "filename": "SubmitCv.php", "line": "?"}}, "Illuminate\\Notifications\\DatabaseNotification": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\MetaData": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FMetaData.php&line=1", "ajax": false, "filename": "MetaData.php", "line": "?"}}}, "count": 31, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 30, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer/job", "action_name": "employer-job", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@listJob", "uri": "GET employer/job", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@listJob<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=162\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=162\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:162-215</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "661ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2070557456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2070557456\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-265767570 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-265767570\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1613989720 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://recland.local/employer/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImtuMFBSTzdTYjBsVUdlRkp3cnl3ZXc9PSIsInZhbHVlIjoidjZ1UTc3OHVLaUxOTHBHbi9LaWJSMCtaazdiTjh6WGJIeFNvbjYzYlVLUEEvaU5hQkt6dDN3UVI4SlF6a1V4K0RUZXBxY0wzT1NnRXcvU3FLRCtWYW1LM2tDR3J5NXc4aFBLSVBGb2ZROEpZMnVRYUg5cFZoU1RRYzY1bDJZeXgiLCJtYWMiOiJkOWRjZTYxNTZlNDAxOWY4MTU3ZTMyNWQ4OTIxMThkMTE1ZThhOWNiY2NhNmMyYWMxOGVlMmNhNTZkZTlkMTViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImRDbzlYSmhxQmJzVjZNUWdmOWhuTlE9PSIsInZhbHVlIjoiUm1FYnBCdldESWFxdm1QRW9MK3NOSWhSZkUyY1JvbmpoNWNOWTdNRC8renlEQVNzWGZpZm5RODR1SThtcjJhOUUwcHhwVVVqM0E1UDZCWi9QKzVkZ2NtRzg1NEIvcWgrdzA4aXp6TE1DNVhXWEkyTzhqbHBaUndXdUFCYXNYQVkiLCJtYWMiOiJmODI5MTg4N2M3MDkyOTU0MTA1NDA1NjVhZWZjNDAyYjNhMjZlNDdhZWU1ZTA2MzU2MjJjZWFmYjkwNjUzM2Y5IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962079$j15$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613989720\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-16139465 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16139465\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-854750082 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:28:01 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854750082\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1043224868 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://recland.local/employer/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043224868\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer/job", "action_name": "employer-job", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@listJob"}, "badge": null}}