<?php

namespace App\Services\Admin;


use App\Helpers\Common;
use App\Http\Resources\Admin\JobResource;
use App\Models\JobTop;
use App\Models\SkillMain;
use App\Repositories\JobMetaRepository;
use App\Repositories\JobRepository;
use App\Repositories\JobSeoRepository;
use App\Repositories\SkillRepository;
use App\Services\Admin\Datatable;
use App\Services\Admin\PermissionService;
use App\Services\Admin\SubmitCvService;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;

class JobService
{

    protected $jobRepository;
    protected $jobSeoRepository;
    protected $jobMetaRepository;
    protected $submitCvService;
    protected $skillRepository;

    public function __construct(
        JobRepository     $jobRepository,
        JobSeoRepository  $jobSeoRepository,
        JobMetaRepository $jobMetaRepository,
        SubmitCvService   $submitCvService,
        SkillRepository   $skillRepository
    ) {
        $this->jobRepository = $jobRepository;
        $this->jobSeoRepository = $jobSeoRepository;
        $this->jobMetaRepository = $jobMetaRepository;
        $this->submitCvService = $submitCvService;
        $this->skillRepository = $skillRepository;
    }

    public function total()
    {
        return $this->jobRepository->total();
    }
    public function totalActive()
    {
        return $this->jobRepository->totalActive();
    }

    public function indexService($params, $order = [], $paginate = false)
    {
        return $this->jobRepository->getListJob($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $data = JobResource::collection($data);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total(),
        ];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-orderable' => 'false',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'name',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderNameJob',
                ],
                'value' => 'Tên job',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'company.name',
                    'data-orderable' => 'false',
                ],
                'value' => 'Tên công ty',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'user.email',
                    'data-orderable' => 'false',
                ],
                'value' => 'Email nhà tuyển dụng',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'vacancies',
                    'data-orderable' => 'false',
                ],
                'value' => 'Số lượng tuyển',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'is_active',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderIsActive',
                ],
                'value' => 'Trạng thái hoạt động',
            ],
            // [
            //     'attributes' => [
            //         'data-mdata' => 'status',
            //         'data-orderable' => 'false',
            //         'data-fn' => 'renderJobStatus',
            //     ],
            //     'value' => 'Trạng Thái tuyển dụng',
            // ],
            [
                'attributes' => [
                    'data-mdata' => 'expire_at',
                    'data-orderable' => 'false',
                ],
                'value' => 'Hạn nộp',
            ],
        ];

        $renderAction = [
            'actionEdit',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('job-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('');
    }

    public function detailService($id)
    {
        return $this->jobRepository->find($id);
    }

    public function createService($params)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $remote = isset($params['remote']) ? config('job.remote.on') : config('job.remote.off');
        $urgent = isset($params['urgent']) ? config('job.urgent.on') : config('job.urgent.off');

        $skill_name = '';
        if (in_array($params['career'], config('job.it_career'))) { // IT Career
            $skill_name = SkillMain::where('id', $params['skill'])->first()->name_vi;
        } else {
            $skill_name = JobTop::where('id', $params['skill'])->first()->name_vi;
        }

        $data = [
            'name'                       => $params['name'],
            'slug'                       => Common::buildSlug($params['name']),
            'company_id'                 => $params['company_id'],
            'employer_id'                => $params['employer_id'],
            'expire_at'                  => Carbon::createFromFormat('d/m/Y', $params['expire_at'])->format('Y-m-d'),
            'vacancies'                  => $params['vacancies'],
            'type'                       => $params['type'],
            'urgent'                     => $urgent,
            'remote'                     => $remote,
            'career'                     => implode(',', (array)$params['career']),
            'rank'                       => implode(',', (array)$params['rank']),
            'skills'                     => $skill_name,
            'skill_id'                   => $params['skill'],
            'address'                    => json_encode($params['address']),
            'jd_description'             => $params['jd_description'],
            'jd_request'                 => $params['jd_request'],
            'jd_welfare'                 => $params['jd_welfare'],
            'bonus_type'                 => $params['bonus_type'],
            'salary_min'                 => $params['salary_min'],
            'salary_max'                 => $params['salary_max'],
            'salary_currency'            => $params['salary_currency'],
            'bonus'                      => $params['bonus'],
            // 'incentive'                  => $params['incentive'],
            // 'bonus_currency'             => $params['bonus_currency'],
            // 'bonus_self_apply'           => $params['bonus_self_apply'],
            // 'bonus_self_apply_incentive' => $params['bonus_self_apply_incentive'],
            // 'bonus_self_apply_currency'  => $params['bonus_self_apply_currency'],
            'is_active'                  => $isActive,
            // 'payment_fee'                => $params['payment_fee'],
            'note'                       => $params['note'],
            'job_type'                   => $params['job_type'],
            'level'                      => $params['level'],
        ];

        if ($isActive) $data['publish_at'] = Carbon::now()->format('Y-m-d');

        if (isset($params['file_jd']) && is_file($params['file_jd'])) {
            $data['file_jd'] = FileServiceS3::getInstance()->uploadToS3($params['file_jd'], config('constant.sub_path_s3.job'));
        }

        $job = $this->jobRepository->create($data);
        //create seo
        $dataSeo = [
            'job_id' => $job->id,
            'title_vi' => $params['name'],
            'title_en' => $params['name'],
            'keyword_vi' => implode(',', (array)$params['skill']),
            'keyword_en' => implode(',', (array)$params['skill']),
        ];

        $this->jobSeoRepository->create($dataSeo);
        //create meta
        $dataMeta = [
            'job_id' => $job->id,
            'priority' => config('job.priority.2'),
        ];

        $this->jobMetaRepository->create($dataMeta);

        return $job;
    }

    public function updateService($params, $id)
    {
        $job = $this->detailService($id);
        if (PermissionService::checkPermission('job.active-change-status')) {
            $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        } else {
            $isActive = $job->is_active;
        }
        $remote = isset($params['remote']) ? config('job.remote.on') : config('job.remote.off');
        $urgent = isset($params['urgent']) ? config('job.urgent.on') : config('job.urgent.off');

        $skill_name = '';
        if (in_array($params['career'], config('job.it_career'))) { // IT Career
            $skill_name = SkillMain::where('id', $params['skill'])->first()->name_vi;
        } else {
            $skill_name = JobTop::where('id', $params['skill'])->first()->name_vi;
        }


        $data = [
            'name'                 => $params['name'],
            'slug'                 => $job->name != $params['name'] ? Common::buildSlug($params['name']) : $job->slug,
            'company_id'           => $params['company_id'],
            'employer_id'          => $params['employer_id'],
            'expire_at'            => Carbon::createFromFormat('d/m/Y', $params['expire_at'])->format('Y-m-d'),
            'vacancies'            => $params['vacancies'],
            'type'                 => $params['type'],
            'urgent'               => $urgent,
            'remote'               => $remote,
            'career'               => implode(',', (array)$params['career']),
            'rank'                 => implode(',', (array)$params['rank']),
            'skills'               => $skill_name,
            'skill_id'             => $params['skill'],
            'address'              => json_encode($params['address']),
            'jd_description'       => $params['jd_description'],
            'jd_request'           => $params['jd_request'],
            'jd_welfare'           => $params['jd_welfare'],
            'bonus_type'           => $params['bonus_type'],
            'salary_min'           => $params['salary_min'],
            'salary_max'           => $params['salary_max'],
            'salary_currency'      => $params['salary_currency'],
            'bonus'                => $params['bonus'],
            'manual_bonus_for_ctv' => isset($params['manual_bonus_for_ctv']) ? $params['manual_bonus_for_ctv'] : $job->manual_bonus_for_ctv,
            'coupon_code'          => $params['coupon_code'] ?? null,
            // 'incentive'                  => $params['incentive'],
            // 'bonus_currency'             => $params['bonus_currency'],
            // 'bonus_self_apply'           => $params['bonus_self_apply'],
            // 'bonus_self_apply_incentive' => $params['bonus_self_apply_incentive'],
            // 'bonus_self_apply_currency'  => $params['bonus_self_apply_currency'],
            'is_active'                  => $isActive,
            'flg'                        => 0,
            // 'payment_fee'                => $params['payment_fee'],
            'status'                     => $params['status'],
            'note'                       => $params['note'],
            'job_type'                   => $params['job_type'],
            'level'                      => $params['level'],
        ];
        if ($isActive && !$job->is_active) $data['publish_at'] = Carbon::now()->format('Y-m-d');

        if (isset($params['file_jd']) && is_file($params['file_jd'])) {
            $data['file_jd'] = FileServiceS3::getInstance()->uploadToS3($params['file_jd'], config('constant.sub_path_s3.job'));
        }

        $job = $this->jobRepository->update($id, [], $data);

        return $job;
    }

    public function detailPostMeta($id)
    {
        return $this->jobMetaRepository->findWithJobId($id);
    }

    public function detailPostSeo($id)
    {
        return $this->jobSeoRepository->findWithJobId($id);
    }

    public function updateJobMeta($params, $jobId)
    {
        $jobMeta = $this->jobMetaRepository->findWithJobId($jobId);

        $isShow = isset($params['show_home_page']) ? config('constant.active') : config('constant.inActive');

        $data = [
            'job_id' => $jobId,
            'priority' => $params['priority'],
            'script' => $params['meta_script'],
            'is_show' => $isShow,
        ];

        if (!$jobMeta) {
            $job = $this->jobMetaRepository->create($data);
        } else {
            $job = $this->jobMetaRepository->updateWithJobId($jobId, $data);
        }
        return $job;
    }

    public function updateJobSeo($params, $jobId)
    {
        $jobSeo = $this->jobSeoRepository->findWithJobId($jobId);

        $data = [
            'job_id' => $jobId,
            'title_vi' => $params['seo_title_vi'],
            'title_en' => $params['seo_title_en'],
            'description_vi' => $params['seo_description_vi'],
            'description_en' => $params['seo_description_en'],
            'keyword_vi' => $params['seo_keyword_vi'],
            'keyword_en' => $params['seo_keyword_en'],
        ];

        if (!$jobSeo) {
            if (isset($params['image']) && is_file($params['image'])) {
                $data['image'] = FileServiceS3::getInstance()->uploadToS3($params['image'], config('constant.sub_path_s3.job-seo'));
            }

            $job = $this->jobSeoRepository->create($data);
        } else {
            if (isset($params['image']) && is_file($params['image'])) {
                $data['image'] = FileServiceS3::getInstance()->uploadToS3($params['image'], config('constant.sub_path_s3.job-seo'));
            }

            $job = $this->jobSeoRepository->updateWithJobId($jobId, $data);
        }
        return $job;
    }

    public function refDatatable($params)
    {
        $jobId = $params['job_id'];
        $params = Datatable::convertRequest($params);
        $params['request']['job_id'] = $jobId;
        $data = $this->submitCvService->indexService($params['request'], $params['orders'], true);
        $total = $data->total();
        $data = $data->load([
            'job' => function ($q) {
                $q->select('id', 'name');
            },
            'company' => function ($q) {
                $q->select('id', 'name');
            },
            'employer' => function ($q) {
                $q->select('users.email');
            },
            'rec' => function ($q) {
                $q->select('email', 'name', 'mobile', 'id');
            },
            'submitCvMeta' => function ($q) {
                $q->select('id', 'candidate_name', 'candidate_email', 'candidate_mobile', 'cv_public', 'cv_private', 'submit_cv_id');
            },
        ]);

        $data = $data->append([
            'submit_cv_meta_candidate_name',
            'submit_cv_meta_candidate_email',
            'submit_cv_meta_candidate_mobile',
            'submit_cv_meta_url_cv_public',
            'submit_cv_meta_url_cv_private',
            'job_name',
            'company_name',
            'employer_email',
            'rec_email',
            'rec_name',
            'rec_mobile',
        ]);
        $response = [
            'data' => $data->toArray(),
            'iTotalRecords' => $total,
            'iTotalDisplayRecords' => $total,
        ];
        return $response;
    }

    public function buildRefDatatable($jobId)
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_candidate_name',
                ],
                'value' => 'Tên ứng viên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_candidate_email',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_candidate_mobile',
                ],
                'value' => 'Số điện thoại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'rec_name',
                ],
                'value' => 'Tên CTV',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'rec_email',
                ],
                'value' => 'Email CTV',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'rec_mobile',
                ],
                'value' => 'Số điện thoại CTV',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_url_cv_public',
                    'data-fn' => 'renderCvPublic',
                ],
                'value' => 'CV Public',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_url_cv_private',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPrivate',
                ],
                'value' => 'CV Private',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'job_name',
                ],
                'value' => 'Tên Job',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'company_name',
                ],
                'value' => 'Tên công ty',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'employer_email',
                ],
                'value' => 'Nhà tuyển dụng',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_value',
                    'data-fn' => 'renderStatusCv',
                ],
                'value' => 'Trạng thái phê duyệt',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'expected_date_value',
                ],
                'value' => 'Ngày dự kiến vào làm',
            ],
        ];
        if (PermissionService::checkPermission('submit-cv.edit')) {
            $renderAction[] = 'actionEditSubmitcv';
        } else {
            $renderAction = [];
        }

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('ref-datatable', ['job' => $jobId]))
            ->setAction($renderAction)
            ->setRenderValue($renderValue);
    }

    public function updateJobExpired()
    {
        return $this->jobRepository->updateJobExpired();
    }
    public function getSkill($name = null)
    {
        return $this->skillRepository->getArrName($name);
    }
    public function totalRecruitingJob($fromDate = null, $toDate = null)
    {
        return $this->jobRepository->numberJob($fromDate, $toDate);
    }

    public function jobStatisticalByMonth($fromDate = null, $toDate = null)
    {
        $data = $this->jobRepository->jobStatisticalByMonth($fromDate, $toDate);
        $data = $data->keyBy('month')->toArray();
        $statistical = [];
        for ($i = 1; $i <= 12; $i++) {
            $key = sprintf('%02d', $i);
            $statistical[] = !empty($data[$key]) ? (int)$data[$key]['total'] : '';
        }
        return $statistical;
    }

    public function duplicateService($id)
    {
        $originalJob = $this->detailService($id);

        // Tạo dữ liệu cho job mới từ job gốc
        $duplicateData = [
            'name' => $originalJob->name,
            'slug' => Common::buildSlug($originalJob->name),
            'company_id' => $originalJob->company_id,
            'employer_id' => $originalJob->employer_id,
            'expire_at' => Carbon::now()->addDays(30)->format('Y-m-d'), // Hạn nộp mới là 30 ngày từ hiện tại
            'vacancies' => $originalJob->vacancies,
            'type' => $originalJob->type,
            'urgent' => $originalJob->urgent, // Mặc định không urgent
            'remote' => $originalJob->remote,
            'career' => $originalJob->career,
            'rank' => $originalJob->rank,
            'skills' => $originalJob->skills,
            'skill_id' => $originalJob->skill_id,
            'address' => $originalJob->address,
            'jd_description' => $originalJob->jd_description,
            'jd_request' => $originalJob->jd_request,
            'jd_welfare' => $originalJob->jd_welfare,
            'bonus_type' => $originalJob->bonus_type,
            'salary_min' => $originalJob->salary_min,
            'salary_max' => $originalJob->salary_max,
            'salary_currency' => $originalJob->salary_currency,
            'bonus' => $originalJob->bonus,
            'manual_bonus_for_ctv' => $originalJob->manual_bonus_for_ctv,
            'is_active' => config('constant.inActive'), // Mặc định không active
            'status' => config('constant.status_job.recruiting'), // Mặc định đang tuyển
            'note' => $originalJob->note,
            'job_type' => $originalJob->job_type,
            'file_jd' => $originalJob->file_jd, // Copy file JD
        ];

        // Tạo job mới
        $duplicatedJob = $this->jobRepository->create($duplicateData);

        // Tạo SEO cho job mới
        $originalSeo = $this->detailPostSeo($id);
        if ($originalSeo) {
            $dataSeo = [
                'job_id' => $duplicatedJob->id,
                'title_vi' => $originalSeo->title_vi,
                'title_en' => $originalSeo->title_en,
                'description_vi' => $originalSeo->description_vi,
                'description_en' => $originalSeo->description_en,
                'keyword_vi' => $originalSeo->keyword_vi,
                'keyword_en' => $originalSeo->keyword_en,
                'image' => $originalSeo->image,
            ];
            $this->jobSeoRepository->create($dataSeo);
        }

        // Tạo Meta cho job mới
        $originalMeta = $this->detailPostMeta($id);
        if ($originalMeta) {
            $dataMeta = [
                'job_id' => $duplicatedJob->id,
                'priority' => config('job.priority.2'), // Mặc định priority 2
                'script' => $originalMeta->script,
                'is_show' => config('constant.inActive'), // Mặc định không hiển thị trang chủ
            ];
            $this->jobMetaRepository->create($dataMeta);
        }

        return $duplicatedJob;
    }
}
