<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupon_code', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('Mã khuyến mại');
            $table->integer('min_recruitment_cost')->comment('Chi phí tuyển dụng tối thiểu');
            $table->integer('default_recruitment_cost')->comment('Chi phí tuyển dụng mặc định');
            $table->integer('collaborator_cost')->comment('Chi phí cho cộng tác viên');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupon_code');
    }
};
