{"__meta": {"id": "01K2E0PHWYTVS2XS2MBXPSJPX4", "datetime": "2025-08-12 09:10:41", "utime": **********.695488, "method": "GET", "uri": "/admin/ajax/company/84", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[09:10:41] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '84' limit 1\\n-- \",\n    \"Time:\": 27.03\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.552708, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:41] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `type` = 'employer' and `company_id` = '84'\\n-- \",\n    \"Time:\": 10.42\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.569853, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:41] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\Company.php on line 174", "message_html": null, "is_string": false, "label": "warning", "time": **********.689048, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.040429, "end": **********.695514, "duration": 0.6550848484039307, "duration_str": "655ms", "measures": [{"label": "Booting", "start": **********.040429, "relative_start": 0, "end": **********.467915, "relative_end": **********.467915, "duration": 0.*****************, "duration_str": "427ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.467924, "relative_start": 0.*****************, "end": **********.695516, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.480711, "relative_start": 0.*****************, "end": **********.495533, "relative_end": **********.495533, "duration": 0.014822006225585938, "duration_str": "14.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/ajax/company/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\Admin\\CompanyController@show<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCompanyController.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/ajax", "as": "ajax-company.show", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCompanyController.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CompanyController.php:54-58</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.037450000000000004, "accumulated_duration_str": "37.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `companies` where `companies`.`id` = '84' limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Admin/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\CompanyService.php", "line": 163}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/CompanyController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CompanyController.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5260031, "duration": 0.027030000000000002, "duration_str": "27.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? limit 1", "hash": "851a442e48430f53c00912b3f02d9cafb671114784b6a2d9181a0a61de3ac70b"}, "start_percent": 0, "width_percent": 72.176}, {"sql": "select * from `users` where `type` = 'employer' and `company_id` = '84'", "type": "query", "params": [], "bindings": ["employer", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 177}, {"index": 15, "namespace": null, "name": "app/Services/Admin/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\CompanyService.php", "line": 164}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/CompanyController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\CompanyController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.559505, "duration": 0.01042, "duration_str": "10.42ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:177", "source": {"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=177", "ajax": false, "filename": "UserRepository.php", "line": "177"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `type` = ? and `company_id` = ?", "hash": "430bd173af1a18505dc89f78475f616de7c6cf8e499fa832ccbbb8a105b35a1c"}, "start_percent": 72.176, "width_percent": 27.824}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 5}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/job-comments/1648\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/ajax/company/84", "action_name": "ajax-company.show", "controller_action": "App\\Http\\Controllers\\Admin\\CompanyController@show", "uri": "GET admin/ajax/company/{id}", "controller": "App\\Http\\Controllers\\Admin\\CompanyController@show<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCompanyController.php&line=54\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/ajax", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FCompanyController.php&line=54\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CompanyController.php:54-58</a>", "middleware": "web", "duration": "656ms", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1659807704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1659807704\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1344494884 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/admin/job/1648/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkVMSHpDSWwyWUFud2J6bmZGcm8wcUE9PSIsInZhbHVlIjoiZkpoeW5zOWtqdmMvM0FmcStGbDFuNnl5NVh6S2tNaFVYelpUY0Y4TUxlajgwS21PdktDbHFGeit1S2YraEh1ck9qd2Z0S3htZzk2cVpGSEt4SGQrYU0vRmNoeEY2VkU4WDBIUUpJNHYxbUV6NWpqVThBWkthVVlEQlNzSC9QU2wiLCJtYWMiOiI5YWY4OTEwMjk4Yzg1N2NkYjczY2Q5NTQ1ZjYyOWIyZTBiNWY2ZWEyMjA2ZjQzNzQxYmVjNDdiMmY5M2FjNDViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImxGZnBJSnhaOWttTTEzOWtWdGF6bnc9PSIsInZhbHVlIjoiM0FRd2MrRmp0UWdFTlMyUlRmeGwwa2s4YUpVamhyK2FiaTJwcWk2ZUkyQ0VjY2poV1lWZjRFZlVkWWFvT29Gd2oxNlVFR2UxdnU1alJ1TkJSeFpVNlJrSWVoaytCdEJ4N3BIaVZUczVXQjNQSXpkWEcvQkFHYkVRRGlGN1JCLzMiLCJtYWMiOiIyMzc3MTM1OTBhNTc2YzI3NDNkZmEyZTE2OWMyYzU3ZjEwN2Q2ZGY5ZmFmZWJlNzE2ZWIzMWE3OTE5OWY2OGViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344494884\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2108743025 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6n7BHowcS6we7JFkDGDZRBjg4hsvpTWKVgnVcqpE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108743025\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1968507700 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 02:10:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968507700\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1095566674 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://recland.local/admin/job-comments/1648</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095566674\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/ajax/company/84", "action_name": "ajax-company.show", "controller_action": "App\\Http\\Controllers\\Admin\\CompanyController@show"}, "badge": null}}