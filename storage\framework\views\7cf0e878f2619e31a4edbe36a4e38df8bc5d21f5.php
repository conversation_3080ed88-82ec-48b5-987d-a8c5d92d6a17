<?php
use App\Services\Admin\PermissionService;
?>


<?php $__env->startSection('css_custom'); ?>
<link href="<?php echo e(asset2('backend/assets/plugins/fileupload/css/fileupload.css')); ?>" rel="stylesheet" type="text/css" />

<!-- INTERNAL Fancy File Upload css -->
<link href="<?php echo e(asset2('backend/assets/plugins/fancyuploder/fancy_fileupload.css')); ?>" rel="stylesheet" />

<script>
    const URL_SUBMIT_CV = "<?php echo e(route('submit-cv.edit', ':id')); ?>";
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!--Page header-->
<div class="page-header d-xl-flex d-block">
    <div class="page-leftheader">
        <h4 class="page-title">Chỉnh sửa Job</h4>
    </div>
    <div class="page-rightheader ms-auto">
        <a href="<?php echo e(route('job.duplicate', ['job' => $data->id])); ?>" class="btn btn-warning btn-lg"
            onclick="return confirm('Bạn có chắc chắn muốn nhân bản tin tuyển dụng này?')">
            <i class="las la-copy"></i> Nhân bản
        </a>
    </div>
</div>

<!--End Page header-->
<div class="row">
    <div class="col-xl-12 col-md-12 col-lg-12">
        <div class="card">
            <div class="card-body">
                <div class="panel panel-primary">
                    <div class=" tab-menu-heading p-0 bg-light">
                        <div class="tabs-menu1 ">
                            <!-- Tabs -->
                            <ul class="nav panel-tabs">
                                <li class=""><a href="#info" class="active" data-toggle="tab">Thông tin</a></li>
                                <?php if(PermissionService::checkPermission('job-seo-update')): ?>
                                <li><a href="#seo" data-toggle="tab">SEO</a></li>
                                <?php endif; ?>
                                <?php if(PermissionService::checkPermission('job-meta-update')): ?>
                                <li><a href="#meta" data-toggle="tab">META</a></li>
                                <?php endif; ?>
                                <?php if(\App\Services\Admin\PermissionService::checkPermission('ref-datatable')): ?>
                                <li><a href="#ref" data-toggle="tab">Giới thiệu ứng viên</a></li>
                                <?php endif; ?>
                                <!-- Thêm tab mới -->
                                <li><a href="#qa" data-toggle="tab">Hỏi đáp</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="panel-body tabs-menu-body">
                        <div class="tab-content">
                            <div class="tab-pane active " id="info">
                                <form action="<?php echo e(route('job.update', ['job' => $data->id])); ?>"
                                    enctype="multipart/form-data" method="post" class="sbm_form_s">
                                    <?php echo csrf_field(); ?>
                                    <?php echo e(method_field('put')); ?>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">Tên job <span
                                                        class="text-red">*</span></label>
                                                <input class="form-control <?php if($errors->has('name')): ?> is-invalid <?php endif; ?>"
                                                    name="name" value="<?php echo e(old('name', $data->name)); ?>"
                                                    autocomplete="off">
                                                <?php if($errors->has('name')): ?>
                                                <span class="text-danger"> <?php echo e($errors->first('name')); ?> </span>
                                                <?php endif; ?>
                                            </div>

                                            <div class="form-group">
                                                <input type="hidden" name="hdd_company_id" id="hdd_company_id"
                                                    value="<?php echo e(isset($companies[0]['id']) ? $companies[0]['id'] : ''); ?>" />
                                                <label class="form-label"> Công ty <span
                                                        class="text-red">*</span></label>
                                                <div
                                                    class="<?php if($errors->has('company_id')): ?> is-invalid-select-2 <?php endif; ?>">
                                                    <select id="company_id"
                                                        class="form-control select2-show-search custom-select company-select <?php if($errors->has('company_id')): ?> is-invalid <?php endif; ?>"
                                                        name="company_id" data-placeholder="--- Chọn ---">
                                                        <option label="--- Chọn ---"></option>
                                                        <?php $__currentLoopData = $companies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($company->id); ?>"><?php echo e($company->name); ?>

                                                        </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                                <?php if($errors->has('company_id')): ?>
                                                <span class="text-danger"> <?php echo e($errors->first('company_id')); ?>

                                                </span>
                                                <?php endif; ?>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label">Nhà tuyển dụng <span
                                                        class="text-red">*</span></label>
                                                <div
                                                    class="<?php if($errors->has('employer_id')): ?> is-invalid-select-2 <?php endif; ?>">
                                                    <select id="employer-select"
                                                        class="form-control select2-show-search custom-select  <?php if($errors->has('employer_id')): ?> is-invalid <?php endif; ?>"
                                                        name="employer_id" data-placeholder="--- Chọn ---">
                                                        <option label="--- Chọn ---"></option>
                                                        <?php $__currentLoopData = $employer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($item->id); ?>" <?php echo e($data->employer_id ==
                                                            $item->id ? 'selected' : ''); ?>>
                                                            <?php echo e($item->name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                                <?php if($errors->has('employer_id')): ?>
                                                <span class="text-danger"> <?php echo e($errors->first('employer_id')); ?>

                                                </span>
                                                <?php endif; ?>
                                            </div>

                                            
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                

                                            
                                                
                                                
                                                     
                                                        
                                                        
                                                            
                                                        
                                                        
                                                    
                                                
                                                
                                                
                                                

                                            <div class="form-group">
                                                <label><?php echo e($arrLang['soluongtuyen']); ?> <span>*</span></label>
                                                <input type="text" class="form-control"
                                                    placeholder="<?php echo e($arrLang['input']); ?> <?php echo e(lcfirst($arrLang['soluongtuyen'])); ?>"
                                                    value="<?php echo e($job->vacancies); ?>" name="vacancies">
                                                <?php if($errors->has('vacancies')): ?>
                                                <span class="text-danger"> <?php echo e($errors->first('vacancies')); ?>

                                                </span>
                                                <?php endif; ?>
                                            </div>

                                            <div class="area-address"
                                                style="<?php echo e($data->remote == 1 ? 'display:none' : ''); ?>">
                                                <?php for($i = 0; $i < 3; $i++): ?> <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <?php if($i == 0): ?>
                                                            <label class="form-label"> Địa điểm <span
                                                                    class="text-red">*</span></label>
                                                            <?php endif; ?>
                                                            <div
                                                                class="<?php if($errors->has('address.' . $i . '.area')): ?> is-invalid-select-2 <?php endif; ?>">
                                                                <select name="address[<?php echo e($i); ?>][area]"
                                                                    class="form-control select2-show-search custom-select is-invalid area<?php echo e($i); ?>"
                                                                    data-placeholder="Khu vực">
                                                                    <option value="">Khu vực</option>
                                                                    <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option <?php if(old('address.' . $i . '.area' ,
                                                                        @$data->address_value[$i]->area) == $key): ?>
                                                                        selected <?php endif; ?>
                                                                        value="<?php echo e($key); ?>">
                                                                        <?php echo e($city); ?></option>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </select>
                                                            </div>
                                                            <?php if($errors->has('address.' . $i . '.area')): ?>
                                                            <div class="text-danger">
                                                                <?php echo e($errors->first('address.' . $i . '.area')); ?>

                                                            </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <?php if($i == 0): ?>
                                                        <label class="form-label"> Địa chỉ cụ thể <span
                                                                class="text-red">*</span></label>
                                                        <?php endif; ?>
                                                        <input name="address[<?php echo e($i); ?>][address]"
                                                            class="form-control address<?php echo e($i); ?> <?php if($errors->has('address.' . $i . '.address')): ?> is-invalid <?php endif; ?>"
                                                            value="<?php echo e(old('address.' . $i . '.address', @$data->address_value[$i]->address)); ?>">
                                                        <?php if($errors->has('address.' . $i . '.address')): ?>
                                                        <div class="text-danger">
                                                            <?php echo e($errors->first('address.' . $i . '.address')); ?>

                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                            </div>
                                            <?php endfor; ?>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">JD <span class="text-red">
                                                    <?php if(is_null($data->file_jd)): ?>
                                                    *
                                                    <?php endif; ?>
                                                </span></label>
                                            <input class="" type="file" name="file_jd" autocomplete="off"
                                                <?php if(is_null($data->file_jd)): ?> 'required' <?php endif; ?>>
                                            <?php if(!is_null($data->file_jd)): ?>
                                            <a target="_blank" class="btn btn-link"
                                                href="<?php echo e(gen_url_file_s3($data->file_jd)); ?>">Show</a>
                                            <?php endif; ?>
                                            <?php if($errors->has('file_jd')): ?>
                                            <span class="text-danger"> <?php echo e($errors->first('file_jd')); ?> </span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Trạng thái công việc <span
                                                    class="text-red">*</span></label>
                                            <select class="form-control <?php if($errors->has('status')): ?> is-invalid <?php endif; ?>"
                                                name="status">
                                                <option value="">--- Chọn ---</option>
                                                <?php $__currentLoopData = config('constant.status_job'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($k); ?>" <?php if(old('status', $data->status) == $k): ?>
                                                    selected <?php endif; ?>>
                                                    <?php echo e($v); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Hạn nộp <span class="text-red">*</span></label>
                                            <input
                                                class="form-control fc-datepicker <?php if($errors->has('expire_at')): ?> is-invalid <?php endif; ?>"
                                                name="expire_at" placeholder="DD/MM/YYYY"
                                                value="<?php echo e(old('expire_at', $data->expire_at_value)); ?>" type="text"
                                                autocomplete="off">
                                            <?php if($errors->has('expire_at')): ?>
                                            <span class="text-danger"> <?php echo e($errors->first('expire_at')); ?>

                                            </span>
                                            <?php endif; ?>
                                        </div>



                                        
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                

                                        <div class="form-group">
                                            <label><?php echo e($arrLang['linhvuc']); ?> <span>*</span></label>
                                            <div class="multiple-select2">
                                                <select id="career-select" style="width:100%;display: none"
                                                    class="select2-custom select-max-item" name="career">
                                                    <?php $__currentLoopData = $career; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($k); ?>" <?php echo e($k==$job->career ? 'selected' : ''); ?>>
                                                        <?php echo e($item); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <?php if($errors->has('career')): ?>
                                            <span class="text-danger"> <?php echo e($errors->first('career')); ?> </span>
                                            <?php endif; ?>
                                        </div>




                                        
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                

                                        <div class="form-group">
                                            <label><?php echo e($arrLang['kinang']); ?> <span>*</span></label>
                                            <div class="multiple-select2">
                                                <select id="skill_id" style="width:100%;display: none"
                                                    class="select2-custom-tags select-max-item" name="skill">
                                                    <option label="--- Chọn ---"></option>
                                                    <?php $__currentLoopData = $skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($k); ?>" <?php echo e($k==$job->skill_id ? 'selected' : ''); ?>>
                                                        <?php echo e($item); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <?php if($errors->has('skill')): ?>
                                            <span class="text-danger"> <?php echo e($errors->first('skill')); ?> </span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="form-group">
                                            
                                            <label><?php echo e($arrLang['capbac']); ?> <span>*</span></label>
                                            <div class="multiple-select2">
                                                <select id="rank-select" style="width:100%;display: none"
                                                    class="select2-custom select-max-item" name="rank">
                                                    
                                                    <?php $__currentLoopData = $levels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($item->id); ?>" <?php echo e($item->id == $job->rank ?
                                                        'selected' : ''); ?>>
                                                        <?php echo e($item->name_vi); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <?php if($errors->has('rank')): ?>
                                            <span class="text-danger"> <?php echo e($errors->first('rank')); ?> </span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-9">
                                                <div class="form-group">
                                                    <label class="form-label">Hình thức <span
                                                            class="text-red">*</span></label>
                                                    <div class="<?php if($errors->has('type')): ?> is-invalid-select-2 <?php endif; ?>">
                                                        <select
                                                            class="form-control select2-show-search custom-select  <?php if($errors->has('type')): ?> is-invalid <?php endif; ?>"
                                                            name="type" data-placeholder="--- Chọn ---">
                                                            <option label="--- Chọn ---"></option>
                                                            <?php $__currentLoopData = $type; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($k); ?>" <?php echo e(old('type', $data->type) == $k ?
                                                                'selected' : ''); ?>>
                                                                <?php echo e($item); ?></option>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </select>
                                                    </div>
                                                    <?php if($errors->has('type')): ?>
                                                    <span class="text-danger"> <?php echo e($errors->first('type')); ?>

                                                    </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Remote <span
                                                            class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        <input type="checkbox" <?php if(old('remote', $data->remote) == 1): ?>
                                                        checked <?php endif; ?>
                                                        value="1" name="remote"
                                                        class="custom-switch-input remote">
                                                        <span
                                                            class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="col-md-9">
                                                <div class="form-group">
                                                    <label class="form-label">Loại Job <span
                                                            class="text-red">*</span></label>
                                                    <div
                                                        class="<?php if($errors->has('job_type')): ?> is-invalid-select-2 <?php endif; ?>">
                                                        <select
                                                            class="form-control select2-show-search custom-select  <?php if($errors->has('job_type')): ?> is-invalid <?php endif; ?>"
                                                            name="job_type" data-placeholder="--- Chọn ---">
                                                            <option label="--- Chọn ---"></option>
                                                            <?php $__currentLoopData = $jobType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($k); ?>" <?php echo e(old('job_type', $data->job_type)
                                                                == $k ? 'selected' : ''); ?>>
                                                                <?php echo e($item); ?></option>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </select>
                                                    </div>
                                                    <?php if($errors->has('job_type')): ?>
                                                    <span class="text-danger">
                                                        <?php echo e($errors->first('job_type')); ?> </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                        </div>

                                        <div class="row">
                                            <?php if(PermissionService::checkPermission('job.active-change-status')): ?>
                                            <div class="col-md-9">
                                                <div class="form-group">
                                                    <label class="form-label">Trạng thái hoạt động <span
                                                            class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        <?php if(old('flg_status') == 1): ?>
                                                        <input type="checkbox" <?php if(old('is_active')==1): ?> checked <?php endif; ?>
                                                            value="1" name="is_active" class="custom-switch-input">
                                                        <?php else: ?>
                                                        <input type="checkbox" <?php if($data->is_active == 1): ?> checked
                                                        <?php endif; ?>
                                                        value="1" name="is_active"
                                                        class="custom-switch-input">
                                                        <?php endif; ?>
                                                        <span
                                                            class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                        <span class="custom-switch-description mr-2"
                                                            id="status_active">Active</span>
                                                    </label>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Urgent <span
                                                            class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        <input type="checkbox" <?php if(old('urgent', $data->urgent) == 1): ?>
                                                        checked <?php endif; ?>
                                                        value="1" name="urgent"
                                                        class="custom-switch-input">
                                                        <span
                                                            class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            </div>

                            <div class="row">
                                <div class="col-md-9">
                                    <div class="form-group">
                                        <label class="form-label">JD_Mô tả công việc <span
                                                class="text-red">*</span></label>
                                        <textarea name="jd_description" id="jd_description" rows="10"
                                            cols="80"><?php echo e(old('jd_description', $data->jd_description)); ?></textarea>
                                        <?php if($errors->has('jd_description')): ?>
                                        <span class="text-danger"> <?php echo e($errors->first('jd_description')); ?>

                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-3"></div>
                            </div>

                            <div class="row">
                                <div class="col-md-9">
                                    <div class="form-group">
                                        <label class="form-label">JD_Yêu cầu công việc <span
                                                class="text-red">*</span></label>
                                        <textarea name="jd_request" id="jd_request" rows="10"
                                            cols="80"><?php echo e(old('jd_request', $data->jd_request)); ?></textarea>
                                        <?php if($errors->has('jd_request')): ?>
                                        <span class="text-danger"> <?php echo e($errors->first('jd_request')); ?>

                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-3"></div>
                            </div>

                            <div class="row">
                                <div class="col-md-9">
                                    <div class="form-group">
                                        <label class="form-label">JD_Phúc lợi <span class="text-red">*</span></label>
                                        <textarea name="jd_welfare" id="jd_welfare" rows="10"
                                            cols="80"><?php echo e(old('jd_welfare', $data->jd_welfare)); ?></textarea>
                                        <?php if($errors->has('jd_welfare')): ?>
                                        <span class="text-danger"> <?php echo e($errors->first('jd_welfare')); ?>

                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-3"></div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e($arrLang['salarymin']); ?> <span>*</span> </label>
                                        <div class="input-unit">
                                            <input data-toggle="current_mask" data-target="#field_salary_min"
                                                type="text" class="field-input-unit form-control"
                                                value="<?php echo e($job->salary_min); ?>"
                                                placeholder="<?php echo e($arrLang['input']); ?> <?php echo e(lcfirst($arrLang['salarymin'])); ?>">
                                            <input id="field_salary_min" class="field-input-unit" type="hidden" value=""
                                                name="salary_min">
                                        </div>
                                        <?php if($errors->has('salary_min')): ?>
                                        <span class="text-danger"> <?php echo e($errors->first('salary_min')); ?>

                                        </span>
                                        <?php endif; ?>

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e($arrLang['salarymax']); ?> <span>*</span> </label>
                                        <div class="input-unit">
                                            <input data-toggle="current_mask" data-target="#field_salary_max"
                                                type="text" class="field-input-unit form-control"
                                                value="<?php echo e($job->salary_max); ?>"
                                                placeholder="<?php echo e($arrLang['input']); ?> <?php echo e(lcfirst($arrLang['salarymax'])); ?>">
                                            <input id="field_salary_max" class="field-input-unit" type="hidden" value=""
                                                name="salary_max">
                                        </div>
                                        <?php if($errors->has('salary_max')): ?>
                                        <span class="text-danger"> <?php echo e($errors->first('salary_max')); ?>

                                        </span>
                                        <?php endif; ?>

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Salary Currency <span class="text-red">*</span></label>
                                    <div class="<?php if($errors->has('salary_currency')): ?> is-invalid-select-2 <?php endif; ?>">
                                        <select
                                            class="form-control select2-show-search custom-select  <?php if($errors->has('salary_currency')): ?> is-invalid <?php endif; ?>"
                                            name="salary_currency" data-placeholder="--- Chọn ---">
                                            <option label="--- Chọn ---"></option>
                                            <?php $__currentLoopData = $currency; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item); ?>" <?php echo e(old('salary_currency', $data->
                                                salary_currency) == $item ? 'selected' : ''); ?>>
                                                <?php echo e($item); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <?php if($errors->has('salary_currency')): ?>
                                    <span class="text-danger"> <?php echo e($errors->first('salary_currency')); ?>

                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e($arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng'); ?>

                                            <span>*</span></label>
                                        <select id="recruitment-type" style="width:100%;display: none" class=""
                                            name="bonus_type">
                                            
                                            <option value="">
                                                <?php echo e(config('settings.' . app()->getLocale() .
                                                '.rec_cv_selling.chonhinhthuc')); ?>

                                            </option>
                                            <?php $__currentLoopData = $bonusType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($item); ?>" <?php echo e($item==$job->bonus_type ? 'selected' : ''); ?>>
                                                <?php echo e($item); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php if($errors->has('bonus_type')): ?>
                                        <span class="text-danger"> <?php echo e($errors->first('bonus_type')); ?>

                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label><?php echo e($arrLang['chiphituyendung'] ?? 'Chi phí cho 1 lần tuyển dụng'); ?>

                                            <span>*</span></label>
                                        <input type="number" id="bonus" name="bonus" class="form-control"
                                            value="<?php echo e($job->bonus); ?>">
                                        <label style="font-weight: normal; font-size: 14px;" id="bonus_msg"
                                            class="form-text text-muted mt-2 d-block">Giá tối thiểu là <span
                                                id="min_price"></span> vnđ</label>
                                        <?php if($errors->has('bonus')): ?>
                                        <span class="text-danger"> <?php echo e($errors->first('bonus')); ?> </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-item">
                                        <label class="form-label"><?php echo e($arrLang['tongtien'] ?? 'Tổng chi phí'); ?></label>
                                        <input type="text" id="total-cost" readonly class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-item">
                                        <label class="form-label">Phí trả cho CTV</label>
                                        <div class="input-group">
                                            <input type="text" id="bonus_for_ctv" readonly class="form-control"
                                                value="<?php echo e($job->bonus_for_ctv); ?>">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary calculate-bonus" type="button">
                                                    <i class="fas fa-calculator"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-item">
                                        <label class="form-label">Phí trả cho CTV (Tự nhập)</label>
                                        <input type="text" id="manual_bonus_for_ctv" name="manual_bonus_for_ctv"
                                            class="form-control" value="<?php echo e($job->manual_bonus_for_ctv); ?>" <?php echo e(PermissionService::checkPermission('job.custom-price') ? '' : 'disabled'); ?>>
                                    </div>
                                </div>
                            </div>

                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Mã khuyến mại</label>
                                        <input type="text" name="coupon_code"
                                            class="form-control <?php if($errors->has('coupon_code')): ?> is-invalid <?php endif; ?>"
                                            value="<?php echo e(old('coupon_code', $data->coupon_code)); ?>"
                                            placeholder="Nhập mã khuyến mại (tùy chọn)">
                                        <?php if($errors->has('coupon_code')): ?>
                                        <span class="text-danger"><?php echo e($errors->first('coupon_code')); ?></span>
                                        <?php endif; ?>
                                        <small class="form-text text-muted">
                                            Mã khuyến mại được áp dụng cho tin tuyển dụng này
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                
                                
                                
                                

                            
                                
                                
                                
                            </div>

                            <div class="form-group">
                                <label class="form-label">Level Job <span class="text-red">*</span></label>
                                <div class="<?php if($errors->has('level')): ?> is-invalid-select-2 <?php endif; ?>">
                                    <select
                                        class="form-control select2-show-search custom-select <?php if($errors->has('level')): ?> is-invalid <?php endif; ?>"
                                        name="level" data-placeholder="--- Chọn ---">
                                        <option label="--- Chọn ---"></option>
                                        <?php $__currentLoopData = $level; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($k); ?>" <?php echo e(old('level', $data->level ?? 0) == $k ? 'selected' :
                                            ''); ?>><?php echo e($item); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <?php if($errors->has('level')): ?>
                                <span class="text-danger"> <?php echo e($errors->first('level')); ?> </span>
                                <?php endif; ?>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Note</label>
                                <textarea rows="5" cols="70" name="note"><?php echo e($data->note); ?></textarea>
                                <?php if($errors->has('note')): ?>
                                <span class="text-danger"> <?php echo e($errors->first('note')); ?> </span>
                                <?php endif; ?>
                            </div>

                            <!-- Hướng dẫn Level Job -->
                            <div class="alert alert-info">
                                <h5><i class="las la-info-circle"></i> Hướng dẫn Level Job</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Level 0 - Thường:</strong>
                                        <ul class="mb-0">
                                            <li>Job tuyển dụng thông thường</li>
                                            <li>Dành cho tất cả CTV</li>
                                            <li>Không yêu cầu đặc biệt</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Level 1 - Cao cấp:</strong>
                                        <ul class="mb-0">
                                            <li>Job tuyển dụng cao cấp</li>
                                            <li>Chỉ dành cho CTV có level = 1</li>
                                            <li>Có yêu cầu số lượng submit CV cao</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer text-right">
                                <input type="hidden" class="flg_status" name="flg_status"
                                    value="<?php echo e(old('flg_status')); ?>" />
                                <a href="<?php echo e(backpack_url('jobs-management')); ?>" class="btn btn-danger btn-lg">Close</a>
                                <button class="btn btn-success btn-lg sbm_form" type="button">Submit</button>
                            </div>


                            </form>
                        </div>
                        <?php if(\App\Services\Admin\PermissionService::checkPermission('job-seo-update')): ?>
                        <div class="tab-pane " id="seo">
                            <form action="<?php echo e(route('job-seo-update', ['job' => $data->id])); ?>"
                                enctype="multipart/form-data" method="post">
                                <?php echo csrf_field(); ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Title VN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control <?php if($errors->has('seo_title_vi')): ?> is-invalid <?php endif; ?>"
                                                name="seo_title_vi"
                                                value="<?php echo e(old('seo_title_vi', $dataSeo ? $dataSeo->title_vi : '')); ?>"
                                                autocomplete="off">
                                            <?php if($errors->has('seo_title_vi')): ?>
                                            <span class="text-danger">
                                                <?php echo e($errors->first('seo_title_vi')); ?> </span>
                                            <?php endif; ?>

                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Description VN <span
                                                    class="text-red">*</span></label>
                                            <input
                                                class="form-control <?php if($errors->has('seo_description_vi')): ?> is-invalid <?php endif; ?>"
                                                name="seo_description_vi"
                                                value="<?php echo e(old('seo_description_vi', $dataSeo ? $dataSeo->description_vi : '')); ?>"
                                                autocomplete="off">
                                            <?php if($errors->has('seo_description_vi')): ?>
                                            <span class="text-danger">
                                                <?php echo e($errors->first('seo_description_vi')); ?> </span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Keyword VN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control <?php if($errors->has('seo_keyword_vi')): ?> is-invalid <?php endif; ?>"
                                                name="seo_keyword_vi"
                                                value="<?php echo e(old('seo_keyword_vi', $dataSeo ? $dataSeo->keyword_vi : '')); ?>"
                                                autocomplete="off">
                                            <?php if($errors->has('seo_keyword_vi')): ?>
                                            <span class="text-danger">
                                                <?php echo e($errors->first('seo_keyword_vi')); ?> </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Title EN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control <?php if($errors->has('seo_title_en')): ?> is-invalid <?php endif; ?>"
                                                name="seo_title_en"
                                                value="<?php echo e(old('seo_title_en', $dataSeo ? $dataSeo->title_en : '')); ?>"
                                                autocomplete="off">
                                            <?php if($errors->has('seo_title_en')): ?>
                                            <span class="text-danger">
                                                <?php echo e($errors->first('seo_title_en')); ?> </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Description EN <span
                                                    class="text-red">*</span></label>
                                            <input
                                                class="form-control <?php if($errors->has('seo_description_en')): ?> is-invalid <?php endif; ?>"
                                                name="seo_description_en"
                                                value="<?php echo e(old('seo_description_en', $dataSeo ? $dataSeo->description_en : '')); ?>"
                                                autocomplete="off">
                                            <?php if($errors->has('seo_description_en')): ?>
                                            <span class="text-danger">
                                                <?php echo e($errors->first('seo_description_en')); ?> </span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Keyword EN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control <?php if($errors->has('seo_keyword_en')): ?> is-invalid <?php endif; ?>"
                                                name="seo_keyword_en"
                                                value="<?php echo e(old('seo_keyword_en', $dataSeo ? $dataSeo->keyword_en : '')); ?>"
                                                autocomplete="off">
                                            <?php if($errors->has('seo_keyword_en')): ?>
                                            <span class="text-danger">
                                                <?php echo e($errors->first('seo_keyword_en')); ?> </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="form-label">Image share</label>
                                        <input type="file" class="dropify" name="image"
                                            data-default-file="<?php echo e(isset($dataSeo->image) ? gen_url_file_s3($dataSeo->image, '', false) : ''); ?>"
                                            data-height="180" />
                                    </div>
                                </div>

                                <div class="card-footer text-right">
                                    <a href="<?php echo e(route('job.index')); ?>" class="btn btn-danger btn-lg">Close</a>
                                    <button class="btn btn-success btn-lg">Submit</button>
                                </div>
                            </form>
                        </div>
                        <?php endif; ?>
                        <?php if(\App\Services\Admin\PermissionService::checkPermission('job-meta-update')): ?>
                        <div class="tab-pane " id="meta">
                            <form action="<?php echo e(route('job-meta-update', ['job' => $data->id])); ?>"
                                enctype="multipart/form-data" method="post">
                                <?php echo csrf_field(); ?>
                                <div class="form-group">
                                    <label class="form-label">Độ ưu tiên <span class="text-red">*</span></label>
                                    <select class="form-control <?php if($errors->has('priority')): ?> is-invalid <?php endif; ?>"
                                        name="priority" data-placeholder="--- Chọn ---">
                                        <option label="--- Chọn ---"></option>
                                        <?php $__currentLoopData = $priority; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($v); ?>" <?php echo e(old('priority', $dataMeta->priority) == $v ?
                                            'selected' : ''); ?>>
                                            <?php echo e($v); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php if($errors->has('priority')): ?>
                                    <span class="text-danger"> <?php echo e($errors->first('priority')); ?> </span>
                                    <?php endif; ?>
                                </div>
                                <div class="form-group">
                                    <label class="form-label"> Script JS </label>
                                    <textarea class="form-control <?php if($errors->has('meta_script')): ?> is-invalid <?php endif; ?>"
                                        name="meta_script"
                                        autocomplete="off"><?php echo e(old('meta_script', $dataMeta->script)); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Hiển thị trên trang chủ <span
                                            class="text-red">*</span></label>
                                    <label class="custom-switch">
                                        <input type="checkbox" <?php if(old('show_home_page', $dataMeta->is_show) == 1): ?>
                                        checked <?php endif; ?> value="1"
                                        name="show_home_page" class="custom-switch-input remote">
                                        <span class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                    </label>
                                </div>

                                <div class="card-footer text-right">
                                    <a href="<?php echo e(route('job.index')); ?>" class="btn btn-danger btn-lg">Close</a>
                                    <button class="btn btn-success btn-lg">Submit</button>
                                </div>
                            </form>
                        </div>
                        <?php endif; ?>
                        <?php if(\App\Services\Admin\PermissionService::checkPermission('ref-datatable')): ?>
                        <div class="tab-pane" id="ref">
                            <div class="row">
                                <div class="col-xl-12 col-md-12 col-lg-12">
                                    <?php echo e($datatable->render()); ?>

                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <!-- Thêm nội dung tab hỏi đáp -->
                        <div class="tab-pane" id="qa">
                            <div class="row">
                                <div class="col-xl-12 col-md-12 col-lg-12">
                                    <div id="job-comments">
                                        <job-comments :job-id="<?php echo e($data->id); ?>"></job-comments>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </form>

</div>
</div>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset2('frontend/asset/js/additional-methods.min.js')); ?>"></script>
<script src="<?php echo e(asset2('frontend/asset/ckeditor/ckeditor.js')); ?>"></script>
<!-- INTERNAL File uploads js -->
<script src="<?php echo e(asset2('backend/assets/plugins/fileupload/js/dropify.js')); ?>"></script>
<script src="<?php echo e(asset2('backend/assets/js/filupload.js')); ?>"></script>
<script src="<?php echo e(asset2('js/admin/box-job-comments.js')); ?>"></script>
<script>
    $(document).ready(function() {
            CKEDITOR.replace('jd_description', {
                filebrowserImageBrowseUrl: '/file-manager/ckeditor'
            });
            CKEDITOR.replace('jd_request', {
                filebrowserImageBrowseUrl: '/file-manager/ckeditor'
            });
            CKEDITOR.replace('jd_welfare', {
                filebrowserImageBrowseUrl: '/file-manager/ckeditor'
            });

            $('[name="is_active"]').prop("checked") ? $('#status_active').html('Active') : $('#status_active').html(
                'Inactive');
            $('[name="is_active"]').change(function() {
                if ($(this).prop("checked")) {
                    $('#status_active').html('Active');
                } else {
                    $('#status_active').html('Inactive');
                }
            });

            var dateToday = new Date();
            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy',
                minDate: dateToday,
            })

            $(document).on('click', '.ti-import', function() {
                let url = $(this).data('value');
                let route = '<?php echo e(route('download-file')); ?>' + '?url=' + url;

                window.open(route, '_blank');
            });

            $(document).on('click', '.fa-eye', function() {
                let url = $(this).data('value');
                window.open(url, '_blank');
            });

            $(document).on('click', '.sbm_form', function() {
                $('.flg_status').val('1');
                // kieerm tồn tại permission
                <?php
                    if (!PermissionService::checkPermission('job.custom-price')) {
                ?>
                if ($('#bonus').val() < min_price) {
                    alert('Giá hoa hồng không được nhỏ hơn giá tối thiểu');
                    return;
                }
                <?php
                    }
                ?>
                $('.sbm_form_s').get(0).submit();
            });

            $(".js-example-tokenizer").select2({
                tags: true,
            })

            $(".remote").click(function() {
                let value = $("[name='remote']:checked").val();
                if (value) {
                    $('.area-address').css('display', 'none');
                } else {
                    $('.area-address').css('display', 'block');
                }
            })

            var clicks = 0;
            $('.company-select').change(function() {
                let id = $(this).val();
                let oldId = '<?php echo e($data->company_id); ?>'
                // if (id != oldId) {
                let url = '<?php echo e(route('ajax-company.show', ':id')); ?>';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'get',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        let companyObj = res[0];
                        let listEmployer = res[1];
                        let arrAddress = JSON.parse(companyObj.address);
                        if (clicks == 0) {
                            // first click
                        } else {
                            // second click
                            // for (let i = 0; i < 3; i++) {
                            //     if (arrAddress[i]) {
                            //         $('.area' + i).val(arrAddress[i].area).trigger('change');
                            //         $('.address' + i).val(arrAddress[i].address);
                            //     } else {
                            //         $('.area' + i).val('').trigger('change');
                            //         $('.address' + i).val('');
                            //     }
                            // }
                        }
                        ++clicks;

                        var output = [];
                        var oldEmployer = '<?php echo json_encode($employer); ?>'
                        var employer = JSON.parse(oldEmployer);
                        $.each(listEmployer, function(key, value) {
                            if (value.id == employer[0].id) {
                                output.push('<option value="' + value.id +
                                    '" selected>' + value.name + '</option>');
                            } else {
                                output.push('<option value="' + value.id + '">' + value
                                    .name + '</option>');
                            }
                        });
                        $('#employer-select').html(output.join(''));
                    }
                });
                // }
            });

            //chang skill
            let url = '<?php echo e(route('ajax-job.list-skill')); ?>';
            $("#skill_id").select2({
                tags: true,
                ajax: {
                    url: url,
                    type: "post",
                    dataType: 'json',
                    delay: 550,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });

            //chang company
            let urlCompany = '<?php echo e(route('ajax-employer.change-company')); ?>';
            $("#company_id").select2({
                ajax: {
                    url: urlCompany,
                    type: "post",
                    dataType: 'json',
                    delay: 250,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });
            $("#company_id").val($('#hdd_company_id').val()).trigger('change');
        })

        $(document).ready(function() {

            // function getSkillIT(career = "") {
            //     $('.skill-main').select2({
            //         dropdownParent: "#modal-sell-cv",
            //         // tags: true,
            //         placeholder: '<?php echo e(config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang')); ?>',
            //         allowClear: true,
            //         ajax: {
            //             url: '<?php echo e(route('ajax-get-skill-main-it')); ?>' + '?career=' + career,
            //             type: "get",
            //             dataType: 'json',
            //             headers: {
            //                 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //             },
            //             data: function(params) {
            //                 return {
            //                     searchTerm: params.term // search term
            //                 };
            //             },
            //             processResults: function(response) {
            //                 return {
            //                     results: $.map(response, function(item) {
            //                         return {
            //                             ...item,
            //                             text: item.name_en
            //                         }
            //                     })
            //                 };
            //             },
            //         },
            //     })
            // }

            function getMinPrice() {
                $('#bonus').prop('disabled', true);
                // if ($('#skill_id').select2('data')[0]) {
                // let group = $('#skill_id').select2('data')[0].group;
                // let isIT = $('#skill_id').select2('data')[0].is_it;
                // }
                if (typeof group == 'undefined' || !group) group = '';
                if (typeof isIT == 'undefined' || !isIT) isIT = '';

                console.log('group: ' + group, 'isIT: ' + isIT);

                let level = $('#rank-select').val();
                let skill_id = $('#skill_id').val();
                let bonus_type = $('#recruitment-type').val();
                let career = $('#career-select').val();
                let salary_min = $('#field_salary_min').val();
                let salary_max = $('#field_salary_max').val();
                let salary_currency = $('.salary_currency').val();
                console.log('level: ' + level);

                if (bonus_type == 'onboard' && salary_min == '') {
                    $('#bonus_msg').html(
                        'Vui lòng nhập lương tối thiểu & hình thức tuyển dụng để tính chi phí tối thiểu');
                    return;
                }
                if (!level || !bonus_type || !skill_id) {
                    $('#bonus_msg').html('Vui lòng chọn cấp bậc, hình thức tuyển dụng và kỹ năng chính để xem giá');
                    return;
                }


                console.log('level: ' + level, 'bonus_type: ' + bonus_type, 'career: ' + career, 'isIT: ' + isIT,
                    'group: ' + group);
                $.ajax({
                    url: '<?php echo e(route('ajax-get-min-submit-price')); ?>' + '?group=' + group + '&is_it=' +
                        isIT + '&bonus_type=' + bonus_type + '&level=' + level + '&career=' + career +
                        '&salary_min=' + salary_min + '&salary_max=' + salary_max + '&salary_currency=' + salary_currency + '&skill_id=' +
                        skill_id,
                    type: "get",
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#bonus').prop('disabled', false);
                        min_price = res.data.min_price;
                        $('#min_price').html(formatCurrency(min_price));
                        $('#bonus_msg').html('Giá tối thiểu là ' + formatCurrency(min_price) + ' vnđ');
                        // if (res) {
                        //     if (type == 'cv') {
                        //         $('.min-price').html(formatCurrency(res.price_cv_min))
                        //         $('.max-price').html(formatCurrency(res.price_cv_max))
                        //         $("input[name='min-price']").val(res.price_cv_min);
                        //         $("input[name='max-price']").val(res.price_cv_max);
                        //     } else if (type == 'interview') {
                        //         $('.min-price').html(formatCurrency(res.price_interview_min))
                        //         $('.max-price').html(formatCurrency(res.price_interview_max))
                        //         $("input[name='min-price']").val(res.price_interview_min);
                        //         $("input[name='max-price']").val(res.price_interview_max);
                        //     }
                        // }
                    }
                })
            }

            function getSkillIT(career = "") {
                let url = '<?php echo e(route('ajax-get-skill-main-it')); ?>?career=' + career;
                $("#skill_id").select2({
                    tags: true,
                    placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['kinang'])); ?>',
                    allowClear: true,
                    maximumSelectionLength: 3,
                    ajax: {
                        url: url,
                        type: "get",
                        dataType: 'json',
                        delay: 550,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                        cache: false
                    }
                });
                // $('#skill_id').select2({
                //     // dropdownParent: "#modal-sell-cv",
                //     // tags: true,
                //     placeholder: '<?php echo e(config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang')); ?>',
                //     allowClear: true,
                //     ajax: {
                //         url: '<?php echo e(route('ajax-get-skill-main-it')); ?>' + '?career=' + career,
                //         type: "get",
                //         dataType: 'json',
                //         headers: {
                //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                //         },
                //         data: function (params) {
                //             return {
                //                 searchTerm: params.term // search term
                //             };
                //         },
                //         processResults: function (response) {
                //             return {
                //                 results: $.map(response, function (item) {
                //                     return {
                //                         ...item,
                //                         text: item.name_en
                //                     }
                //                 })
                //             };
                //         },
                //     },
                // })
            }

            function loadInitData() {
                // load du lieu vao skill va cap bac
                let career = $('#career-select').val();
                let url = '<?php echo e(route('ajax-get-skill-main-it')); ?>?career=' + career;
                console.log(url);

                $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        let skillSelect = $('#skill_id');
                        skillSelect.empty(); // Xóa tất cả options hiện tại

                        // Thêm option mặc định
                        skillSelect.append($('<option>', {
                            value: '',
                            text: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['kinang'])); ?>'
                        }));

                        // Thêm các options từ dữ liệu trả về
                        $.each(response, function(index, item) {
                            skillSelect.append($('<option>', {
                                value: item.id,
                                text: item.name_en
                            }));
                        });
                        getSkillIT(career);
                        $('#skill_id').val(<?php echo e($job->skill_id); ?>).trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error("Lỗi khi tải dữ liệu kỹ năng:", error);
                    }
                });


                $.ajax({
                    url: '<?php echo e(route('ajax-get-level-by-career')); ?>' +
                        '?career_id=<?php echo e($job->career); ?>&skill_id=<?php echo e($job->skill_id); ?>',
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        let rankSelect = $('#rank-select');
                        rankSelect.empty(); // Xóa tất cả options hiện tại

                        // Thêm option mặc định
                        rankSelect.append($('<option>', {
                            value: '',
                            text: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['kinang'])); ?>'
                        }));

                        // Thêm các options từ dữ liệu trả về
                        $.each(response, function(index, item) {
                            rankSelect.append($('<option>', {
                                value: item.id,
                                text: item.name_vi
                            }));
                        });
                        getLevel({
                            group: '<?php echo e($skill && $skill->group ? $skill->group : 0); ?>',
                            isIT: '<?php echo e($isIt ? 'true' : 'false'); ?>'
                        });
                        $('#rank-select').val(<?php echo e($job->rank); ?>).trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error("Lỗi khi tải dữ liệu kỹ năng:", error);
                    }
                });
                // $.each(skill, function(key, value) {
                //     output.push('<option value="' + value.id + '">' + value.name + '</option>');
                // });
                $('#career-select').val(<?php echo e($job->career); ?>).trigger('change');
                $('#recruitment-type').val('<?php echo e($job->bonus_type); ?>').trigger('change');
            }
            // loadInitData();

            $('#career-select').change(function() {
                let career = $(this).val();
                $("#skill_id").empty();
                getSkillIT(career);
            });

            $('#rank-select').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#recruitment-type').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('[data-target="#field_salary_max"]').blur(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#skill_id').change(function(e) {
                if ($(this).val()) {
                    $('#rank-select').val(null).trigger('change');
                    $('#recruitment-type').val(null).trigger('change');
                    let group = $(this).select2('data')[0].group;
                    let isIT = $(this).select2('data')[0].is_it;
                    getLevel({
                        group,
                        isIT
                    });
                }
            });
            getMinPrice();
            calculateTotalCost();

            function getLevel({
                group,
                isIT
            }) {
                var career_id = $('#career-select').val();
                var skill_id = $('#skill_id').val();
                $('#rank-select').select2({
                    // dropdownParent: "#modal-sell-cv",
                    // tags: true,
                    placeholder: '<?php echo e(config('settings.' . app()->getLocale() . '.rec_cv_selling.chonlevel')); ?>',
                    allowClear: true,
                    ajax: {
                        url: '<?php echo e(route('ajax-get-level-by-career')); ?>' + '?career_id=' + career_id +
                            '&skill_id=' + skill_id,
                        type: "get",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                    },
                })
            }

            $(".select2-custom-tags").select2({
                tags: true,
                placeholder: '<?php echo e($arrLang['input']); ?> <?php echo e($arrLang['kinang']); ?>',
                allowClear: true
            });

            // Khởi tạo select2 cho trường hình thức tuyển dụng
            $('#recruitment-type').select2({
                placeholder: '<?php echo e($arrLang['chon'] ?? 'Chọn'); ?> <?php echo e(lcfirst($arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng')); ?>',
                allowClear: true,
                minimumResultsForSearch: -1,
                // dropdownParent: "#modal-sell-cv",
                // dropdownCssClass: 'customer-dropdown-select2'
            });
            // Tính tổng tiền khi thay đổi chi phí hoặc số lượng tuyển
            $('#bonus, input[name="vacancies"]').on('input', function() {
                calculateTotalCost();
            });

            function calculateTotalCost() {
                var cost = parseFloat($('#bonus').val()) || 0;
                var bonus_for_ctv = parseFloat($('#bonus_for_ctv').val()) || 0;
                var vacancies = parseInt($('input[name="vacancies"]').val()) || 0;
                var total = cost * vacancies;
                $('#total-cost').val(total.toLocaleString('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }));
                // $('#bonus_for_ctv').val(bonus_for_ctv.toLocaleString('vi-VN', {
                //     style: 'currency',
                //     currency: 'VND'
                // }));
            }

            $('[data-toggle="add-item"]').click(function() {
                // let html = $('.item-clone').html();
                let countHtmlAddress = $('.render-address').length;
                if (countHtmlAddress <= 3) {
                    let cities = '<?php echo json_encode($cities); ?>';
                    cities = JSON.parse(cities);
                    let options;
                    $.each(cities, function(key, value) {
                        options = options + '<option value="' + key + '">' + value + '</option>';
                    });
                    let html = '';
                    html += '<div class="row item render-address">'
                    html +=
                        '<div class="col-md-3"><div class="form-item"><select style="width: 100%" class="select-clone area' +
                        (countHtmlAddress - 1) + '" name="address[' + (countHtmlAddress - 1) + '][area]">'
                    html += options
                    html += '</select></div></div>'
                    html +=
                        '<div class="col-md-9"><div class="row"><div class="col-md-11 col-form-item"><div class="form-item">'
                    html += '<input type="text" placeholder="Địa chỉ cụ thể" name="address[' + (
                            countHtmlAddress - 1) + '][address]" class="address' + (countHtmlAddress - 1) +
                        '">'
                    html += '</div></div><div class="col-md-1 col-remove-btn">'
                    html += '<a href="javascript:void(0)" data-toggle="removeitem">'
                    html +=
                        '<img src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/icon-remove.svg')); ?>">'
                    html += '</a></div></div></div>'


                    $('.group-field-repeater').append(html);
                    $('.group-field-repeater .select-clone').select2();
                }
            })
            $('body').on('click', '[data-toggle="removeitem"]', function() {
                $(this).parents('.item').remove();
            })
            CKEDITOR.replace('jd_description');
            CKEDITOR.replace('jd_request');
            CKEDITOR.replace('jd_welfare');

            var dateToday = new Date();
            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy',
                minDate: dateToday,
            });

            let urlCompany = '<?php echo e(route('ajax-list-company')); ?>';
            $("#company_id").select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['congty'])); ?>',
                allowClear: true,
                ajax: {
                    url: urlCompany,
                    type: "get",
                    dataType: 'json',
                    delay: 250,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });
            $("#company_id").val($('#hdd_company_id').val()).trigger('change');

            $('#company_id').change(function() {
                let id = $(this).val();
                let url = '<?php echo e(route('ajax-detail-company', ':id')); ?>';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'get',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        let companyObj = res[0];
                        let listEmployer = res[1];
                        let arrAddress = JSON.parse(companyObj.address);
                        $.each(arrAddress, function(key, value) {
                            if (key == 0) {

                            } else if (value['area']) {
                                $('[data-toggle="add-item"]').trigger('click');
                            }
                            $('.area' + key).val(value.area).trigger('change');
                            $('.address' + key).val(value.address);
                        });

                        var output = [];
                        $.each(listEmployer, function(key, value) {
                            output.push('<option value="' + value.id + '">' + value
                                .name + '</option>');
                        });
                        $('#employer-select').html(output.join(''));
                    }
                });
            });

            $("#form-job").validate({
                ignore: [],
                debug: false,
                rules: {
                    name: {
                        required: true,
                    },
                    expire_at: {
                        required: true,
                    },
                    company_id: {
                        required: true,
                    },
                    vacancies: {
                        required: true,
                        number: true,
                        digits: true,
                    },
                    employer_id: {
                        required: true,
                    },
                    'rank[]': {
                        required: true,
                    },
                    'career[]': {
                        required: true,
                    },
                    type: {
                        required: true,
                    },
                    'skill[]': {
                        required: true,
                    },
                    salary_min: {
                        required: true,
                        number: true,
                    },
                    salary_max: {
                        required: true,
                        number: true,
                    },
                    file_jd: {
                        <?php if(!$job->file_jd): ?>
                            required: true,
                        <?php endif; ?>
                        extension: "pdf,docx",
                        filesize: 5, // <- 5 MB
                    },
                    jd_description: {
                        ckrequired: true,
                    },
                    jd_request: {
                        ckrequired: true,
                    },
                    jd_welfare: {
                        ckrequired: true,
                    },
                    "address[0][area]": {
                        required: true
                    },
                    "address[0][address]": {
                        required: true
                    },

                },
                messages: {
                    name: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    expire_at: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    company_id: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    vacancies: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        number: '<?php echo e(__('frontend/validation.number')); ?>',
                        digits: '<?php echo e(__('frontend/validation.number')); ?>',
                    },
                    employer_id: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    'rank[]': {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    'career[]': {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    type: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    'skill[]': {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    salary_min: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        number: '<?php echo e(__('frontend/validation.number')); ?>',
                    },
                    salary_max: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        number: '<?php echo e(__('frontend/validation.number')); ?>',
                    },
                    file_jd: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        extension: '<?php echo e(__('frontend/validation.format_cv')); ?>',
                        filesize: '<?php echo e(__('frontend/validation.file_max')); ?>',
                    },
                    jd_description: {
                        ckrequired: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    jd_request: {
                        ckrequired: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    jd_welfare: {
                        ckrequired: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    "address[0][area]": {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    "address[0][address]": {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                },

                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else if (elem.hasClass("field-input-unit")) {
                        elem.parent().addClass(errorClass);
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        console.log(errorClass);
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    console.log(elem);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-input-unit")) {
                        element = elem.parent();
                        error.insertAfter(element);
                    } else if (elem.hasClass("file-browserinput")) {
                        element = elem.parents('.input-file');
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-ck")) {
                        element = elem.parents('.ck-custom');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            $.validator.addMethod('regex', function(value) {
                var regex = /^[0-9()+.-]*$/;
                return value.trim().match(regex);
            });

            $.validator.addMethod('filesize', function(value, element, param) {
                return this.optional(element) || (element.files[0].size <= param * 1000000)
            }, 'File size must be less than {0} MB');

            $(document).on('click', '.btn-save', function() {
                if ($("#form-job").valid()) {
                    $('#form-job').submit();
                }
            });

            $('#type-select').select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['hinhthuc'])); ?>',
                allowClear: true
            });

            $('#career-select').select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['linhvuc'])); ?>',
                allowClear: true,
                maximumSelectionLength: 3
            });

            $('#rank-select').select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['capbac'])); ?>',
                allowClear: true,
                maximumSelectionLength: 3
            });

            $('#employer-select').select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['nhatuyendung'])); ?>',
                allowClear: true
            });

            $('.address-select').select2({
                placeholder: '<?php echo e(lcfirst($arrLang['diadiem'])); ?>',
                allowClear: true
            });

            let url = '<?php echo e(route('ajax-list-skill')); ?>';
            // $("#skill_id").select2({
            //     tags: true,
            //     placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['kinang'])); ?>',
            //     allowClear: true,
            //     maximumSelectionLength: 3,
            //     ajax: {
            //         url: url,
            //         type: "get",
            //         dataType: 'json',
            //         delay: 550,
            //         headers: {
            //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //         },
            //         data: function(params) {
            //             return {
            //                 searchTerm: params.term // search term
            //             };
            //         },
            //         processResults: function(response) {
            //             return {
            //                 results: response
            //             };
            //         },
            //         cache: true
            //     }
            // });

            $('.salary_currency').change(function() {
                let val = $(this).val();
                $('.salary_currency_max').val(val).trigger('change');
            });

            $('.btn-reload').click(function() {
                location.reload();
            })

            $(document).on('click', '.show-iframe', function() {
                let url = $(this).data('url');
                $(".iframe").attr('src', url);
                $("#modal-cv").modal('show');
            });

            $(document).on('click', '.back-header', function() {
                $("#modal-cv").modal('hide');
            });
            $('#status').select2({
                minimumResultsForSearch: Infinity
            });
            $('#is_active').select2({
                minimumResultsForSearch: Infinity
            });

            $('#bonus').change(function() {
                calculateBonusCtv();
            });

            $('#recruitment-type').change(function() {
                calculateBonusCtv();
            });

            $('.calculate-bonus').click(function() {
                calculateBonusCtv();
            });

            function calculateBonusCtv() {
                
                let bonus = $('#bonus').val();
                let bonus_type = $('#recruitment-type').val();
                
                if (!bonus || !bonus_type) {
                    alert('Vui lòng điền đầy đủ thông tin chi phí, số lượng tuyển, kỹ năng, cấp bậc và hình thức tuyển dụng');
                    return;
                }

                $.ajax({
                    url: '<?php echo e(route("ajax-calculate-bonus-ctv")); ?>',
                    type: 'POST',
                    data: {
                        bonus: bonus,
                        bonus_type: bonus_type
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#bonus_for_ctv').val(formatCurrency(response.bonus_for_ctv));
                        } else {
                            alert(response.message || 'Có lỗi xảy ra khi tính toán');
                        }
                    },
                    error: function() {
                        alert('Có lỗi xảy ra khi tính toán');
                    }
                });
            }
            // Hàm format tiền tệ
            function formatCurrency(amount) {
                return new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }).format(amount);
            }
        })
</script>
<!-- Thêm Vue và component -->
<script src="<?php echo e(asset('js/app.js')); ?>"></script>
<script>
    new Vue({
            el: '#job-comments'
        });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Projects\HRI\RecLand\resources\views/admin/pages/job/edit.blade.php ENDPATH**/ ?>