<?php

namespace App\Observers;

use App\Models\SubmitCv;
use App\Models\Job;
use App\Models\CouponCode;
use Illuminate\Support\Facades\Log;

class SubmitCvObserver
{
    /**
     * Handle the SubmitCv "created" event.
     *
     * @param  \App\Models\SubmitCv  $submitCv
     * @return void
     */
    public function created(SubmitCv $submitCv)
    {
        $this->checkCouponCvLimit($submitCv);
    }

    /**
     * Kiểm tra giới hạn CV cho mã khuyến mại
     */
    private function checkCouponCvLimit(SubmitCv $submitCv)
    {
        try {
            // Lấy thông tin job
            $job = Job::find($submitCv->job_id);
            if (!$job || empty($job->coupon_code)) {
                return; // Không có mã khuyến mại, bỏ qua
            }

            // Lấy thông tin coupon
            $coupon = CouponCode::findByCode($job->coupon_code);
            if (!$coupon || empty($coupon->max_cv)) {
                return; // Không có giới hạn CV, bỏ qua
            }

            // Đếm tổng số submit_cv của tất cả job thuộc cùng employer sử dụng cùng mã khuyến mại
            $totalSubmitCv = SubmitCv::whereHas('job', function ($query) use ($job, $coupon) {
                $query->where('employer_id', $job->employer_id)
                    ->where('coupon_code', $coupon->code);
            })->count();

            // Kiểm tra nếu đã đạt giới hạn
            if ($totalSubmitCv >= $coupon->max_cv) {
                // Tự động chuyển trạng thái tất cả job sử dụng mã khuyến mại thành "dừng tuyển"
                Job::where('employer_id', $job->employer_id)
                    ->where('coupon_code', $coupon->code)
                    ->update(['status' => 0]); // status = 0 là "dừng tuyển"

                // Ghi log
                Log::info('Coupon CV limit reached', [
                    'coupon_code' => $coupon->code,
                    'employer_id' => $job->employer_id,
                    'max_cv' => $coupon->max_cv,
                    'total_submit_cv' => $totalSubmitCv,
                    'jobs_stopped' => Job::where('employer_id', $job->employer_id)
                        ->where('coupon_code', $coupon->code)
                        ->pluck('id')->toArray()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error checking coupon CV limit', [
                'submit_cv_id' => $submitCv->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle the SubmitCv "updated" event.
     *
     * @param  \App\Models\SubmitCv  $submitCv
     * @return void
     */
    public function updated(SubmitCv $submitCv)
    {
        //
    }

    /**
     * Handle the SubmitCv "deleted" event.
     *
     * @param  \App\Models\SubmitCv  $submitCv
     * @return void
     */
    public function deleted(SubmitCv $submitCv)
    {
        //
    }

    /**
     * Handle the SubmitCv "restored" event.
     *
     * @param  \App\Models\SubmitCv  $submitCv
     * @return void
     */
    public function restored(SubmitCv $submitCv)
    {
        //
    }

    /**
     * Handle the SubmitCv "force deleted" event.
     *
     * @param  \App\Models\SubmitCv  $submitCv
     * @return void
     */
    public function forceDeleted(SubmitCv $submitCv)
    {
        //
    }
}
