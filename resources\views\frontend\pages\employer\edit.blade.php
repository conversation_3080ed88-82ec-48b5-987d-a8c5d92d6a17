@extends('frontend.layouts.employer.app')
@section('content-collaborator')
    <div class="base-layout-edit">
        <div class="header-layout-edit">
            <div class="row">
                <div class="col-md-4">
                    <a class="st1" href="{{ route('employer-job') }}"><img
                            src="{{ asset2('frontend/asset/images/dashboard-ctv/arrow-back.svg') }}">
                        {{ ucfirst(mb_strtolower($arrLang['danhsachcongviec'])) }}
                    </a>
                </div>
                <div class="col-md-4 text-center">
                    <a class="st2" href="javascript:void(0)">{{ $arrLang['chinhsuacongviec'] }}</a>
                </div>
                <div class="col-md-4"></div>
            </div>
        </div>


        <div class="main-form">
            <form action="{{ route('employer-update', ['id' => $job->id]) }}" id="form-job" method="post"
                enctype="multipart/form-data">
                @csrf
                {{ method_field('put') }}
                <div class="row">
                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['tenvieclam'] }} <span>*</span></label>
                            <input type="text"
                                placeholder="{{ $arrLang['input'] }} {{ lcfirst($arrLang['tenvieclam']) }}"
                                value="{{ $job->name }}" name="name">
                        </div>
                    </div>
                    <div class="col-md-3 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['hannop'] }} <span>*</span></label>
                            <input class="fc-datepicker" type="text" placeholder="DD/MM/YYYY"
                                value="{{ $job->expire_at_value }}" name="expire_at">
                        </div>
                    </div>
                    {{-- 
                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['trangthai'] }} <span>*</span></label>
                            <select id="is_active" style="width:100%" class="select2-custom" name="is_active">
                                @foreach ($isActive as $k => $item)
                                    <option value="{{ $k }}" {{ $job->is_active == $k ? 'selected' : '' }}>
                                        {{ $item }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div> --}}

                    <div class="col-md-3 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['soluongtuyen'] }} <span>*</span></label>
                            <input type="text"
                                placeholder="{{ $arrLang['input'] }} {{ lcfirst($arrLang['soluongtuyen']) }}"
                                value="{{ $job->vacancies }}" name="vacancies">
                        </div>
                    </div>
                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['trangthaicongviec'] }} <span>*</span></label>
                            <select id="status" style="width:100%" class="select2-custom" name="status">
                                @foreach ($status as $k => $item)
                                    <option value="{{ $k }}" {{ $job->status == $k ? 'selected' : '' }}>
                                        {{ $item }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    {{--                    <div class="col-md-6 col-form-item"> --}}
                    {{--                        <div class="form-item"> --}}
                    {{--                            <label>{{$arrLang['congty']}} <span>*</span></label> --}}
                    {{--                            <input type="hidden" name="hdd_company_id" id="hdd_company_id" value="{{$job->company_id}}"/> --}}
                    {{--                            <select id="company_id" style="width:100%" class="select2-custom" name="company_id"> --}}
                    {{--                            </select> --}}
                    {{--                        </div> --}}
                    {{--                    </div> --}}


                    {{--                    <div class="col-md-6 col-form-item"> --}}
                    {{--                        <div class="form-item"> --}}
                    {{--                            <label>{{$arrLang['nhatuyendung']}} <span>*</span></label> --}}
                    {{--                            <select id="employer-select" style="width:100%" class="select2-custom" name="employer_id"> --}}
                    {{--                            </select> --}}
                    {{--                        </div> --}}
                    {{--                    </div> --}}

                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['linhvuc'] }} <span>*</span></label>
                            <div class="multiple-select2">
                                <select id="career-select" style="width:100%;display: none"
                                    class="select2-custom select-max-item" name="career">
                                    @foreach ($career as $k => $item)
                                        <option value="{{ $k }}" {{ $k == $job->career ? 'selected' : '' }}>
                                            {{ $item }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <?php
                    // $arrRank = explode(',', $job->rank);
                    // $arrRank = array_flip($arrRank);
                    ?>
                    {{-- <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{$arrLang['capbac']}} <span>*</span></label>
                            <div class="multiple-select2">
                                <select id="rank-select" style="width:100%;display: none"
                                        class="select2-custom select-max-item" multiple name="rank[]">
                                    @foreach ($rank as $k => $item)
                                        <option
                                            value="{{$k}}" {{isset($arrRank[$k]) ? 'selected' : ''}}>{{$item}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div> --}}

                    <?php
                    // $arrCareer = explode(',', $job->career);
                    // $arrCareer = array_flip($arrCareer);
                    ?>
                    {{-- <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{$arrLang['linhvuc']}} <span>*</span></label>
                            <div class="multiple-select2">
                                <select id="career-select" style="width:100%;display: none"
                                        class="select2-custom select-max-item" multiple name="career[]">
                                    @foreach ($career as $k => $item)
                                        <option
                                            value="{{$k}}" {{isset($arrCareer[$k]) ? 'selected' : ''}}>{{$item}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div> --}}

                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['kinang'] }} <span>*</span></label>
                            <div class="multiple-select2">
                                <select id="skill_id" style="width:100%;display: none"
                                    class="select2-custom-tags select-max-item" name="skill">
                                    <option label="--- Chọn ---"></option>
                                    @foreach ($skills as $k => $item)
                                        <option value="{{ $k }}" {{ $k == $job->skill_id ? 'selected' : '' }}>
                                            {{ $item }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <?php
                    // $arrSkill = explode(',', $job->skills);
                    ?>
                    {{-- <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{$arrLang['kinang']}} <span>*</span></label>
                            <div class="multiple-select2">
                                <select id="skill_id" multiple style="width:100%;display: none"
                                        class="select2-custom-tags select-max-item" name="skill[]">
                                    @if (count($arrSkill))
                                        @foreach ($arrSkill as $item)
                                            <option value="{{$item}}" selected>{{$item}}</option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                        </div>
                    </div> --}}

                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['capbac'] }} <span>*</span></label>
                            <div class="multiple-select2">
                                <select id="rank-select" style="width:100%;display: none"
                                    class="select2-custom select-max-item" name="rank">
                                    @foreach ($levels as $k => $item)
                                        <option value="{{ $k }}" {{ $k == $job->rank ? 'selected' : '' }}>
                                            {{ $item }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['salarymin'] }} <span>*</span> </label>
                            <div class="input-unit">
                                <input data-toggle="current_mask" data-target="#field_salary_min" type="text"
                                    class="field-input-unit" value="{{ $job->salary_min }}"
                                    placeholder="{{ $arrLang['input'] }} {{ lcfirst($arrLang['salarymin']) }}">
                                <select style="display: none" class="select2-unit select2-hide-input salary_currency"
                                    name="salary_currency">
                                    @foreach ($currency as $item)
                                        <option value="{{ $item }}"
                                            {{ $job->salary_currency == $item ? 'selected' : '' }}>{{ $item }}
                                        </option>
                                    @endforeach
                                </select>
                                <input id="field_salary_min" class="field-input-unit" type="hidden" value=""
                                    name="salary_min">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['salarymax'] }} <span>*</span> </label>
                            <div class="input-unit">
                                <input data-toggle="current_mask" data-target="#field_salary_max" type="text"
                                    class="field-input-unit" value="{{ $job->salary_max }}"
                                    placeholder="{{ $arrLang['input'] }} {{ lcfirst($arrLang['salarymax']) }}">
                                <select style="display: none" class="select2-unit select2-hide-input salary_currency_max"
                                    name="salary_currency_max">
                                    @foreach ($currency as $item)
                                        <option value="{{ $item }}"
                                            {{ $job->salary_currency == $item ? 'selected' : '' }}>{{ $item }}
                                        </option>
                                    @endforeach
                                </select>
                                <input id="field_salary_max" class="field-input-unit" type="hidden" value=""
                                    name="salary_max">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng' }} <span>*</span></label>
                            <select id="recruitment-type" style="width:100%;display: none" class=""
                                name="bonus_type">
                                {{-- <option value="onboard">Onboard</option>
                                <option value="interview">Interview</option>
                                <option value="cv">CV</option> --}}
                                <option value="">
                                    {{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonhinhthuc') }}
                                </option>
                                @foreach ($bonusTypes as $bonusType)
                                    <option value="{{ $bonusType }}"
                                        {{ $bonusType == $job->bonus_type ? 'selected' : '' }}>{{ $bonusType }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['chiphituyendung'] ?? 'Chi phí cho 1 lần tuyển dụng' }}
                                <span>*</span></label>
                            <input type="number" id="bonus" name="bonus" class="form-control"
                                value="{{ $job->bonus }}">
                            <label style="font-weight: normal; font-size: 14px;" id="bonus_msg"
                                class="form-text text-muted mt-2 d-block">Giá tối thiểu là <span id="min_price"></span>
                                vnđ</label>
                        </div>
                    </div>

                    <div class="col-md-4 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['tongtien'] ?? 'Tổng chi phí' }}</label>
                            <input type="text" id="total-cost" readonly class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6 col-form-item">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-item">
                                            <label>{{ $arrLang['diadiem'] }} <span>*</span>
                                                <a data-toggle="add-item" href="javascript:void(0)">
                                                    <img
                                                        src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-add-item.svg') }}">
                                                </a></label>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="form-item">
                                            <label>{{ $arrLang['diachicuthe'] }}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="group-field-repeater group-field-repeater-out-row">
                                    @foreach ($job->address_value as $k => $item)
                                        @if ($item)
                                            <div class="row render-address">
                                                <div class="col-md-3">
                                                    <div class="form-item">
                                                        <select style="width: 100%;display: none"
                                                            class="select2-custom area{{ $k }} address-select"
                                                            name="address[{{ $k }}][area]">
                                                            <option value=""></option>
                                                            @foreach ($cities as $key => $city)
                                                                <option value="{{ $key }}"
                                                                    {{ $item->area == $key ? 'selected' : '' }}>
                                                                    {{ $city }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-9">
                                                    <div class="row">
                                                        <div class="col-md-11 col-form-item">
                                                            <div class="form-item">
                                                                <input type="text" class="address{{ $k }}"
                                                                    value="{{ $item->address }}"
                                                                    placeholder="{{ $arrLang['diachicuthe'] }}"
                                                                    name="address[{{ $k }}][address]">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="item-clone" style="display: none">
                            <div class="row item render-address">
                                <div class="col-md-3">
                                    <div class="form-item">
                                        <select style="width: 100%" class="select-clone">
                                            @foreach ($cities as $k => $city)
                                                <option value="{{ $k }}">{{ $city }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-11 col-form-item">
                                            <div class="form-item">
                                                <input type="text" placeholder="{{ $arrLang['diachicuthe'] }}"
                                                    name="address[]">
                                            </div>
                                        </div>
                                        <div class="col-md-1 col-remove-btn">
                                            <a href="javascript:void(0)" data-toggle="removeitem">
                                                <img
                                                    src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-remove.svg') }}">
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-form-item">
                        <div class="row">
                            <div class="col-md-12 col-form-item">
                                <div class="form-item">
                                    <label>{{ $arrLang['hinhthuc'] }} <span>*</span></label>
                                    <select id="type-select" style="width:100%;display: none" class="select2-custom"
                                        name="type">
                                        <option label="--- Chọn ---"></option>
                                        @foreach ($type as $k => $item)
                                            <option value="{{ $k }}" {{ $job->type == $k ? 'selected' : '' }}>
                                                {{ $item }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12 col-form-item">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-item-switch">
                                                    <div class="checkbox-remote">
                                                        <label class="switch">
                                                            <input type="checkbox" name="remote"
                                                                {{ $job->remote ? 'checked' : '' }}>
                                                            <span class="slider round"></span>
                                                        </label>
                                                        {{ $arrLang['remote'] }}
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-item-switch">
                                                    <div class="checkbox-remote">
                                                        <label class="switch">
                                                            <input type="checkbox" name="urgent"
                                                                {{ $job->urgent ? 'checked' : '' }}>
                                                            <span class="slider round"></span>
                                                        </label>
                                                        {{ $arrLang['urgent'] }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    {{-- <div class="col-md-6 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['jd'] }}</label>
                            <div class="input-file">
                                <div class="input-group file-browser">
                                    <div class="input-review">
                                        <input type="text" class="file-browser-mask"
                                            placeholder="{{ $job->file_jd }}" readonly="">
                                        <a class="button-review show-iframe" data-url="{{ $job->path_file_jd }}"
                                            href="javascript:void(0)">{{ $arrLang['xem'] }}</a>
                                    </div>
                                    <label class="input-group-append">
                                        <span class="btn-file">
                                            {{ $arrLang['chon'] }} <input type="file" class="file-browserinput"
                                                style="display: none;" name="file_jd">
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div> --}}


                    <div class="col-md-12 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['jdmota'] }} <span>*</span></label>
                            <div class="ck-custom">
                                <textarea class="field-ck" id="jd_description" rows="10" cols="80" name="jd_description">{!! $job->jd_description !!}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['jdyeucau'] }} <span>*</span></label>
                            <div class="ck-custom">
                                <textarea class="field-ck" id="jd_request" rows="10" cols="80" name="jd_request">{!! $job->jd_request !!}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 col-form-item">
                        <div class="form-item">
                            <label>{{ $arrLang['jdphucloi'] }} <span>*</span></label>
                            <div class="ck-custom">
                                <textarea class="field-ck" id="jd_welfare" rows="10" cols="80" name="jd_welfare">{!! $job->jd_welfare !!}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="col-12">
                <div class="group-btn">
                    <a class="btn btn-reload" href="javascript:void(0)">{{ $arrLang['datlai'] }}</a>
                    <button class="btn btn-save">{{ $arrLang['luu'] }}</button>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal CV -->
    <div class="modal fade" id="modal-cv">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="header back-header" style="cursor: pointer"><img
                            src="{{ asset2('frontend/asset/images/dashboard-ctv/arrow-right.svg') }}">
                        {{ $arrLang['chitietjob'] }}
                    </div>
                    <div class="content">
                        <iframe class="iframe" src="" style="width: 100%">

                        </iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Modal CV -->
@endsection
@section('scripts')
    <script src="{{ asset2('frontend/asset/js/additional-methods.min.js') }}"></script>
    <script src="{{ asset2('frontend/asset/ckeditor/ckeditor.js') }}"></script>
    <script>
        jQuery.validator.addMethod("ckrequired", function(value, element) {
            var idname = $(element).attr('id');
            var editor = CKEDITOR.instances[idname];
            var ckValue = GetTextFromHtml(editor.getData()).replace(/<[^>]*>/gi, '').trim();
            if (ckValue.length === 0) {
                $(element).val(ckValue);
            } else {
                $(element).val(editor.getData());
            }
            return $(element).val().length > 0;
        }, "This field is required");

        function GetTextFromHtml(html) {
            var dv = document.createElement("DIV");
            dv.innerHTML = html;
            return dv.textContent || dv.innerText || "";
        }


        $(document).ready(function() {

            // function getSkillIT(career = "") {
            //     $('.skill-main').select2({
            //         dropdownParent: "#modal-sell-cv",
            //         // tags: true,
            //         placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang') }}',
            //         allowClear: true,
            //         ajax: {
            //             url: '{{ route('ajax-get-skill-main-it') }}' + '?career=' + career,
            //             type: "get",
            //             dataType: 'json',
            //             headers: {
            //                 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //             },
            //             data: function(params) {
            //                 return {
            //                     searchTerm: params.term // search term
            //                 };
            //             },
            //             processResults: function(response) {
            //                 return {
            //                     results: $.map(response, function(item) {
            //                         return {
            //                             ...item,
            //                             text: item.name_en
            //                         }
            //                     })
            //                 };
            //             },
            //         },
            //     })
            // }

            function getMinPrice() {
                $('#bonus').prop('disabled', true);
                // if ($('#skill_id').select2('data')[0]) {
                // let group = $('#skill_id').select2('data')[0].group;
                // let isIT = $('#skill_id').select2('data')[0].is_it;
                // }
                if (typeof group == 'undefined' || !group) group = '';
                if (typeof isIT == 'undefined' || !isIT) isIT = '';

                console.log('group: ' + group, 'isIT: ' + isIT);

                let level = $('#rank-select').val();
                let skill_id = $('#skill_id').val();
                let bonus_type = $('#recruitment-type').val();
                let career = $('#career-select').val();
                let salary_min = $('#field_salary_min').val();
                let salary_max = $('#field_salary_max').val();
                let salary_currency = $('.salary_currency').val();

                if (bonus_type == 'onboard' && salary_max == '') {
                    $('#bonus_msg').html(
                        'Vui lòng nhập lương tối thiểu & hình thức tuyển dụng để tính chi phí tối thiểu');
                    return;
                }
                if (!level || !bonus_type || !skill_id) {
                    $('#bonus_msg').html('Vui lòng chọn cấp bậc, hình thức tuyển dụng và kỹ năng chính để xem giá');
                    return;
                }


                console.log('level: ' + level, 'bonus_type: ' + bonus_type, 'career: ' + career, 'isIT: ' + isIT,
                    'group: ' + group);
                $.ajax({
                    url: '{{ route('ajax-get-min-submit-price') }}' + '?group=' + group + '&is_it=' +
                        isIT + '&bonus_type=' + bonus_type + '&level=' + level + '&career=' + career +
                        '&salary_min=' + salary_min + '&salary_max=' + salary_max + '&salary_currency=' +
                        salary_currency + '&skill_id=' +
                        skill_id,
                    type: "get",
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#bonus').prop('disabled', false);
                        min_price = res.data.min_price;
                        $('#min_price').html(formatCurrency(min_price));
                        $('#bonus_msg').html('Giá tối thiểu là ' + formatCurrency(min_price) + ' vnđ');
                        // if (res) {
                        //     if (type == 'cv') {
                        //         $('.min-price').html(formatCurrency(res.price_cv_min))
                        //         $('.max-price').html(formatCurrency(res.price_cv_max))
                        //         $("input[name='min-price']").val(res.price_cv_min);
                        //         $("input[name='max-price']").val(res.price_cv_max);
                        //     } else if (type == 'interview') {
                        //         $('.min-price').html(formatCurrency(res.price_interview_min))
                        //         $('.max-price').html(formatCurrency(res.price_interview_max))
                        //         $("input[name='min-price']").val(res.price_interview_min);
                        //         $("input[name='max-price']").val(res.price_interview_max);
                        //     }
                        // }
                    }
                })
            }

            function getSkillIT(career = "") {
                let url = '{{ route('ajax-get-skill-main-it') }}?career=' + career;
                $("#skill_id").select2({
                    tags: true,
                    placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}',
                    allowClear: true,
                    maximumSelectionLength: 3,
                    ajax: {
                        url: url,
                        type: "get",
                        dataType: 'json',
                        delay: 550,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                        cache: false
                    }
                });
                // $('#skill_id').select2({
                //     // dropdownParent: "#modal-sell-cv",
                //     // tags: true,
                //     placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang') }}',
                //     allowClear: true,
                //     ajax: {
                //         url: '{{ route('ajax-get-skill-main-it') }}' + '?career=' + career,
                //         type: "get",
                //         dataType: 'json',
                //         headers: {
                //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                //         },
                //         data: function (params) {
                //             return {
                //                 searchTerm: params.term // search term
                //             };
                //         },
                //         processResults: function (response) {
                //             return {
                //                 results: $.map(response, function (item) {
                //                     return {
                //                         ...item,
                //                         text: item.name_en
                //                     }
                //                 })
                //             };
                //         },
                //     },
                // })
            }

            function loadInitData() {
                // load du lieu vao skill va cap bac
                let career = $('#career-select').val();
                let url = '{{ route('ajax-get-skill-main-it') }}?career=' + career;
                console.log(url);

                $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        let skillSelect = $('#skill_id');
                        skillSelect.empty(); // Xóa tất cả options hiện tại

                        // Thêm option mặc định
                        skillSelect.append($('<option>', {
                            value: '',
                            text: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}'
                        }));

                        // Thêm các options từ dữ liệu trả về
                        $.each(response, function(index, item) {
                            skillSelect.append($('<option>', {
                                value: item.id,
                                text: item.name_en
                            }));
                        });
                        getSkillIT(career);
                        $('#skill_id').val({{ $job->skill_id }}).trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error("Lỗi khi tải dữ liệu kỹ năng:", error);
                    }
                });


                $.ajax({
                    url: '{{ route('ajax-get-level-by-career') }}' +
                        '?career_id={{ $job->career }}&skill_id={{ $job->skill_id }}',
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        let rankSelect = $('#rank-select');
                        rankSelect.empty(); // Xóa tất cả options hiện tại

                        // Thêm option mặc định
                        rankSelect.append($('<option>', {
                            value: '',
                            text: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}'
                        }));

                        // Thêm các options từ dữ liệu trả về
                        $.each(response, function(index, item) {
                            rankSelect.append($('<option>', {
                                value: item.id,
                                text: item.name_vi
                            }));
                        });
                        getLevel({
                            group: '{{ $skill && $skill->group ? $skill->group : 0 }}',
                            isIT: '{{ $isIt ? 'true' : 'false' }}'
                        });
                        $('#rank-select').val({{ $job->rank }}).trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error("Lỗi khi tải dữ liệu kỹ năng:", error);
                    }
                });
                // $.each(skill, function(key, value) {
                //     output.push('<option value="' + value.id + '">' + value.name + '</option>');
                // });
                $('#career-select').val({{ $job->career }}).trigger('change');
                $('#recruitment-type').val('{{ $job->bonus_type }}').trigger('change');
            }
            // loadInitData();

            $('#career-select').change(function() {
                let career = $(this).val();
                $("#skill_id").empty();
                getSkillIT(career);
            });

            $('#rank-select').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#recruitment-type').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#skill_id').change(function(e) {
                if ($(this).val()) {
                    $('#rank-select').val(null).trigger('change');
                    $('#recruitment-type').val(null).trigger('change');
                    let group = $(this).select2('data')[0].group;
                    let isIT = $(this).select2('data')[0].is_it;
                    getLevel2();
                }
            });
            getMinPrice();
            calculateTotalCost();

            function getLevel({
                group,
                isIT
            }) {
                $('#rank-select').select2({
                    // dropdownParent: "#modal-sell-cv",
                    // tags: true,
                    placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonlevel') }}',
                    allowClear: true,
                    ajax: {
                        url: '{{ route('ajax-get-level') }}' + '?group=' + group + '&is_it=' + isIT,
                        type: "get",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                    },
                })
            }

            function getLevel2() {
                var career_id = $('#career-select').val();
                var skill_id = $('#skill_id').val();
                $.ajax({
                    url: '{{ route('ajax-get-level-by-career') }}' + '?career_id=' + career_id +
                        '&skill_id=' + skill_id,
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        let rankSelect = $('#rank-select');
                        rankSelect.empty(); // Xóa tất cả options hiện tại

                        // Thêm option mặc định
                        rankSelect.append($('<option>', {
                            value: '',
                            text: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}'
                        }));

                        // Thêm các options từ dữ liệu trả về
                        $.each(response, function(index, item) {
                            rankSelect.append($('<option>', {
                                value: item.id,
                                text: item.name_vi
                            }));
                        });
                        // getLevel({
                        //     group: '{{ $skill && $skill->group ? $skill->group : 0 }}',
                        //     isIT: '{{ $isIt ? 'true' : 'false' }}'
                        // });
                        $('#rank-select').val({{ $job->rank }}).trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error("Lỗi khi tải dữ liệu kỹ năng:", error);
                    }
                });
                // $('#rank-select').select2({
                //     // dropdownParent: "#modal-sell-cv",
                //     // tags: true,
                //     placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonlevel') }}',
                //     allowClear: true,
                //     ajax: {
                //         url: '{{ route('ajax-get-level') }}' + '?group=' + group + '&is_it=' + isIT,
                //         type: "get",
                //         dataType: 'json',
                //         headers: {
                //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                //         },
                //         data: function(params) {
                //             return {
                //                 searchTerm: params.term // search term
                //             };
                //         },
                //         processResults: function(response) {
                //             return {
                //                 results: $.map(response, function(item) {
                //                     return {
                //                         ...item,
                //                         text: item.name_en
                //                     }
                //                 })
                //             };
                //         },
                //     },
                // })
            }

            $(".select2-custom-tags").select2({
                tags: true,
                placeholder: '{{ $arrLang['input'] }} {{ $arrLang['kinang'] }}',
                allowClear: true
            });

            // Khởi tạo select2 cho trường hình thức tuyển dụng
            $('#recruitment-type').select2({
                placeholder: '{{ $arrLang['chon'] ?? 'Chọn' }} {{ lcfirst($arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng') }}',
                allowClear: true,
                minimumResultsForSearch: -1,
                // dropdownParent: "#modal-sell-cv",
                // dropdownCssClass: 'customer-dropdown-select2'
            });
            // Tính tổng tiền khi thay đổi chi phí hoặc số lượng tuyển
            $('#bonus, input[name="vacancies"]').on('input', function() {
                calculateTotalCost();
            });

            function calculateTotalCost() {
                var cost = parseFloat($('#bonus').val()) || 0;
                var vacancies = parseInt($('input[name="vacancies"]').val()) || 0;
                var total = cost * vacancies;
                $('#total-cost').val(total.toLocaleString('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }));
            }

            $('[data-toggle="add-item"]').click(function() {
                // let html = $('.item-clone').html();
                let countHtmlAddress = $('.render-address').length;
                if (countHtmlAddress <= 3) {
                    let cities = '{!! json_encode($cities) !!}';
                    cities = JSON.parse(cities);
                    let options;
                    $.each(cities, function(key, value) {
                        options = options + '<option value="' + key + '">' + value + '</option>';
                    });
                    let html = '';
                    html += '<div class="row item render-address">'
                    html +=
                        '<div class="col-md-3"><div class="form-item"><select style="width: 100%" class="select-clone area' +
                        (countHtmlAddress - 1) + '" name="address[' + (countHtmlAddress - 1) + '][area]">'
                    html += options
                    html += '</select></div></div>'
                    html +=
                        '<div class="col-md-9"><div class="row"><div class="col-md-11 col-form-item"><div class="form-item">'
                    html += '<input type="text" placeholder="Địa chỉ cụ thể" name="address[' + (
                            countHtmlAddress - 1) + '][address]" class="address' + (countHtmlAddress - 1) +
                        '">'
                    html += '</div></div><div class="col-md-1 col-remove-btn">'
                    html += '<a href="javascript:void(0)" data-toggle="removeitem">'
                    html +=
                        '<img src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-remove.svg') }}">'
                    html += '</a></div></div></div>'


                    $('.group-field-repeater').append(html);
                    $('.group-field-repeater .select-clone').select2();
                }
            })
            $('body').on('click', '[data-toggle="removeitem"]', function() {
                $(this).parents('.item').remove();
            })
            CKEDITOR.replace('jd_description');
            CKEDITOR.replace('jd_request');
            CKEDITOR.replace('jd_welfare');

            var dateToday = new Date();
            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy',
                minDate: dateToday,
            });

            let urlCompany = '{{ route('ajax-list-company') }}';
            $("#company_id").select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['congty']) }}',
                allowClear: true,
                ajax: {
                    url: urlCompany,
                    type: "get",
                    dataType: 'json',
                    delay: 250,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });
            $("#company_id").val($('#hdd_company_id').val()).trigger('change');

            $('#company_id').change(function() {
                let id = $(this).val();
                let url = '{{ route('ajax-detail-company', ':id') }}';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'get',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        let companyObj = res[0];
                        let listEmployer = res[1];
                        let arrAddress = JSON.parse(companyObj.address);
                        $.each(arrAddress, function(key, value) {
                            if (key == 0) {

                            } else if (value['area']) {
                                $('[data-toggle="add-item"]').trigger('click');
                            }
                            $('.area' + key).val(value.area).trigger('change');
                            $('.address' + key).val(value.address);
                        });

                        var output = [];
                        $.each(listEmployer, function(key, value) {
                            output.push('<option value="' + value.id + '">' + value
                                .name + '</option>');
                        });
                        $('#employer-select').html(output.join(''));
                    }
                });
            });

            $("#form-job").validate({
                ignore: [],
                debug: false,
                rules: {
                    name: {
                        required: true,
                    },
                    expire_at: {
                        required: true,
                    },
                    company_id: {
                        required: true,
                    },
                    vacancies: {
                        required: true,
                        number: true,
                        digits: true,
                    },
                    employer_id: {
                        required: true,
                    },
                    'rank[]': {
                        required: true,
                    },
                    'career[]': {
                        required: true,
                    },
                    type: {
                        required: true,
                    },
                    'skill[]': {
                        required: true,
                    },
                    salary_min: {
                        required: true,
                        number: true,
                    },
                    salary_max: {
                        required: true,
                        number: true,
                    },
                    // file_jd: {
                    //     @if (!$job->file_jd)
                    //         required: true,
                    //     @endif
                    //     extension: "pdf,docx",
                    //     filesize: 5, // <- 5 MB
                    // },
                    jd_description: {
                        ckrequired: true,
                    },
                    jd_request: {
                        ckrequired: true,
                    },
                    jd_welfare: {
                        ckrequired: true,
                    },
                    "address[0][area]": {
                        required: true
                    },
                    "address[0][address]": {
                        required: true
                    },

                },
                messages: {
                    name: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    expire_at: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    company_id: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    vacancies: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                        digits: '{{ __('frontend/validation.number') }}',
                    },
                    employer_id: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    'rank[]': {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    'career[]': {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    type: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    'skill[]': {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    salary_min: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                    },
                    salary_max: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                    },
                    file_jd: {
                        required: '{{ __('frontend/validation.required') }}',
                        extension: '{{ __('frontend/validation.format_cv') }}',
                        filesize: '{{ __('frontend/validation.file_max') }}',
                    },
                    jd_description: {
                        ckrequired: '{{ __('frontend/validation.required') }}',
                    },
                    jd_request: {
                        ckrequired: '{{ __('frontend/validation.required') }}',
                    },
                    jd_welfare: {
                        ckrequired: '{{ __('frontend/validation.required') }}',
                    },
                    "address[0][area]": {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    "address[0][address]": {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                },

                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else if (elem.hasClass("field-input-unit")) {
                        elem.parent().addClass(errorClass);
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        console.log(errorClass);
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    console.log(elem);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-input-unit")) {
                        element = elem.parent();
                        error.insertAfter(element);
                    } else if (elem.hasClass("file-browserinput")) {
                        element = elem.parents('.input-file');
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-ck")) {
                        element = elem.parents('.ck-custom');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            $.validator.addMethod('regex', function(value) {
                var regex = /^[0-9()+.-]*$/;
                return value.trim().match(regex);
            });

            $.validator.addMethod('filesize', function(value, element, param) {
                return this.optional(element) || (element.files[0].size <= param * 1000000)
            }, 'File size must be less than {0} MB');

            $(document).on('click', '.btn-save', function() {
                $('#bonus_msg').removeClass('error');
                let bonus = $('#bonus').val();
                // alert(min_price);
                if (bonus < min_price) {
                    $('#bonus').focus();
                    $('#bonus_msg').addClass('error');
                    return;
                }
                if ($("#form-job").valid()) {
                    $('#form-job').submit();
                }
            });

            $('#type-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['hinhthuc']) }}',
                allowClear: true
            });

            $('#career-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['linhvuc']) }}',
                allowClear: true,
                maximumSelectionLength: 3
            });

            $('#rank-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['capbac']) }}',
                allowClear: true,
                maximumSelectionLength: 3
            });

            $('#employer-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['nhatuyendung']) }}',
                allowClear: true
            });

            $('.address-select').select2({
                placeholder: '{{ lcfirst($arrLang['diadiem']) }}',
                allowClear: true
            });

            let url = '{{ route('ajax-list-skill') }}';
            // $("#skill_id").select2({
            //     tags: true,
            //     placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}',
            //     allowClear: true,
            //     maximumSelectionLength: 3,
            //     ajax: {
            //         url: url,
            //         type: "get",
            //         dataType: 'json',
            //         delay: 550,
            //         headers: {
            //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //         },
            //         data: function(params) {
            //             return {
            //                 searchTerm: params.term // search term
            //             };
            //         },
            //         processResults: function(response) {
            //             return {
            //                 results: response
            //             };
            //         },
            //         cache: true
            //     }
            // });

            $('.salary_currency').change(function() {
                let val = $(this).val();
                $('.salary_currency_max').val(val).trigger('change');
            });

            $('.btn-reload').click(function() {
                location.reload();
            })

            $(document).on('click', '.show-iframe', function() {
                let url = $(this).data('url');
                $(".iframe").attr('src', url);
                $("#modal-cv").modal('show');
            });

            $(document).on('click', '.back-header', function() {
                $("#modal-cv").modal('hide');
            });
            $('#status').select2({
                minimumResultsForSearch: Infinity
            });
            $('#is_active').select2({
                minimumResultsForSearch: Infinity
            });
        })
    </script>
@endsection
