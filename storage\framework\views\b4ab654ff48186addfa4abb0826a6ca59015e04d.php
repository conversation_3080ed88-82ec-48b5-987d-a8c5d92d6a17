

<?php $__env->startSection('content'); ?>

    <div class="page-header d-xl-flex d-block">
        <div class="page-leftheader">
            <h4 class="page-title">DASHBOARD</h4>
        </div>
    </div>

    <!--Row-->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-9">
                            <div class="mt-0 text-left">
                                <span class="fs-16 font-weight-semibold">Total Collaborators</span>
                                <h3 class="mb-0 mt-1 text-primary fs-25"><?php echo e($totalCollaborator); ?></h3>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="icon1 bg-primary my-auto  float-right"> <i class="feather feather-briefcase"></i> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-9">
                            <div class="mt-0 text-left">
                                <span class="fs-16 font-weight-semibold">Total Employees</span>
                                <h3 class="mb-0 mt-1 text-secondary fs-25"><?php echo e($totalEmployer); ?></h3>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="icon1 bg-secondary my-auto  float-right"> <i class="feather feather-briefcase"></i> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-9">
                            <div class="mt-0 text-left">
                                <span class="fs-16 font-weight-semibold">Total CV</span>
                                <h3 class="mb-0 mt-1 text-success fs-25"><?php echo e($totalCv); ?></h3>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="icon1 bg-success my-auto  float-right"> <i class="feather feather-briefcase"></i> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-9">
                            <div class="mt-0 text-left">
                                <span class="fs-16 font-weight-semibold">Total Job</span>
                                <h3 class="mb-0 mt-1 text-danger fs-25"><?php echo e($totalJob); ?></h3>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="icon1 bg-danger my-auto  float-right"> <i class="feather feather-briefcase"></i> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-9">
                            <div class="mt-0 text-left">
                                <span class="fs-16 font-weight-semibold">Total Company</span>
                                <h3 class="mb-0 mt-1 text-warning fs-25"><?php echo e($totalCompany); ?></h3>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="icon1 bg-warning my-auto  float-right"> <i class="feather feather-briefcase"></i> </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center align-items-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header text-center">
                    <h4>Lọc theo khoảng thời gian </h4>
                </div>
                <div class="card-body">
                    <form id="filterForm" class="d-flex justify-content-center">
                        <div class="form-group mx-2">
                            <label for="from_date">Từ ngày</label>
                            <input type="date" id="from_date" name="from_date" class="form-control">
                        </div>
                        <div class="form-group mx-2">
                            <label for="to_date">Đến ngày</label>
                            <input type="date" id="to_date" name="to_date" class="form-control">
                        </div>
                        <div class="form-group mx-2 align-self-end">
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 col-md-12">
            <div class="card">
                <div class="card-header border-bottom-0">
                    <h3 class="card-title">Biểu đồ tăng trưởng CV/CTV theo tháng</h3>
                </div>
                <div class="card-body">
                    <div id="warehouse" class="chartsh"
                         data-value="<?php echo e(implode(',', $wareHouseStatisticalByMonth)); ?>"
                         data-dataset1="<?php echo e(implode(',', $wareHouseCvSellingByMonth)); ?>"
                         data-dataset2="<?php echo e(implode(',', $submiCvByMonth)); ?>"
                         data-dataset3="<?php echo e(implode(',',$collaboratorStatisticalByMonth)); ?>">
                    </div>

                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-12">
            <div class="card">
                <div class="card-header border-bottom-0">
                    <h3 class="card-title">Biểu đồ tăng trưởng NTD/Job theo tháng</h3>
                </div>
                <div class="card-body">
                    <div id="employer-job" class="chartsh " data-employer="<?php echo e(implode(',',$employerStatisticalByMonth)); ?>"
                    data-job="<?php echo e(implode(',',$jobStatisticalByMonth)); ?>"></div>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>

    <!--Othercharts js-->
    <script src="<?php echo e(asset2('backend/assets/plugins/othercharts/jquery.sparkline.min.js')); ?>"></script>

    <!-- INTERNAL Peitychart js-->
    <script src="<?php echo e(asset2('backend/assets/plugins/peitychart/jquery.peity.min.js')); ?>"></script>
    <script src="<?php echo e(asset2('backend/assets/plugins/peitychart/peitychart.init.js')); ?>"></script>

    <!-- INTERNAL Apexchart js-->
    <script src="<?php echo e(asset2('backend/assets/plugins/apexchart/apexcharts.js')); ?>"></script>

    <!-- INTERNAL Vertical-scroll js-->
    <script src="<?php echo e(asset2('backend/assets/plugins/vertical-scroll/jquery.bootstrap.newsbox.js')); ?>"></script>
    <script src="<?php echo e(asset2('backend/assets/plugins/vertical-scroll/vertical-scroll.js')); ?>"></script>

    <!-- INTERNAL  Datepicker js -->
    <script src="<?php echo e(asset2('backend/assets/plugins/date-picker/jquery-ui.js')); ?>"></script>

    <!-- INTERNAL Chart js -->
    <script src="<?php echo e(asset2('backend/assets/plugins/chart/chart.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset2('backend/assets/plugins/chart/utils.js')); ?>"></script>

    <!-- INTERNAL Timepicker js -->
    <script src="<?php echo e(asset2('backend/assets/plugins/time-picker/jquery.timepicker.js')); ?>"></script>
    <script src="<?php echo e(asset2('backend/assets/plugins/time-picker/toggles.min.js')); ?>"></script>

    <!-- INTERNAL Chartjs rounded-barchart -->
    <script src="<?php echo e(asset2('backend/assets/plugins/echarts/echarts.5.3.2.min.js')); ?>"></script>




    <!-- INTERNAL Index js-->
    <script>
        function buildChartCvAndCtv(id,rawDataCvAndCtv) {
            // let object = $('#' + id);
            // let value = object.data('value').split(',').map(v => parseInt(v, 10));
            // let dataset1 = object.data('dataset1').split(',').map(v => parseInt(v, 10));
            // let dataset2 = object.data('dataset2').split(',').map(v => parseInt(v, 10));
            // let dataset3 = object.data('dataset3').split(',').map(v => parseInt(v, 10));

            // const rawData = {
            //     month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            //     value: value,
            //     dataset1: dataset1,
            //     dataset2: dataset2,
            //     dataset3: dataset3
            // };

            // Create datasets
            const datasetWithFilters = [
                {
                    id: 'dataset_value',
                    source: {
                        month: rawDataCvAndCtv.month,
                        value: rawDataCvAndCtv.value
                    }
                },
                {
                    id: 'dataset_dataset1',
                    source: {
                        month: rawDataCvAndCtv.month,
                        value: rawDataCvAndCtv.dataset1
                    }
                },
                {
                    id: 'dataset_dataset2',
                    source: {
                        month: rawDataCvAndCtv.month,
                        value: rawDataCvAndCtv.dataset2
                    }
                },
                {
                    id: 'dataset_dataset3',
                    source: {
                        month: rawDataCvAndCtv.month,
                        value: rawDataCvAndCtv.dataset3
                    }
                }
            ];

            const seriesList = [
                {
                    type: 'line',
                    datasetId: 'dataset_value',
                    showSymbol: false,
                    name: 'Tăng trưởng CV (Kho CV)',
                    endLabel: {
                        show: true,
                        formatter: function (params) {
                            return 'Kho CV: ' + params.value;
                        }
                    },
                    labelLayout: {
                        moveOverlap: 'shiftY'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    encode: {
                        x: 'month',
                        y: 'value',
                        label: ['Tăng trưởng CV (Kho CV)', 'value'],
                        itemName: 'month',
                        tooltip: ['value']
                    }
                },
                {
                    type: 'line',
                    datasetId: 'dataset_dataset1',
                    showSymbol: false,
                    name: 'Tăng trưởng CV (Market CV)',
                    endLabel: {
                        show: true,
                        formatter: function (params) {
                            return 'Market CV: ' + params.value;
                        }
                    },
                    labelLayout: {
                        moveOverlap: 'shiftY'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    encode: {
                        x: 'month',
                        y: 'value',
                        label: ['Tăng trưởng CV (Market CV)', 'value'],
                        itemName: 'month',
                        tooltip: ['value']
                    }
                },
                {
                    type: 'line',
                    datasetId: 'dataset_dataset2',
                    showSymbol: false,
                    name: 'Tăng trưởng CV (Giới thiệu)',
                    endLabel: {
                        show: true,
                        formatter: function (params) {
                            return 'Giới thiệu: ' + params.value;
                        }
                    },
                    labelLayout: {
                        moveOverlap: 'shiftY'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    encode: {
                        x: 'month',
                        y: 'value',
                        label: ['Tăng trưởng CV (Giới thiệu)', 'value'],
                        itemName: 'month',
                        tooltip: ['value']
                    }
                },
                {
                    type: 'line',
                    datasetId: 'dataset_dataset3',
                    showSymbol: false,
                    name: 'Tăng trưởng CTV',
                    endLabel: {
                        show: true,
                        formatter: function (params) {
                            return 'CTV: ' + params.value;
                        }
                    },
                    labelLayout: {
                        moveOverlap: 'shiftY'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    encode: {
                        x: 'month',
                        y: 'value',
                        label: ['Tăng trưởng CTV', 'value'],
                        itemName: 'month',
                        tooltip: ['value']
                    }
                }
            ];

            const option = {
                animationDuration: 10000,
                dataset: [
                    {
                        id: 'dataset_raw',
                        source: rawDataCvAndCtv
                    },
                    ...datasetWithFilters
                ],
                tooltip: {
                    order: 'valueDesc',
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    nameLocation: 'middle'
                },
                yAxis: {

                },
                grid: {
                    top: "25",
                    right: "0",
                    bottom: "17",
                    left: "25"
                },
                series: seriesList
            };

            var p = document.getElementById(id);
            echarts.init(p).setOption(option);
        }

        const rawDataCvAndCtv = {
            month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            value: $('#warehouse').data('value').split(',').map(v => parseInt(v, 10)),
            dataset1: $('#warehouse').data('dataset1').split(',').map(v => parseInt(v, 10)),
            dataset2: $('#warehouse').data('dataset2').split(',').map(v => parseInt(v, 10)),
            dataset3: $('#warehouse').data('dataset3').split(',').map(v => parseInt(v, 10))
        };
        buildChartCvAndCtv('warehouse', rawDataCvAndCtv);


        function buildChartEmployerAndJob(id,rawData) {
            // let object = $('#' + id);
            // let employer = object.data('employer').split(',').map(v => parseInt(v, 10));
            // let job = object.data('job').split(',').map(v => parseInt(v, 10));
            //
            //
            // const rawData = {
            //     month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            //     employer: employer,
            //     job: job
            //
            // };

            // Create datasets
            const datasetWithFilters = [
                {
                    id: 'dataset_employer',
                    source: {
                        month: rawData.month,
                        value: rawData.employer
                    }
                },
                {
                    id: 'dataset_job',
                    source: {
                        month: rawData.month,
                        value: rawData.job
                    }
                }
            ];

            const seriesList = [
                {
                    type: 'line',
                    datasetId: 'dataset_employer',
                    showSymbol: false,
                    name: 'Tăng trưởng NTD',
                    endLabel: {
                        show: true,
                        formatter: function (params) {
                            return 'NTD: ' + params.value;
                        }
                    },
                    labelLayout: {
                        moveOverlap: 'shiftY'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    encode: {
                        x: 'month',
                        y: 'value',
                        label: ['Tăng trưởng NTD', 'value'],
                        itemName: 'month',
                        tooltip: ['value']
                    }
                },
                {
                    type: 'line',
                    datasetId: 'dataset_job',
                    showSymbol: false,
                    name: 'Tăng trưởng Job',
                    endLabel: {
                        show: true,
                        formatter: function (params) {
                            return 'Job: ' + params.value;
                        }
                    },
                    labelLayout: {
                        moveOverlap: 'shiftY'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    encode: {
                        x: 'month',
                        y: 'value',
                        label: ['Tăng trưởng Job', 'value'],
                        itemName: 'month',
                        tooltip: ['value']
                    }
                }

            ];

            const option = {
                animationDuration: 10000,
                dataset: [
                    {
                        id: 'dataset_raw',
                        source: rawData
                    },
                    ...datasetWithFilters
                ],
                tooltip: {
                    order: 'valueDesc',
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    nameLocation: 'middle'
                },
                yAxis: {

                },
                grid: {
                    top: "25",
                    right: "0",
                    bottom: "17",
                    left: "25"
                },
                series: seriesList
            };

            var p = document.getElementById(id);
            echarts.init(p).setOption(option);
        }
        const rawData = {
            month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            employer: $('#employer-job').data('employer').split(',').map(v => parseInt(v, 10)),
            job: $('#employer-job').data('job').split(',').map(v => parseInt(v, 10))
        };
        buildChartEmployerAndJob('employer-job', rawData);

    </script>

    <script>
        $(document).ready(function () {
            $('#filterForm').on('submit', function (e) {
                e.preventDefault();
                let fromDate = $('#from_date').val();
                let toDate = $('#to_date').val();

                if (!fromDate || !toDate) {
                    const rawData = {
                        month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                        employer: $('#employer-job').data('employer').split(',').map(v => parseInt(v, 10)),
                        job: $('#employer-job').data('job').split(',').map(v => parseInt(v, 10)),

                    };
                    buildChartEmployerAndJob('employer-job', rawData);

                    const rawDataCvAndCtv = {
                        month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                        value: $('#warehouse').data('value').split(',').map(v => parseInt(v, 10)),
                        dataset1: $('#warehouse').data('dataset1').split(',').map(v => parseInt(v, 10)),
                        dataset2: $('#warehouse').data('dataset2').split(',').map(v => parseInt(v, 10)),
                        dataset3: $('#warehouse').data('dataset3').split(',').map(v => parseInt(v, 10))
                    };
                    buildChartCvAndCtv('warehouse', rawDataCvAndCtv);
                } else {
                    $.ajax({
                        url: '<?php echo e(route('ajax-dashboard-filter-date')); ?>',
                        method: 'GET',
                        data: {
                            from_date: fromDate,
                            to_date: toDate
                        },
                        success: function(response) {
                            const formattedData = {
                                month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                                employer: response.employer.map(value => value === "" ? 0 : parseInt(value, 10)),
                                job: response.job.map(value => value === "" ? 0 : parseInt(value, 10))
                            };
                            buildChartEmployerAndJob('employer-job', formattedData);

                            const formattedDataCvAndCtv = {
                                month: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                                value: response.cv.map(value => value === "" ? 0 : parseInt(value, 10)),
                                dataset1: response.cv_selling.map(value => value === "" ? 0 : parseInt(value, 10)),
                                dataset2: response.submit_cv.map(value => value === "" ? 0 : parseInt(value, 10)),
                                dataset3: response.collaborator.map(value => value === "" ? 0 : parseInt(value, 10))
                            };
                            buildChartCvAndCtv('warehouse', formattedDataCvAndCtv);
                        },
                        error: function(error) {
                            console.log(error);
                        }
                    });
                }
            });
        });

    </script>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Projects\HRI\RecLand\resources\views/admin/pages/dashboard/index.blade.php ENDPATH**/ ?>