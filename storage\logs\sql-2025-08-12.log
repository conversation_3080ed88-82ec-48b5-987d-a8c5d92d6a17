[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":61.1} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":4.11} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":3.39} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":3.84} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":4.05} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":3.46} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":4.21} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":3.85} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":5.96} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":2.51} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":3.8} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":3.31} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":3.46} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":3.72} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":3.36} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":3.42} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":3.41} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":3.96} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":3.98} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":3.54} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":2.51} 
[2025-08-12 08:26:43] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":5.11} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":3.83} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":2.63} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":3.49} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":2.68} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":3.3} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":3.54} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":2.49} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":2.69} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":2.4} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":2.36} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":2.34} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":2.12} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":2.1} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":2.1} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":2.38} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":2.06} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":2.1} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":1.81} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":1.89} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":2.22} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":3.45} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":3.54} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":3.65} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":4.85} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":3.14} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":2.95} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":2.62} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":3.02} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":1.83} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":1.64} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":3.9} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":2.79} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":2.65} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":2.76} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":20.89} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":1.63} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":2.59} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":2.54} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":1.7} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":1.63} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":1.33} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":10.01} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":1.72} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":1.88} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":1.76} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":1.66} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":1.67} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":1.81} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":1.81} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":1.79} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":1.66} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":1.63} 
[2025-08-12 08:26:44] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":1.65} 
[2025-08-12 08:27:07] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local', '', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6Inh5dGVlenIvRU5NQnNkWmIyNlJOQVE9PSIsInZhbHVlIjoiRmJEU09vcUVLc2RsQnU0YlpQMlRlVXRaVFVON2pWQUFlQVRPZU1CbVF4QjU3RjVJL3pyNjF4cFBTUzZIVDRNVUMyc09LYld2N3ZyUm1VOTlkYUxMU2xUU1hFQjJ5bS9oRGQ3dXJBRmIxa0hnWmRLSW1USVY3QUNqNFJ4dGh1bjciLCJtYWMiOiI5ZmIwOTMyZTEyODUxMjczNTYzYjQzNWUxMzZmY2YyYTAyOGI0ZmQ5YTYyZWVmMzcyMWI2ZjcxZGRkZmMyZjAxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im04Y1phdDg0bDNQQkVZalp4MW5ySkE9PSIsInZhbHVlIjoiV2R5ZmpXLysxRzltYklQbXlWNUh1SkEwanB4NVR3aGd2b0w0ak1vTG1lbSszZnZLeFFlb2xJVVlNMzRJNFhnWWJjWHo4dDBQVDl4QjdTZTBrYlJMRmk3S0RYUEJzOHdJSGVuUjN1b2dmeTNTbEdleGQ2MUtGWW9UTlRNWWp3UzIiLCJtYWMiOiIxNzM0YjE0MTBmYWIyNzkzZjU3OTBlNGM3ZDI5ZjViZmYyMGM3YTMwYTZiMmVmOTY1NGQ1NTE3Y2ZhOGZjYzFiIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s**********$o1$g1$t1754909515$j60$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:06', '2025-08-12 08:27:06')
-- ","Time:":22.78} 
[2025-08-12 08:27:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'home' limit 1
-- ","Time:":0.62} 
[2025-08-12 08:27:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '0' and `is_active` = '1'
-- ","Time:":0.94} 
[2025-08-12 08:27:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` order by `id` desc limit 1
-- ","Time:":1.56} 
[2025-08-12 08:27:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.69} 
[2025-08-12 08:27:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '512' limit 1
-- ","Time:":0.67} 
[2025-08-12 08:27:15] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"skill\":null,\"lang\":\"vi\"}', 'http://recland.local/job/api/list', 'http://recland.local/', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"24\"],\"x-xsrf-token\":[\"eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0=\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/plain, *\\/*\"],\"content-type\":[\"application\\/json\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s**********$o1$g1$t1754909515$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IktsZHNpbWFNNVVSY0lidmt4UWZDZ1E9PSIsInZhbHVlIjoiaExNNUhpMTFYb3d0bkdHWUIvank1eW0vM1BwZ2cwTVBvVEhzQ1lmWnR5TytoL2M1aGtTMExZOXdEU3NMbThDTU15OFp3VUUvRUdZTG1CSFZKUVRuK0ZwRFlLa0czSFZFTlEwZTB4MDRybjVoZ2p0SlFrbHliSVRHU2JweUJZdHIiLCJtYWMiOiJlY2VmZTcwODM2ZmM5NGVjMzY2ZTVlYzVkMDJhMjk0MWMwNjkxMWU2OGYxZGQyOTU4OTZmMTBmZGUzZDE4MjlhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:15', '2025-08-12 08:27:15')
-- ","Time:":25.0} 
[2025-08-12 08:27:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = '1' and `job`.`status` = '1' and `job`.`expire_at` >= '2025-08-12' order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30
-- ","Time:":34.47} 
[2025-08-12 08:27:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` in (3, 187, 234, 274, 277, 278, 286, 487, 696)
-- ","Time:":0.6} 
[2025-08-12 08:27:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` where `job_meta`.`job_id` in (631, 700, 710, 818, 819, 820, 821, 822, 823, 825, 826, 827, 828, 838, 861, 865, 866, 1642, 1643, 1644)
-- ","Time:":1.26} 
[2025-08-12 08:27:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select skills from `job` where `skills` is not null and `skills` != '' and `is_active` = '1' and `status` = '1' and `expire_at` >= '2025-08-12' group by `skills` order by COUNT(id) DESC limit 8
-- ","Time:":17.35} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1644' and `type` = 'save'
-- ","Time:":0.52} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1642' and `type` = 'save'
-- ","Time:":0.39} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1643' and `type` = 'save'
-- ","Time:":0.37} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '631' and `type` = 'save'
-- ","Time:":0.33} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '700' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '821' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '865' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '866' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '861' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '838' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '825' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '826' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '827' and `type` = 'save'
-- ","Time:":0.39} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '828' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '818' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '822' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '823' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '819' and `type` = 'save'
-- ","Time:":0.32} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '820' and `type` = 'save'
-- ","Time:":0.43} 
[2025-08-12 08:27:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '710' and `type` = 'save'
-- ","Time:":0.31} 
[2025-08-12 08:27:48] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/logout', 'http://recland.local/', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g0$t1754962034$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImhZZVl6d29ZdGlFQ2pZSytQQUhEa3c9PSIsInZhbHVlIjoibXZ6b2lGUlFmazlqYjJYWVRFZnErSWhnazBrcnJzTXRPUnVwTDJNRUMrOXFaV0VJdHZUY3NTTzllVDFWRTMrT3JZSlU1VXUzZEtiZlU3Uk9SSzRzM2Q2K25PcVpWYUpveVg5UE5sQWprVWFNSCt6RHRrZjN5cHdMUURhN0JnUGUiLCJtYWMiOiIxYmIxNzFiZjBmMTM4YTY4YTU2NDg5NGM2N2M2NzliMDA3YTU2NGMwNmMzNDEyNDMyYTQ3NTkwMjAzOTJmYjVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlUvbHA4RXNrZWxxbnlHVk5pV2NkZ2c9PSIsInZhbHVlIjoiYmdyMGl3YmwzNkNNOWVqYWU4ekxGakk0N00yTzl1RXorLzUxWEVBY0tkaXk1eWdMSmY3bFJhRG1vdUgvTkk5cXNmWG9JM0tMQmNXTjBmQjA5SUpENDJJdktiUnduTU5ITlpHOHpjOGFMWDlPRWdDZ2RjNS9keDhFWFQwUTVETloiLCJtYWMiOiI1YTJhYTRkYjkxMGE4YTM1N2EzZjM5MzEwMzkzNTQyMDBjZDdiZGExYWQzZjE3MGVjNmY5NGJiYWE0ZTkwN2Q1IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:48', '2025-08-12 08:27:48')
-- ","Time:":2.52} 
[2025-08-12 08:27:48] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '512' limit 1
-- ","Time:":0.74} 
[2025-08-12 08:27:48] local.DEBUG: [SQL EXEC] {"SQL:":"
update `users` set `remember_token` = 'yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8' where `id` = '512'
-- ","Time:":0.44} 
[2025-08-12 08:27:49] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.65} 
[2025-08-12 08:27:49] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\"remember_token\":\"ofX19AAMB01Dk38VNDGTNlfq1ZGTjU2D3OupM89jd8tKo1029azwcdFVUtBd\"}', '{\"remember_token\":\"yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8\"}', 'updated', '512', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://recland.local/logout', '2025-08-12 08:27:49', '2025-08-12 08:27:49')
-- ","Time:":0.65} 
[2025-08-12 08:27:49] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'rec-login' limit 1
-- ","Time:":4.06} 
[2025-08-12 08:27:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.52} 
[2025-08-12 08:27:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":27.26} 
[2025-08-12 08:27:52] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer', 'http://recland.local/login', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/login\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6InBJL0s5S1R5UTlQSUw3ZHl0OEJSK3c9PSIsInZhbHVlIjoiempNNG41Zms5ZXpPS3NGNFUwR1JPR0krcUZoOVdiN1NxS0dUdmtZWkpwVjMrT0h2VjhuMURFTDlzTXNsYUw4NmcwUVFaVmswNlRuOVZFS0pSYzBTam9lMWUvRzY1SVh2T3FScEpYdlFVN0RYVzdsUVF5WnY5dnA4c1dwWU5zVjYiLCJtYWMiOiI1YmNlODdhNTRjZmY1Zjc3Y2YxMWNiOGM0N2ZlMWRhZjY5N2NiMjYwM2VjNzEzZmRmYThjMGFlYTQxNzhhZmI0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlJqbXNUQlRTL0hVYjlKWkFPM21scFE9PSIsInZhbHVlIjoiNXFKNnJ2R2J2NWhnTHNMTDBYVGh5aDVmdWd3UjJUN0JxTGVEckpOQllqTW1WNTc2NE5JM2IyWUlRQ0l6dExQZWcyUG5PVXdOeHhvYmljZDlNdWMza2JLQlE5Z3Jub2NuaEdjd296c3Y4VUJEbUQvZE92NlY2WVo4QzE2K1VUc1AiLCJtYWMiOiI3NmE0NjE0Yzk4NzIwMzA3NGIzZDEzMmQyMWE3NjM0NDFkYzNjNzQ5NjU2NWJjNzRjNzA3ZWRlM2JiZGEwNjQxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962070$j24$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:52', '2025-08-12 08:27:52')
-- ","Time:":0.55} 
[2025-08-12 08:27:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-dashboard' limit 1
-- ","Time:":0.43} 
[2025-08-12 08:27:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'
-- ","Time:":0.41} 
[2025-08-12 08:27:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'
-- ","Time:":1.58} 
[2025-08-12 08:27:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` where `type` = '1' and `is_active` = '1'
-- ","Time:":0.4} 
[2025-08-12 08:27:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories`
-- ","Time:":0.62} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\",\"email\":\"<EMAIL>\",\"password\":\"02042000\"}', 'http://recland.local/employer/login', 'http://recland.local/employer', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"98\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6ImZValJBTmQ2ZW1HNHlZcCt0WW5TbFE9PSIsInZhbHVlIjoiYVBra05HekhUOVhsbURxMnkwRFBnMW1ZRDE1VnUvdkpWeWVtUkhQZnUxd3pSWDdvSng2ektZM0luKzJQTlB1UTd3UERObURmNTA5ZGtGTjRpaXZ1WFNUNVhZTnQwVDl5RTZ5WUVNZXdXWktRTTVVMGZUV25wU0RKNng0TU1USnoiLCJtYWMiOiI5YTQ3ZWMwYWI3YTJlN2IxYjFkNmI0NWNkYTg1NDM5NzhiMjc4Mzc1N2Y0NWU1OGNhZDNlMzM0YjljYWUyODUzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InRudWFPNmtiVitBQy9vbm1qRnFreFE9PSIsInZhbHVlIjoiN25Yb3VKSlJrSVdUUW55ZHhiUGpVVzk3NlJnQ3ZaL2xHT1VMT1N6c29zNkJxTzFNSFVQbS95S1l0VklXK3k5amRPMlJIYXFvdkR4dGM5MVFyRnMrV3FsVy8zZFVycTFRZSs0enN6MHhOL3FXMUtkTGdnanpxMnRQMGVhR0E0VEQiLCJtYWMiOiIzOTI3NjA4ZWE3MDc4YzRjNWI1Y2E1Y2ZiMWEwODliZDhhN2I3ZTYzN2UyNjk5ODg0YzBlNmVhNjMyMmU3M2Q4IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962076$j18$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:57', '2025-08-12 08:27:57')
-- ","Time:":26.05} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1
-- ","Time:":1.86} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' and `is_active` = '1' limit 1
-- ","Time:":0.69} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
update `users` set `last_login_at` = '2025-08-12 08:27:57', `users`.`updated_at` = '2025-08-12 08:27:57' where `id` = '1723'
-- ","Time:":0.58} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.57} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\"last_login_at\":\"2025-08-11 15:15:49\"}', '{\"last_login_at\":\"2025-08-12 08:27:57\"}', 'updated', '1723', 'App\\Models\\User', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://recland.local/employer/login', '2025-08-12 08:27:57', '2025-08-12 08:27:57')
-- ","Time:":0.5} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
delete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\Models\\User' and `personal_access_tokens`.`tokenable_id` = '1723' and `personal_access_tokens`.`tokenable_id` is not null
-- ","Time:":1.85} 
[2025-08-12 08:27:57] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('Personal Access Token', '98d6f485f8a7f500d6d114f5734f1d14c5332f7245c0dc5799ef1cda0cec1273', '[\"*\"]', '1723', 'App\\Models\\User', '2025-08-12 08:27:57', '2025-08-12 08:27:57')
-- ","Time:":0.72} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-manager-dashboard' limit 1
-- ","Time:":16.62} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/dashboard', 'http://recland.local/employer', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962076$j18$l0$h0; XSRF-TOKEN=eyJpdiI6InZwdDNVeG1jdzQxc25BSHBsT0NDQVE9PSIsInZhbHVlIjoibDM1ZkNKZmEyc0lVVG9uRUhrQnc2YWd3SFJpSGJTdWg4K1hkQ3dFdGJOWVZIWEYxbUlDakJTSEhNY3VOeDF1VDVpaW9PZEVRSjJadC9SRENpU3F4azVWOUM3czZnS1lmbDNMU1ZLT2p3dnZCMlJKOG5McFN1bkl2QTdBdXZGNjIiLCJtYWMiOiIyZGM0OTFmZTBiYThiMWU0OTUyODEyMjllZTUwNDI2ZjM1ZmRkNmQ4NDFmNzBmMjAwMDYxY2QyYjcxNGE2YjYzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJhd1duMWU0b2JDWWhvdG1KTW0zQXc9PSIsInZhbHVlIjoiMWQwUHZwNmZ3bnJLeWJqMFdMTiszYm9tV1N3ZDJHNWFFQXgyUXlkcjZZMm41cGNMWFRjUkJscHVuVDd2UC9VYkR4UzB5VmZMOHhFMDdaUncyRktrL21QcHczemJMcTRxRkdIODhMaG81UkxUbHlYc1ZUaDdFY2tXdFlWNVErdWQiLCJtYWMiOiIwNjRhZTI4NmU2OWJlOTk3OGQ1MmFhMzMyMjVlNTIzNTIwYTQzNjhkMThkY2RiZmQwMzljOTNkNjg3OWNkMDZkIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:58', '2025-08-12 08:27:58')
-- ","Time:":0.56} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.59} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.76} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.28} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '1723'
-- ","Time:":7.7} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `status` = '5' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1'
-- ","Time:":3.81} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `status` != '7' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1'
-- ","Time:":26.75} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '0' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.83} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '1' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.88} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '2' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":2.04} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '3' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.79} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '4' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.8} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '8' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.78} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '5' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.78} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '6' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc
-- ","Time:":1.81} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-manager-dashboard' limit 1
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1'
-- ","Time:":21.23} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `user_id` = '1723' limit 1
-- ","Time:":1.83} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `deposits` where `user_id` = '1723'
-- ","Time:":0.25} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` where `user_id` = '1723' order by `id` desc limit 10 offset 0
-- ","Time:":0.31} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`amount`) as aggregate from `deposits` where `user_id` = '1723' and `created_at` between '2025-08-01 00:00:00' and '2025-08-31 23:59:59'
-- ","Time:":0.5} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`amount`) as aggregate from `deposits` where `user_id` = '1723' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59'
-- ","Time:":0.4} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '1723'
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` where `user_id` = '1723' order by `id` desc limit 10 offset 0
-- ","Time:":0.48} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`id` in (18, 25) and `warehouse_cv_selling_buys`.`deleted_at` is null
-- ","Time:":0.52} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`id` in (1372, 50624) and `warehouse_cv_sellings`.`deleted_at` is null
-- ","Time:":1.28} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` where `warehouse_cvs`.`id` in (2084, 57585)
-- ","Time:":1.41} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '1723' and `type` = '0' and year(`created_at`) = '2025' and month(`created_at`) = '08'
-- ","Time:":0.34} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '1723' and `type` = '0' and year(`created_at`) = '2025'
-- ","Time:":0.3} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.44} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.33} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.42} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:27:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":26.86} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job', 'http://recland.local/employer/dashboard', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/dashboard\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6ImtuMFBSTzdTYjBsVUdlRkp3cnl3ZXc9PSIsInZhbHVlIjoidjZ1UTc3OHVLaUxOTHBHbi9LaWJSMCtaazdiTjh6WGJIeFNvbjYzYlVLUEEvaU5hQkt6dDN3UVI4SlF6a1V4K0RUZXBxY0wzT1NnRXcvU3FLRCtWYW1LM2tDR3J5NXc4aFBLSVBGb2ZROEpZMnVRYUg5cFZoU1RRYzY1bDJZeXgiLCJtYWMiOiJkOWRjZTYxNTZlNDAxOWY4MTU3ZTMyNWQ4OTIxMThkMTE1ZThhOWNiY2NhNmMyYWMxOGVlMmNhNTZkZTlkMTViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImRDbzlYSmhxQmJzVjZNUWdmOWhuTlE9PSIsInZhbHVlIjoiUm1FYnBCdldESWFxdm1QRW9MK3NOSWhSZkUyY1JvbmpoNWNOWTdNRC8renlEQVNzWGZpZm5RODR1SThtcjJhOUUwcHhwVVVqM0E1UDZCWi9QKzVkZ2NtRzg1NEIvcWgrdzA4aXp6TE1DNVhXWEkyTzhqbHBaUndXdUFCYXNYQVkiLCJtYWMiOiJmODI5MTg4N2M3MDkyOTU0MTA1NDA1NjVhZWZjNDAyYjNhMjZlNDdhZWU1ZTA2MzU2MjJjZWFmYjkwNjUzM2Y5IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962079$j15$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:01', '2025-08-12 08:28:01')
-- ","Time:":0.44} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.68} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.63} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.28} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.85} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":0.35} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `employer_id` = '1723' and `company_id` = '84'
-- ","Time:":4.0} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `employer_id` = '1723' and `company_id` = '84' order by `id` desc limit 10 offset 0
-- ","Time:":9.89} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` where `submit_cvs`.`job_id` in (483, 1645, 1646, 1647, 1648)
-- ","Time:":5.15} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '1723'
-- ","Time:":2.9} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '0' and `employer_id` = '1723'
-- ","Time:":3.39} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1648' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.48} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1647' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.44} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1646' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.44} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1645' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.43} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '483' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.41} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.31} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.38} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.36} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:28:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":22.52} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6InRsVG9BNzQ1OFhoWThxQWZlVDRkY3c9PSIsInZhbHVlIjoiRDJheE9Yd0x0NFFwSVFDU0Y5dkFzTlQ3Y2RVTFd1SXNzbTByTENtNUIxeElScVlndForUXFFMzE2SGNhSDZVMEhwek5LdjZweG9FZkpoTWZYSzNkSXdwT1ZPaEVkUHBKWDM5K0ZVaU5LbTE5Y3BLVkZ4dzJLa0hPY0c2dW11ZEIiLCJtYWMiOiI3Yjc1Yjk4NTFmOWRmNDliOThhNTQ0YjVhMzc2YjVkNzE5OTJiMmNkMjdkZmQyMjI0MzUwZWJjYjQ2ZDU4ZWVhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkFrdExSTys2Zm1TZ2dRR0J5ZW5nSUE9PSIsInZhbHVlIjoieFh1c3EwVFM0Y0V5dSttRFlFbEp1d0tnZTdtYllrdFVQc2JHRm96dkROKzBOTFpRaytlMmpTY2hiSnVmSXRLWUxuamk4Nkp6VlpsQjlCTVhSNlBqanhTQ0pqaHJsUkplQjN0elRpY3FsbllqUFlzRjhqb1pyNWJSSlgwRExSVmYiLCJtYWMiOiIzY2U4NjkyMzEwY2RjYzAyZDFhN2ZiMDQ2MTZlMGVkODJjMmZhOGYyYTA1NWI1OGMzZjhmMDRmMTRmNTcxNWZlIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962081$j13$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:05', '2025-08-12 08:28:05')
-- ","Time:":0.44} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.47} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.53} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.25} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.73} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.55} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.47} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.44} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.73} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.77} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.53} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.49} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.37} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.44} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.42} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.49} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.52} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.49} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.64} 
[2025-08-12 08:28:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:28:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":13.05} 
[2025-08-12 08:28:09] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6IndPdjRhbEZ2enduNEUrY3ZrSkFCalE9PSIsInZhbHVlIjoiMXNCNWJ3NW1ndDhFWk9VdWFKaE1zMG1DeDZGUWxucnNVVXNDdmhMUk84ZFZzcDFTZk9tL1NxYmJmekY5dUx6K01KaGlwa0lIdVhlVFBqb3ZXNG8yeUk3NEk0NE12dGZRUGJtemh6QzBJSFQ2c0VtVFpGd2RvZVo4cEVSczZoZisiLCJtYWMiOiJmMzhkYzhiYTgzMDA1MTFmZWNhMDIzYWJiMjQ3N2RhZjNiMjIzMTZiY2ViMWU5MGVmZTczMjExMDhmNTAwM2YzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IndzWXl2VVBXUzhYdEdhTkptQWl6Ymc9PSIsInZhbHVlIjoiYWMwdUdWWEkySW93VkxRSFNlTm1EYXk2eElDWXkzZFVvNDhFVHRlMFdXeGthdng5NGkrcnRFWG94UEdNejlROWl6dTFyRmNEaHIwOFpTWWtFZTZMQyt0ckovUjhmU3BWZTBvbFc4VUhqb29ZbnZwS05NWm9jclZySnJ0OERreE8iLCJtYWMiOiI5YzBkZWZiNTEyODY4ZDM4MjNkOGJkOTgyZDdjN2Q0OWVmY2ZmMTIzZDQ5YjlmYTY1MWY1M2U4YWMxMTNmY2ExIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962085$j9$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:09', '2025-08-12 08:28:09')
-- ","Time:":0.55} 
[2025-08-12 08:28:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.57} 
[2025-08-12 08:28:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:28:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.25} 
[2025-08-12 08:28:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.17} 
[2025-08-12 08:28:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.3} 
[2025-08-12 08:28:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":24.66} 
[2025-08-12 08:28:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":14.73} 
[2025-08-12 08:28:21] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE202\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"62\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6ImhJdHRYQXNvdHJYdmxJRDk0YnQ5YkE9PSIsInZhbHVlIjoiUnNVQ0hFV000dHp5d1FkRitQQ1p1bzRmZW1kTUtPdWR1QUZUL1ZNZFhmYlliWko4bkE0MVlPNjI3ODRXdmhsSm1aV3d2K0J4R25pTlhEWC93dVNMN3I3ZmticjNoSjI4V3IrM3hvekp0WG13d1JxQ00xSXM0WmNSczV1RFh5Z0kiLCJtYWMiOiI0NGFjOTQwYWRlODkyZDFmYzI2MmQ4NGIwZWMxMDYzMGVkNTg2N2Q2ZTRiMTliNWI4MmJmOGZiYjc2OWJmMGZjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImFHV01mRnNRc2JDOWl4TFZSTWxCVkE9PSIsInZhbHVlIjoidHp3V0tXRGp0d1NWZ29lZnVHTnhLZzlUdVliZGQ1WEk2VFluN09lK0VJclhwWCtMMitsdEFYbWFFODNaYW4rU0RxaGxaZ2VNWkc1M2dXL09Kb2dlRVZySDBuV0NCQk5JbUttZnZKVHhPR05kZU1CV0g0a05QWkJOTjYwZmV0VXQiLCJtYWMiOiJlY2UzM2JmMDcwNDIyMzJjYzNlMTYzYWIxODI0NmE1N2Y5ZTFlNDQ4YWM5ZGIzODFlOGUyYmQ2NzRiYTZmZDExIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:21', '2025-08-12 08:28:21')
-- ","Time:":0.66} 
[2025-08-12 08:28:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.59} 
[2025-08-12 08:28:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:28:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:28:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.21} 
[2025-08-12 08:28:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE202' limit 1
-- ","Time:":0.39} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":22.35} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6IjgyOEY1RisweGdPbENYMHUxcm1aVFE9PSIsInZhbHVlIjoiOFY4WTJkNWttaVY1eGFSQkszaTI5NC9IdEl4VkhsSVRIOVpzWWpMcXpOYVlraHhVV0xHVzZoY3dPWTE4ZnhzaXVFb0lQOXprdDQ5dWdEUE0vUG40RHV6MmJTakpjYUE4TklXanE5SWZTWXpVa0ZQMlFYRE92bXVCTDZtL3EzTG4iLCJtYWMiOiJjMzM1YzM5ZjliZDcwYWM4MzI2Njg5MjEzZGEyYmQ2NGY0ZmM4OTk2ZTAyMTVjNTY1OWE1Zjc4NzIyMmE2YjVkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik4xaUtIZFdwUkEwaW5USW1LUjMvK0E9PSIsInZhbHVlIjoiVW9NMU8vNjVBZzFDekVDZExvUlVKTkRrL1BGQStva0t5eXVYRzdjVnBGT3E1UzVTc2dOZnJTSE1RR21PZ1NFZWlFTHhLcE52anN2YnhNWlloVXU1NDVOZDhwamk0ZmF6UlliQy9UQWNvcnZWQ0ZsR2ZidGxPbjVTU0g4RFNsdVMiLCJtYWMiOiI0NTMzM2M4MDVhMTUzODVhOTQyZmNmYjg5NDY5MGE5YTU2NWZkYzExNzk4NTFhOTRkNzc0Y2U1YTg3YTZhZDI3IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:24', '2025-08-12 08:28:24')
-- ","Time:":0.53} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.57} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.12} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.41} 
[2025-08-12 08:28:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":35.62} 
[2025-08-12 08:28:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":20.24} 
[2025-08-12 08:28:42] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6Im1YQUlzejYxb0kwd3U4ZEhDZ3dsS3c9PSIsInZhbHVlIjoid1JCdUlDb2daZzlsS1lucFRSdnd0M2pZbFpBdVRjbTRIb2s0RytCeThLdzJXcXVGcndCOUNoWEdLTkU3TFdLcFdGbGIwa2JSVlhMRG9OUzg0VFk0a1FNc21iQXBGQ2R5NlREeGdXTERGcWcwN0FQcWFHY1hpZWxMbU9ETEFlQVAiLCJtYWMiOiI3ZDdiOTdhYWMzOGNkMWE0NDEyYThmNWY4MjRlZjkxY2U4Njk4NWI4NGQyZjk1MWFkY2IzZThmNDVkNjc2MjJjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjRCRnQ4Z1B4Sy85MjRVYk1JbS83QWc9PSIsInZhbHVlIjoibGMxdi9XMGJ4VUpMUS94T0E0NEdlWnZ5SHpxWGd0KzhXcS9QaXJjMWloOGNnWmJzZ21oY0F5cnhoS3FvdnRlRitzaVNmQ2J6TU1KY1g1aVZMR2QxK3NFSlJyNTZnaWJEOFBzSDBTRHRYUHVBRXVDK2duTmo4Q2JHN2NzT3B5blgiLCJtYWMiOiJlODhkYmIxNWZhNTBlODcxZWYwNGRiYWZjNGZjNzA4YTNlMGNiZmIwOWZhMmIzMDBmZmMzZmQ1ZTA0YWQ4MDQ4IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:42', '2025-08-12 08:28:42')
-- ","Time:":0.56} 
[2025-08-12 08:28:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.58} 
[2025-08-12 08:28:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:28:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:28:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.13} 
[2025-08-12 08:28:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.4} 
[2025-08-12 08:28:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":41.79} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":19.58} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6IjYxTEhmSjVldEVpN3RQQmxaaXZhbkE9PSIsInZhbHVlIjoicmNNQVpiSlQ5TlZnQlZ0MUlvdWhjN3JzdUxLSzBkTktNSUM1djR0T1dKNEhKeExabEY4Y2daS3ROSUpYM2tVMGRHZUc2REpSY29yR1lrd3E3bTFDNnhhWUZ2ZEZXRkhjNVU2c2dpa29CSElGN1ZyclBMemFTL3ZraUYyLzQycjgiLCJtYWMiOiJmZDhhMThmMzhmMGNkMTlkY2UxM2M4OGVlMmZlYjY0MTA4NWNlY2M4NjFhOGFhY2M0ZWZhNjMzMjQ0NDM0MDIxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlgzbU9HeE05bDY2QW1iNVlJbHVJS1E9PSIsInZhbHVlIjoiNThNZTRwd1VDMmt4c3RoWlB1dWpjeDJrQmFtQXVYdE5hUGFBWWRNTndpdkZwaUthTzYzWmVKcytoMW0zVnZzQWloWGtqbGI4a2k4ckFHdytGM2F6Mm9qS2IvOWhnSlhqeXhPQUxCWDR1d3p3SVRJVi9SZ3ozajRhOERzMGRhZlUiLCJtYWMiOiJhYzBiNWMwNzZhODY0MTRlZjY1NTdiOWI2MjFkMzBiMDg3ZTdiZGY2ZTczOTMxZWM4Y2Q3YjVlNzgyZmU5NjJlIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:51', '2025-08-12 08:28:51')
-- ","Time:":0.56} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.46} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.56} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.26} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.58} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.32} 
[2025-08-12 08:28:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":41.96} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":24.51} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6IjMrWU1FYkhsSlJLYXdrS3J4SWpKemc9PSIsInZhbHVlIjoiL0hteFZWNFJGUnNtY3VEZ0lRR3pMZDhyNXU3M2Z2T1pkQS9QTVk3TlAzMVFVSjRpZ2pIMElZSVk4UFNEdGM3K1RtTlEybzBQcHZaZnRHTnBKbVBHVXJrODQyTVhtbDgycHFvTHZNNTdjalFhUUpuQloyNnVaVUF1Tlo5ZmlxTVMiLCJtYWMiOiJjZTM3MjYyNjgxNjU4MGVjMWEyYTlhOGMyOTYwNDcxNjllZjE0MjFmNjNlZWMxN2MzOTI0MGI3NGQyOTg0ZTA0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlFTcW4vOS9lRy9aaGYydUZleUNYd0E9PSIsInZhbHVlIjoiRjhrSjZsMWJCVG16ZUVzL1FJdlpBZjlpZENmQ0crMi9yMjF1WEJ3QmxBMGxlWUFQU2ZYOWxKNnZlNnkrVk5jbWlCVVpMNHVjc1BJOWNVT1lwNWJYZEZvd2cwTFlPdXE1QkVZenZuYUNyOUM2eVZibWVXbVI2WHhIT1ZBWEtndzAiLCJtYWMiOiI0MTE1MTExYjYyMzZmZGZhYzkzMjMyOWYyMGM1NWE5MTM0MGZiZWQzNGU3NTY4NWFmOTAwZmJkNTI1OGFiZTU0IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:30:11', '2025-08-12 08:30:11')
-- ","Time:":0.43} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.42} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.43} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.22} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":1.84} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.27} 
[2025-08-12 08:30:11] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":21.49} 
[2025-08-12 08:30:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":21.75} 
[2025-08-12 08:30:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":22.94} 
[2025-08-12 08:30:15] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.55} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'employer' and `is_active` = '1' and `is_real` = '1'
-- ","Time:":23.5} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1'
-- ","Time:":9.38} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":93.16} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":14.12} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `companies` where `is_active` = '1' and `is_real` = '1'
-- ","Time:":3.93} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cvs` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":52.27} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select (count(id)) as total, (DATE_FORMAT(created_at, '%m')) as month from `users` where `type` = 'rec' and `is_active` = '1' and `is_real` = '1' and `created_at` >= '2025-01-01 00:00:00' and `created_at` <= '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, '%m') order by DATE_FORMAT(created_at, '%m') asc
-- ","Time:":9.13} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `warehouse_cv_sellings` where `status` = '0' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' and `warehouse_cv_sellings`.`deleted_at` is null group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":69.6} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.59} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, \"%m\") as month from `submit_cvs` inner join `job` on `submit_cvs`.`job_id` = `job`.`id` where `submit_cvs`.`is_active` = '1' and `job`.`is_real` = '1' and `submit_cvs`.`created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(submit_cvs.created_at, \"%m\") order by DATE_FORMAT(submit_cvs.created_at, \"%m\")
-- ","Time:":11.18} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `users` where `is_active` = '1' and `is_real` = '1' and `type` = 'employer' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":8.02} 
[2025-08-12 08:30:16] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(id) as total, DATE_FORMAT(created_at, \"%m\") as month from `job` where `is_active` = '1' and `is_real` = '1' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59' group by DATE_FORMAT(created_at, \"%m\") order by DATE_FORMAT(created_at, \"%m\")
-- ","Time:":6.76} 
[2025-08-12 08:30:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job`
-- ","Time:":2.83} 
[2025-08-12 08:30:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1'
-- ","Time:":4.23} 
[2025-08-12 08:30:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":4.03} 
[2025-08-12 08:30:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.88} 
[2025-08-12 08:30:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job`
-- ","Time:":24.64} 
[2025-08-12 08:30:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1'
-- ","Time:":8.1} 
[2025-08-12 08:30:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":7.54} 
[2025-08-12 08:30:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` order by `id` desc limit 25
-- ","Time:":1.04} 
[2025-08-12 08:30:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` in (84, 217, 286, 487, 491, 693, 694, 695, 696)
-- ","Time:":0.54} 
[2025-08-12 08:30:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` in (1723, 4629, 5421, 6139, 6156, 7372, 7412, 7415, 7456)
-- ","Time:":0.55} 
[2025-08-12 08:30:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `job`.`id` from `job`) as `job_aggregator`
-- ","Time:":0.24} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":21.94} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.87} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.81} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops`
-- ","Time:":0.8} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.39} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.27} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `group` = '1'
-- ","Time:":0.33} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.44} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` where `job_id` = '1648' limit 1
-- ","Time:":1.7} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` where `job_id` = '1648' limit 1
-- ","Time:":2.64} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.48} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.37} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":14.25} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.34} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` where `user_role`.`user_id` in (1723)
-- ","Time:":0.25} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.56} 
[2025-08-12 08:30:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.33} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":3.22} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.44} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_id` = '1648' and `parent_id` is null and `job_comments`.`deleted_at` is null order by `created_at` desc
-- ","Time:":0.8} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"onboard\",\"level\":\"2\",\"career\":\"1\",\"salary_min\":\"1000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"undefined\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=onboard&career=1&group=&is_it=&level=2&salary_currency=undefined&salary_max=20000000&salary_min=1000000&skill_id=2', 'http://recland.local/admin/job/1648/edit', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/admin\\/job\\/1648\\/edit\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlhFVHd1azlab1AwaU9BUHhSWXU0N2c9PSIsInZhbHVlIjoiR2ZWOS8xZkR5UTUzeXVkZHVmeU5UUzZKRWI1NTRnTGZXeXBtQzZSdjg3aitzS1ZybDE4SGtJbldSbEIrUjlWejl3Mi9QM0gvNG41YUZXSU9jVkptR3d6NCthbDE4a0ZXVTBYVHgremVJUXBzMEVodFc3T1ZRN0Z6QlIxQzFLeWUiLCJtYWMiOiJmYWNjZmI4OTAzMTdmYzJjYzhkMDFmODA0ZjBjYjAxMTM3ZDdjMGZmNTQ4MDQxNTNmZWU1NTUyNGQxZGVhNjY5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkM1cU1BY3piOElNK0xqbGFSWko3L0E9PSIsInZhbHVlIjoieHFWaHA3ZGd2YmFVWS9EQk01TXV5N1RMU1ZWR1c4WUlQeGthbWMvZ1FSa0VCcVRpVEtkY3Y4ZTErNXRpZDlzS1VyQlhlaTcwNy9zOENaN2tpY2k2S3BmeUxHdTN3UjFnV2VSM0Z2NjVjbVlnR0tzMkVRYWJYT0lOZzdRcXJaK3giLCJtYWMiOiIwYmRhOTY2YTljY2Q4NzhjMWE1YmZlZWUxYWI3YjJiOGRkMGQ5ZGMxYmNkOGExNTViZGFmYmI1OTkwNDQ0ODBlIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-12 08:30:27', '2025-08-12 08:30:27')
-- ","Time:":2.36} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":2.23} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":3.48} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":10.74} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.4} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1'))
-- ","Time:":1.6} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1')) order by `id` desc limit 10 offset 0
-- ","Time:":5.56} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `job` where `job`.`id` in (1648)
-- ","Time:":0.3} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `companies` where `companies`.`id` in (84)
-- ","Time:":0.29} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select `users`.`email`, `job`.`id` as `laravel_through_key` from `users` inner join `job` on `job`.`employer_id` = `users`.`id` where `users`.`type` = 'employer' and `job`.`id` in ('1648')
-- ","Time:":0.31} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select `email`, `name`, `mobile`, `id` from `users` where `users`.`id` in ('512')
-- ","Time:":0.25} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `candidate_name`, `candidate_email`, `candidate_mobile`, `cv_public`, `cv_private`, `submit_cv_id` from `submit_cv_metas` where `submit_cv_metas`.`submit_cv_id` in (3165, 3167)
-- ","Time:":1.19} 
[2025-08-12 08:30:27] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.4} 
[2025-08-12 08:30:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":19.71} 
[2025-08-12 08:30:28] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":25.1} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":3.7} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6IjBCa1gxZjQ4eDZuNmxINzZHc0ppM3c9PSIsInZhbHVlIjoiLzk0WHNnMmd3MnZKWEYzbmIxMEhrYXgwbGxBZFJETURIWUorMU8rbENXellRK2hUb2Jmd3h1bWduZ1dNcWhEd2hiMnhKaWdielpEemkwSjNsYm5YeFlVRkt5Nm45RnpPQTZEMkdrY0VwY1hidnJHWVcwNW0vYy9NK1V5Nk5tQkwiLCJtYWMiOiI1OGRkOTUyYjljZmIyMjcyM2U5MWQyYWU2NDMxYTI4ZjkzNjI1OTgwZWFjNzQ1NTcyMGUyOGVhNzAzYWQzNjMxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ijd1alJIVE9lVEpCNFJNSDNlU3o3OWc9PSIsInZhbHVlIjoiUU5nMW45YjI5Y09IUUdzWDhvZk1nWTZacjVNc0tuTXlIcnBtaEwzSXppUW0yQVozMUVqdU9yakpEQWJNRktEK09CSnkweHVMZEticjUwQ1VmR0ZyRnJsZGxxV1BIUkJ2OGhmOC9NWlNTRkp1ZDB5b3NpcGNNbTViRmhBRGE3YzAiLCJtYWMiOiIyYWJhN2Q2OWM5ZTA5NDY5NDExNDZjYzEwZDQ4ZDhlNzU5OWZlOWFmNGVlZmMyMWE0NDQ4OTU1ODM5YmNiMWJiIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:32:08', '2025-08-12 08:32:08')
-- ","Time:":0.71} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.69} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":1.19} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.34} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":5.56} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.45} 
[2025-08-12 08:32:08] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":25.45} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":4.28} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.39} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.39} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.29} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.28} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.87} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.27} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.25} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.28} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.28} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.29} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.71} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.25} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.31} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.32} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.32} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":1.59} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.3} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.56} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.28} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.28} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.41} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.53} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.33} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.38} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.52} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.44} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.7} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.42} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.25} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.24} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.43} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.42} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.39} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.41} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.7} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.38} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.7} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.67} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.24} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.32} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.26} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.67} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.33} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.53} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.57} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.97} 
[2025-08-12 08:35:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.8} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.64} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.56} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.38} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.58} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.56} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.39} 
[2025-08-12 08:35:54] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":23.07} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6IitCS1J1NUVuOUREeU91WUVNU0tYTGc9PSIsInZhbHVlIjoiQlF5SVJML0p1alZhdkpmamhPVmkyeEV6c0ZvY1VkTDVQTW0ybGVSSWQ3U0hicXZkdW9QQUhzbVpVNzlrVHB5MEU3NFM0dDJITjlGSXpOaEFHYWFmNDE5anJwalY2YVd0SEMxeHZJaHl0Sll5emlwRzdVckl2cEY1eElWNlVPVlMiLCJtYWMiOiJjYjlhMDNkNTQxMTRiM2Q2OGQ0MjNhY2E4NGMxMTk4MmQyOTNkM2E5NmEwZDdmNDQyMTBkNDYzMjFkYWQzZDFhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IldRZEN6cEJMRWR5NTIrWW9mdDd3TUE9PSIsInZhbHVlIjoiQzhSZWtob29TZ01NV2t4NWhuR1VBK3Z0ZHFHUDIxdGlsOWE2anZsOXM2dHJMa2lYUTRoN3ZSaXB1OFdTMnBVM2tpclBMMUlXQ2pEcmk4UmJJRC94djZoY1Bvd2FYcWVrakR4NkJKeWQySUlLaHlkMVQzYkhFVWRtTTlxZ3lCMHciLCJtYWMiOiI0YmU2NDg0NzJlZDJlY2E1ZWFhNmE1ZjE1ZTE5NWZmNmM0MDVjYWNkYzk1NDU5NGU0YTYyZjQzOWJjNTRiMTczIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:36:50', '2025-08-12 08:36:50')
-- ","Time:":0.59} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.75} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.83} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.21} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.56} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.46} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.46} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.77} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.8} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.37} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":9.32} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.43} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.47} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.89} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:36:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":18.25} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6ImthbUxGZEZIZ3VoS1RodnNzbTIrVVE9PSIsInZhbHVlIjoiNkdpeU1wUWFYcWpyWVo4R2RRZWVpWEs5eDUwcjFnTmx6MmhudEJLajJBTnNOeUlBQ1pac1JDSW1wTThiUk56NXVHckFMRWVUdVF0QXo3cUM2WnQrZW9HeC9PK3ZwbnUvQzFURjdQMEFaU1VRSno3N3pxbDlBc20xTnJuU2J0anIiLCJtYWMiOiIwYmY1OTBiNmM5NDhkNjI3OTZkMzFjZTU0NjM5N2M2ZmUzOTU1NWM1YmVhZGQ4ZDQxNGEyMWU1NWNiOWQyMzRkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkhWWU5VQ0txa2owSklvbE13QjFUdmc9PSIsInZhbHVlIjoiNVQ5OWN3OTdLQzlmSVJ4WHNlMHc5OTAzMFJuUW5sSFNYRFpkNnRhUDI3UDBKdjRFRkJKWGJadmh1cjc5UEg0UWs1M09OSGV3ODhnVHpDRlJOSVB2c29nUUUzdVEyS1VoUm5ZUkgwTnF0Zk5CcnlNeVphck9IMkh1VE5VZFI2M2kiLCJtYWMiOiIzZjk4ZjM0YWVkMzAyYjQ2MmRkYzM3MDRjYWQwNjJjNjE0MTU4MzQxNjgxOTM1MDY4MDJmMDk3OGQ1ZTQzODIxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962611$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:36:55', '2025-08-12 08:36:55')
-- ","Time:":0.55} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.59} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.77} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.15} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.42} 
[2025-08-12 08:36:55] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":25.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":4.68} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.47} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.42} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.38} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.41} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.39} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.94} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.43} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.38} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.45} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.52} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.48} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.39} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.55} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.44} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.32} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.31} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.41} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.38} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.33} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.43} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.32} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.33} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.38} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.33} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.33} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.33} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.32} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.46} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.31} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.31} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.31} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.32} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.36} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.44} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.54} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.44} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.41} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.37} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.39} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.41} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.34} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.35} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:38:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.4} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":3.14} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962614$j56$l0$h0; XSRF-TOKEN=eyJpdiI6ImNSalowQXVRQU9Nc2VOZk9aNFlGNUE9PSIsInZhbHVlIjoid0c2d3hiaFFuQlBBVEdoYWxNdHdORWhOaHk0NU5BTE5PL0hpend2Uktlam04WVRGYmZLVXpHejBLRUV1cThqYTR0Z3NzYlExWjFXK3p1ZUxzcFVvZFBVa3FLREFQTzNNV2h2YzgxTzlUYnBkWm5KUU9maDVjbk4raWRlVGR4ZDMiLCJtYWMiOiI4MGI2MGU5YmU5NzU1NDc1YzVjZmU0ZmE1ODYyMmE4ZTYxMTg1ODIwYjljMWE3NTY4MjY5MTg4NGY4NzAwZmI2IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkdTMWR6bUJrR0Z1TXVwYmgxMm1BQXc9PSIsInZhbHVlIjoiL2VJcjNsazdkdWNqTFU0eHN3S2VHdDdTNnMwdXJlNXRKR3Irb2Q0bjlPYnhmTUJQSnNSczNPWEtPTjNwbUtRK0hBV2QySG5SMXU5QmVVSks2Z2NMYkdYa0RzNmpoMGxTNVp0R3h4U1FGSDl4ZHRpVi9VdGh0M3BST0dCSjFTODIiLCJtYWMiOiJhOWM5NTM4MTFkZTNiNDhlNzZiNzI2YzkyNjRjMzY5ZGJmZmNkY2VmZmNhN2U2MzcxNmRiZDdjNjEyZGY4NzkwIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:38:33', '2025-08-12 08:38:33')
-- ","Time:":0.6} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.38} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.44} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.22} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":1.85} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.29} 
[2025-08-12 08:38:33] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":21.89} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":19.08} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962614$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IndOK0lNMG1rcnEweFc3MUFFVlJXemc9PSIsInZhbHVlIjoiTkNiQVNBWjVFdWZyY2M5V0locFJoczE5ZWorbisvV05zaVU2c1MycVpQZnRnU1VoejZneTJmYzdQSEhSZ2RmZGdTa0pJYzlNVWQ5cXk5aWRFVXlHdFBDZW10dTZYRW40WU15ZVFrdXlaL3Y0YWlmSDZUN0p1aEhuL0V2cGgzYm0iLCJtYWMiOiI1MTRiOGI0Y2JlYTRjN2M5MWE2YzZmYzJhZTNkZWYzNzhjNGRmOWMxYjU0ODQ1NWI0OTU1NTZkNjJhMTdiYzUwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjFwUHZtTmJBemRZNW5XcHhaNG94VlE9PSIsInZhbHVlIjoiU1cyOXpuK3N1bzlHR3c5T0xteVlVOTIrbWtuZGt2TW9tTmoyWDlwZ2JLS0lRZnV2MWZRdi9MRW03WjdsZFErTXJJTzNuRHFmckdZTVJORWxQdW40QTJMVVpwV0hyQlJmU2lSVTA3NXlBMnBpVTdiSTEwNnN5UThhS0M1a3J3Sy8iLCJtYWMiOiIzYWVhNDU5NmI4YTUwOGMyNGU1ZmYzYzdmOWQxOTBlMjBjNjcxOGFiMTZlY2I0YTRiMmY0NDcwYzBkMTEzZWQyIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:00', '2025-08-12 08:39:00')
-- ","Time:":0.71} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.61} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.78} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.19} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.55} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.47} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.42} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":1.09} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.89} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.83} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.83} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.82} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.86} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.45} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.38} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.46} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.4} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.45} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.45} 
[2025-08-12 08:39:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.45} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":19.06} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6Im9HaGR0WlVMR2U5Z1J2MzM0MmVwOFE9PSIsInZhbHVlIjoiWXhid1pLRjBNZm1sMGxFTXlaZUtCRjNHVkx2YUFZT09uMERMeHNrc3NtUnlaMWcydU9sWjJUV0ZHUTFyajliWjZrcUZKZnFQR1hiZXdWbjNXY0V2UjczNDRLb0ROQUd5MkZmUmc3RFlsVUJMMlAvK2owenB5c2IzaEZNTFlvUXgiLCJtYWMiOiIzNTEwZGQ4NjhiODBlZWYyZDUyYzY5YmY2OTdjMzFiODRlYmY5NDIyMTNiYzI1MmYxNDg3MjZiMWNjOTRkN2E3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkRybWhPZ2RqT01FUnh3VHdTS1R2b1E9PSIsInZhbHVlIjoiZklsZnBESVBob1h3VllkR1BOZEdYK0xScEF1UDJzMWpScDAycFNTVVV4OGlycXFNNS9aVWdQN1dYL1JpNTNRS043cHVVeEx1TEdKZVpEa0VCNEl1TFduUG9PMWg1TmF6bjJvNnBGdmNrY25yZ2UrTXUrdXhoZUVYWVM0aGoxS2MiLCJtYWMiOiJiMThkYTA0YzM3ZjE1NWFmZmI2MzEyMTc2MDkwNGIyOThmMmQ3NTI0MWIwYjA0ZjM2Yjk5MmE0NWU5NDg2ZWRkIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962741$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:05', '2025-08-12 08:39:05')
-- ","Time:":0.53} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.57} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.13} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.41} 
[2025-08-12 08:39:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":29.1} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":21.11} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962744$j56$l0$h0; XSRF-TOKEN=eyJpdiI6InhBR09GVXhqQzFtQVpja0VIWUpoMGc9PSIsInZhbHVlIjoiQjBrVXk4azUzaEphWE5LVFFDQ0hVUzV5aW4yWThXU25Qc3NlRmhkbC83cFc2RklTS0xpK29SK1JJT3laaFdvclBNTmNyeitxMDJDS2gyd0t6NHJVc0tuVVlwU0k5Ykg5bmhUaVoyQjR4dmR2QnUyeE81RXVxenNOTlRDU1J5TlgiLCJtYWMiOiI3NTNjZmNkOTJhYjMxNDhkMDRjYTRmOTJjNThhNjNhY2NmYjc4MDZhMmQyYTY1N2VhNDM2YzA3NmU5ZDUyNzU3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkpHdDNTeXI0cTJjSlZrUHpuL3hENEE9PSIsInZhbHVlIjoiNVUwTTZFa3lHaEdVdlFZM205eXhKU0lsUGFqK2lEWlBWelpqblNUd1hIdkllUmxMR2FUYVlTamlCYVBoaFRrdjNoVnB4Q2lTUVEzeXlXL1UxVGRUNzNiNUFia2ppTXU3c1E3ZEZmL2JIMVRjbTEvSlQ0Z3Z6VGF3bzU3eGtWRzQiLCJtYWMiOiIzZWJlZWZkOWJiN2RjZjFhYWU3YmNhMDg2OTNkY2M0MmYwMzM4YzNiZmIxNmMwMWQzNjNmZDFmY2M2ZDAwOWEyIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:18', '2025-08-12 08:39:18')
-- ","Time:":0.58} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.6} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.8} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.14} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.53} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.46} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.44} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.99} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.86} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.89} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.89} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.84} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.83} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.83} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.5} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.48} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.45} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.53} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.51} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.51} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.51} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.5} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.51} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.51} 
[2025-08-12 08:39:18] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.5} 
[2025-08-12 08:39:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":14.37} 
[2025-08-12 08:39:22] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"coupon-message\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"67\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6InBtV3U2cFZDUS9lMWh2TUxpRk5oVFE9PSIsInZhbHVlIjoiNGVNSmhHMU9OUHlRbWJrQU1GZGtlako4NjFuR0RFUjdXRHRUbU5GTjZiWEUyazlHdWVsSC9OT1ZveDFvS2l3SXpKNVl2VjFhZ1FUS2pxWUU0V2xvcWRrT1l6ai8yTVg5MkRnamtvOVNHd2hsOXlYSzBvZEEvRTJ2cEVnMHlSSE0iLCJtYWMiOiIzMzJiOWZlNDYwMWM1ZjkyNDViNmI2MWEwYzUyN2JjYTVhNmRkYzQ3OGI4MzI2YzRmMWVlOTdkMTdiZjYyOTJjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjBUV0pZN2hUMXJaeHArZm1ybHlRMmc9PSIsInZhbHVlIjoicVlVQVZjZkloRWpqbTB5NS9MYm1Ca0VVSVk2WkFEOGFCbmU2QWc4dHhiclg1bzhab3c5VDVydTErajlQcjlwakNwU0xPSjlmZUp6N09MS0hBcCtsRHRmaFNQNWtzVU1BblFEbHRPRHlGQ05mTXQ3ZHI2UVo3OFJYYUUwUlk5MkgiLCJtYWMiOiI5OTc0YTUxN2NmYzQ1YWFkZjQyMzc1ZGVlNzZlZDllMDBmNThjOWUwNWEyZmVjMGEzOWZhODFhM2IxMmYwMjE3IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962759$j41$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:22', '2025-08-12 08:39:22')
-- ","Time:":0.54} 
[2025-08-12 08:39:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.57} 
[2025-08-12 08:39:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:39:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:39:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.14} 
[2025-08-12 08:39:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'coupon-message' limit 1
-- ","Time:":0.38} 
[2025-08-12 08:39:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":27.0} 
[2025-08-12 08:39:24] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"coupon-message\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"67\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962762$j38$l0$h0; XSRF-TOKEN=eyJpdiI6IjV0ODdPYk1vVFpRL20wZkNCTlM1a3c9PSIsInZhbHVlIjoiYk5LSGIzYlZKQ0ZMWkZ6d2x2cHFGYmROVVRJMzhSSE91cXlSOGhxa1ZXcHVlTFc5V0k5VHBTK1Y1L29wL1dhSm9TdlBYTVJuaHZxamo4MjA4Y05OK3dXdWFiS0ZwRmxqcTlyVHVlblhibVFJYnI5OEkrWVZHS3NzRG9ybGFDMHMiLCJtYWMiOiI0OGViMWM4Y2Q3NzJiZjI2YjA4MTllYWU5ZWIwNDE0YjUxNWFlNTRiMGY0ZDM3YmFhMTE0Yzg4YmVmYjQ5MmIwIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImJEdEgxWGlsL1FyTWpjYUJpWjIwSVE9PSIsInZhbHVlIjoiTXV5c0lLUkM3RUc0ZzF0UXFoSEZHeUd6SXhyQlpKZ2FHNHZVQi9nbzE0TlFFWThtYVd5RlJEUmNaSWNhS2drZzFQNkFTdmdUR2RycGxpd3JyZ0tHd0FwYVhObmRQU202QmZGMEhlZ08rOXJkcE9mZnUxTUZORTlNWHE3NWxzbXIiLCJtYWMiOiJhYTE0NzM5ZWEyZTJkMTFlYzIwY2IwMmRhNmFjMDBlZDRiYmJkZDk0ZWI5NWIzZTNjYzk4NTc3YTc5Y2U4OWZlIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:24', '2025-08-12 08:39:24')
-- ","Time:":0.41} 
[2025-08-12 08:39:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.42} 
[2025-08-12 08:39:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.5} 
[2025-08-12 08:39:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.24} 
[2025-08-12 08:39:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.38} 
[2025-08-12 08:39:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'coupon-message' limit 1
-- ","Time:":0.31} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":23.72} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962762$j38$l0$h0; XSRF-TOKEN=eyJpdiI6Ijl5T1VCNy9ZQ1AzcVNRcFZvOTAvQUE9PSIsInZhbHVlIjoiWUwrYmFDUGJjTWZ3elBqUWFSd0F2ZG9TYUxucTJEMk5OSXVyYmhySDk1MEpYcFdJQS8vMnQrVVdlVnp1NVBQMDRlc2JtQ1FtY2V5QlZQTjh5eVpvaUpLYUhBNjNINkFoWVRuakxWMFgydnZBOU5PRzdIWWx2TFZPQjFoS3BPUHciLCJtYWMiOiIxYWUyOWU5NGMzMjkzYzc1ZDJlZGViNTQwZDIxNTAxYWFlYjllYzM4NmMwZjZiMjQ0ZjViNjA5NzU4ZjMwMDJjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkdOM2ViR2MzVTRoNlduS2RoSjI2aWc9PSIsInZhbHVlIjoiS2d2TDl5WFVldnRYN2RYcW0xOFFKWk9uRXRReWdXdjZNRVFacWN5bGJ3ZXlNNE12MndveEpGNFNSQm1EV3hNMlJvdjdMeWJSbDF6bEIwVGtmQitxajZZcGdsTGd4ZlJmUTJnbGlycDdQUTl3ZjFWSnErUHdiZ3hhNGc2TzdTSFYiLCJtYWMiOiIwOWU4YTYxZTAxMTE2YzUzN2M1ZGU2ZTc0OGRmZjQzMjZhOTE1ZjFlYjFkYjRlYTYwODkyYTNlZWIxOTllMDc3IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:25', '2025-08-12 08:39:25')
-- ","Time:":0.56} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.57} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.16} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.32} 
[2025-08-12 08:39:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":30.04} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":24.25} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962762$j38$l0$h0; XSRF-TOKEN=eyJpdiI6IjY4Z2kvUWJzc2JrenV2dzhZSVdhQUE9PSIsInZhbHVlIjoiVTZHUGtOR2lEODJyOGtRR3JRaDhuU01OUlMvZHRnMFU3K1BZYXpKdFVtNmVkajhHMEk3eE1iWDNKa3I0QzBnRmJkbllBY3h2bW5wcUJLdFNNRXhQRUY1UDJ6R0VBWXc0QjJHaDV5ZWxQdGdNY0duOUJHeVBzSG5WNkxpbTFFUkciLCJtYWMiOiJkZjE5M2ViZWVlNDI2MWNjODgwNDg3NTQ0MGU3Y2JhYmU3ZjljYzU5YWU4Y2QyNWFhYTgzYzNkNGZlMTdjZDU4IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ilp2MEwzWVViUkovSmZVT3FjT1pGT2c9PSIsInZhbHVlIjoiNTdxcHNUbmJmaWNkSXBFYTUwTGxVaHlRcjB0a0tNa2FtMVp0RjRLTU80WmRLTWtaUHVta2hnaFVwamZkZ3VkemhKN0ovV3FrWXpXa0twb1JnQTJmNVlpWTFrT3I4RFhBYXZwUmh0enJLY1N2NzdjbUx0NUFmRTAwR1FSWldqVE4iLCJtYWMiOiJmOTkwOTYxZTBkNTc5ZGEwNzViNGI2MzVmOWJkZTFlMTFiMTk5ODYyOTk4YTY2ZThjOWY5NmVlNGM4ZmM0ODUxIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:46', '2025-08-12 08:39:46')
-- ","Time:":0.55} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.56} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.24} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.55} 
[2025-08-12 08:39:46] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":37.69} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":22.38} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962762$j38$l0$h0; XSRF-TOKEN=eyJpdiI6InhwME1VeU1rWTBPaWJJTkxKaXZycHc9PSIsInZhbHVlIjoiRkQreFdQb0VHandONHRTQ2c3a1J5d0FzSUZRV0F1RktkMzA3d1hGQ2FBZXJrL01QV0xscHk0ZVRjajFBSFFrMWkvU1I5YUVaa21iajgyME1LUUwyTmc2SU5oK2dtTXRMZU9xbVNoRGxmNjNxdlFwWEZKckVLbHBPZ2h0Y3IxTGUiLCJtYWMiOiJhYTViMWI2OTUyOWY2ZWY0ODkwZTlhYWQ2ZjFiZTdiNTUwZGViMmQxMDdkNjkwNGMxZDZmOGUxMWJiN2VmYzhhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ijc0OHoyN2YzalN4NnNaYmxHWTUrMVE9PSIsInZhbHVlIjoiUmFlL1FiZ1lRUEx4d05NdXQyTDhhVEZhbUc5VFByOVdUMDJiT0VqY25VeVliTUtORml6U3dpZ1NKVXNKZTFRUlN6TnU5bDJkekhmSUppT1hmV3BOVzcvc2M0ZWZvTjVvNFZCNC9DKzI1cVd3QWg1VkpiQUJOK3gza0N0VUNreDciLCJtYWMiOiJlY2NhZDc3Y2VhZGFhZWRmMmRkMDllMDNkNzYxOGNiMjcwZTU2OTdjZmQ1NjU3NDk0N2M0MDlkYmY5ZThkYTIyIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:39:50', '2025-08-12 08:39:50')
-- ","Time:":0.52} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.56} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.25} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.41} 
[2025-08-12 08:39:50] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":34.9} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":20.01} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962762$j38$l0$h0; XSRF-TOKEN=eyJpdiI6InNLMklFTmE0YjBEL1ZMc1FXSm9ydmc9PSIsInZhbHVlIjoiSS8zUlhmRFZzT2pRWExnTVRSQmJpRUI4WjhWdzBjSU1TVmtFQWFrKyt1eHlzRlJZWVFPQ2pESVkrRGFNS01GVEMyWjBPb3hxR21ObGNjZE1ONDROSEJsbjZvNnVHeWJmZmhNdUowTlFwYm5DL0R0dWY4T1FlM3JHZHRpV1N1TWciLCJtYWMiOiI3ZWM5Zjg2YmY1NWRlNDk3NWQ3MmYwMDk0MTc3NDhlNGIwMWZkZmFiNzk5ZTU5MzM3MGVmNzk4ZTRjNTNlNzI3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkpiZWpBK2MrRzlZY1p6TzFVWVA1SGc9PSIsInZhbHVlIjoiUDNMZ3QzSnk4eDdrbW8ybCtLbE9lb1VnWTA0M2toY1FabUZQRU52UkpMRDE5dFJySWNLUDdzWjUvdkZadWJrcUMzNXZoQ212WTFoam1hTG82K2paMXRqSnFMK3hlM1FURDZNVlVFZTNYaEk0OFIycWNDM1hiL05qb1FJdGtwelIiLCJtYWMiOiIyZWYwNjI1YTU0N2I4ZTMyYjFmN2UzN2M0MjBjNTA3NjQ0MzA1NTMyYWY2MDQ4NDI1ZjAxOTU4YWM2MmUzYWIxIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:40:09', '2025-08-12 08:40:09')
-- ","Time:":0.54} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.58} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.82} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.17} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.52} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.46} 
[2025-08-12 08:40:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.43} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":1.02} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.45} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.68} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.35} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.38} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.35} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:40:10] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":12.84} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6Ilk1OVlvV0FtUzFQdHRrWFBBYlNKVkE9PSIsInZhbHVlIjoiRnFveXRBb2daY2VJLzF6Y3pTcWNreXZzNkprWmFWVTh2MDIxRGV3R2dUWFlXOTlVL0t6NXY1NVJTL1hyNFJ0OEhSb2ZpSEV0SzEwWTZJdjVFSy9OVXFrRzJST01BSUFLTnhsbHRVMlF6WmpBRjZIblJPYlRvYTFBMjBFYUhkSUEiLCJtYWMiOiI3MjBlODgyMzUyMzJlNDJiMGFmOTk4NzEwZDQxNjhjYWVkMzI0OWQ4ZGNlNWIxZjg2ODA4YzZiYzBiNGQ2ZmM1IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImZTYkxDOGxNVDVvTW5RTEdhTnk1MUE9PSIsInZhbHVlIjoiQ0Q2YkRSeEtkQjNPNkZVaWJKbDZlWUVld1pDS0lGb2ZVUDBMdmhXTnJLN2l5b013SEFKb3cyWGRidDRRZnlxd0V6NzVUalRJS0d1Wi9HUmNXODFjbFMyZXp1bmhPZ240dFQ2bnJBY1pQVU9WanNlbHVoR0hqS01ESzBTNm95MXQiLCJtYWMiOiI5NTAxZjZmNzljODgzNmUwODBjNjdjZDZiODMzYzUzZDdjZDc4YjEwNDkzZjFlYzY0OWJlNmNjZDBkZDljNjc4IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962811$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:40:13', '2025-08-12 08:40:13')
-- ","Time:":0.37} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.38} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.29} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":3.62} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.38} 
[2025-08-12 08:40:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":32.0} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":19.81} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962812$j58$l0$h0; XSRF-TOKEN=eyJpdiI6IlIvQWVmV09aVWtic2tXZ1R2Y2RNSVE9PSIsInZhbHVlIjoiWDZYbk8zbjlYQVdqays1ZExVWms4NldaV0RuUCtSbUkwdktLMHdQdk1oY1BQTCtjbnVBVGpVUnhCazdlZnorWHgrRXkyV0VWK0VNRldBM2lSM0Zvb2NEeENkSVRXWnIwcXFsWjBNaTQvZGNHNWEwa0RPV0ZqVEJ1UGxJS0VHaHYiLCJtYWMiOiIzYmE1ZWZmNjM3N2E3OTNiYjQ5Y2E3MTI2ZGQ4NWNmOTZjMmI4ZjI2NzI1Y2M0NWQ4MTYzYzdlMDBlYjkyMTcxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkNhOWZ0S3orZ2JuWkcrWGVVWGVxQkE9PSIsInZhbHVlIjoielVHUEhMQkRSdVVDQzJCTnhjNE1Ta1h2K2lWNFZZdURmYnRhenplYXBpbUZ1MzFjN25QdGRpN2tXSkhGMytsYTNHWTJ5UExOWnlrS1plTEk3QXcvSDZXWUZUVlpVRFFad3hKVytCRHVPWnFHQ2svRFNteGswSkdDbVkza0tRenYiLCJtYWMiOiI1Nzg3ZWJkNjRlNTQxNzA3NGY0MjBiYzg1M2UxYjg2YmQ3ZmU0YTExNzViMDUxMzhhNzYzNjU3Y2VlYTI1ZDRjIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:41:52', '2025-08-12 08:41:52')
-- ","Time:":0.69} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.65} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.82} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.2} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.53} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.5} 
[2025-08-12 08:41:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.43} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":1.11} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.84} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.52} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.67} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.69} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.85} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.62} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.82} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.77} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.54} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.5} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.53} 
[2025-08-12 08:41:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.49} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":14.01} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6Ik1OVnRMZW5oOWRiRjZwVGVRZTFkZlE9PSIsInZhbHVlIjoiNmQvRTJES0R4blNyU01SaHArQk9lSVp3dkFaNm5WVUNsZHNQOGNTVGxiR0tJVXRPY1ZEdmFqUVAvYy9aQkFuVHBNbHprT3ZLczlQczFqbStvK0J3aFM4S3ovRlFCRzRhWURlVERKYUt1djZITHhUUlVPMVBEc21YTy9sZ0JsM2oiLCJtYWMiOiJlZjJlNjNmNmI4MGJjNThhYTEyZjBmZThkZTk0YmMzMDY5ODkwMGE5NGI1NjM1ZjcxMmM2NTgyYWQ0ZjQ2Y2VhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjlxTXN3SnpYMjhhZ3VMK2ZYY3FacUE9PSIsInZhbHVlIjoiUE4rK0pUaUx3Ym1HQjBQaE5TT28yT1F4Qm5MR3V6ZTIwQzkvYmNXeXRYbDhUamJHR2N3VEhVUlVsZkYzcnBMaE5wWUFacDhaSGtzRXVFbTNGRkV2UEZJM0V2VEVGOUF1TnVTNWxBcURWeUxEVXA3blpGRmRwb0JScHBldUlTZlgiLCJtYWMiOiI3MzEyMjM2YmZkNTY1NmE3ZDA4Y2FjNmE2OGQxMjY5MWRlOGUxOGY3YTM5NzQ5YjQyOWIxNGQ3NDk5ZjllOTY0IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962914$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:41:57', '2025-08-12 08:41:57')
-- ","Time:":0.55} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.56} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.25} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.41} 
[2025-08-12 08:41:57] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":35.68} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":18.11} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962916$j57$l0$h0; XSRF-TOKEN=eyJpdiI6IkkxWE91UWVnTUpyUlBTM1NWbmpRMlE9PSIsInZhbHVlIjoiRktYUEVlMmkxV25lcWxiVFFqcUhiRzdXSHVnK3ZDeWJWQVp6M3lpV0lROVJiQ0V4N0YvV1BObThBSHNzbFJPK1RXRzFWaUNsYmFrcWwzdGcxaDF3ZnQ0MncrdU0rTjlmQXlncDJPU2d2Y29FU0dFMzBaWGhvcDRaKzRrbFc2ckoiLCJtYWMiOiI4YjhiYzg4YWE4NmU2OWRjOTVjNjY5MTc4ZDJmNjMzMmFlMzM3MTA4MDc1MzRjYTBhNWE4NzI2NWQwM2Q2ZTVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjJaczVDVXNxbnM5TEVXQVZka2hQdXc9PSIsInZhbHVlIjoicU1lSFl1OC9OczQvUjN3VmhqN1lyNVdleVlhVUlJblM4QVNjQytROUsxTEpHVWpPSmVuUnhyWnFkV2hMMzdSTVAyRFpDdHN1Q3BGNjFna1ZaN0xTQlNtK2Q3UTBtTE9iU2d3SlJ2OGJ5OUR6YXh3VTZ2WHdkSHBLemY4VGFobGoiLCJtYWMiOiI3YWE4MTk4YTY0NDcxOGViY2E1YTVhYjEwMzNjNzJhMzU4ODQzYTQzMzExYmU0NjY2ODY0Nzk3ZjQ1YzBlNTZiIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:42:37', '2025-08-12 08:42:37')
-- ","Time:":0.45} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.49} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.57} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.25} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.69} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.41} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.35} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.35} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.85} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.73} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.56} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.74} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.61} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:42:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":23.98} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6IjRoTjk1UlVlaU1WeXorUTkwOUVTZGc9PSIsInZhbHVlIjoiWEpEYmdCeXJkTDBkUnVQdDNkV1l4Ykozb3IzVnU5RzdNS01OaVdvWDJQM24vQncyM21PZ1VCSS9xYzN2ZzRURi9hWDVCTzhES2dJdERoTWl1bXVUKzFIcVdPOXlJUTdVZXhMcTdjMHN4V2hyclNmL0ZNN2RObUR3T3RFdnNYclAiLCJtYWMiOiJkZmQ0OGZiM2U1YTMxMDRjM2VmMTdjNWM3NDU5MjE4MmIyODliNTcwNzNhOTAwYmE1NDk3MmI2ZmRmYzE5OTk0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImJEa3dnMDRzUG5EVjFwTyt5eWtZQkE9PSIsInZhbHVlIjoiU0JjTHhlUERXaTJUMm5qVU5VV3kvN0o3UXVnakJhVVFnNnY3TU8yVStGNGM4K3RrYUxFQ2RqOWErN1ZWVjBPWjBvTUF6MmFvblRHeU1kOCtpTFhQSDQwNVlvY2h1UmZYeFR0Y0h5SUNsc3g3Z0toQlM0TWVNS3luZnhBRmlKZ0IiLCJtYWMiOiIwM2RhMmMwMzQzNDE4YjQ0MDk1YmVkOGJmODUwOTgzMTQyOGIyNDBhNmJlZDcyMmMwM2IyMDcxYWI1YzY0YmYzIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962958$j15$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:42:42', '2025-08-12 08:42:42')
-- ","Time:":0.55} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.58} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.78} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.37} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.17} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.43} 
[2025-08-12 08:42:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":46.12} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":18.51} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962961$j12$l0$h0; XSRF-TOKEN=eyJpdiI6IjZMdUtGS2IrU3V5N2ZabXhFRzdUbWc9PSIsInZhbHVlIjoiYldTOWN5a2pERVdFbGhJYWcvclkraDlIZjZ0OVNmb05yUWVqbmU3bTdxRDdLY3laM2dCdmtRTDRVMWJaRGIxL2gydmtnRzN3MGFxQWZwSmxZYXZ2OEppeUkrNk1TTisvV0RVNjFua3dVZW5GNll3aktCSysvcHI0OVc0LytkTVMiLCJtYWMiOiI3MDk4MDZiMjY0Y2QyNGRlZDI4NjgwMTVjYmE4NTk0ZDAzMjAzMGZjMjY0MDUzODI4MjAyMDVlNjAxYjU5OWJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjRpcEJxRldQRnVsd09PbUlSSWlLUEE9PSIsInZhbHVlIjoiWWFrN3lnWWNCOG1nZmhjMEdOQWl5czFSRy9ZTkcxcVhtOXMxbll6VEFoS1Vna1VueFZMNWxEUkQvT2l2YnJPaVB2dGkwakV1WkVWeklNZzhWUDhTL2UwSWEyNDJ5U2ZSZGFmdUNtbE4wR3RaR3JjYmpkVGkxZ1NkeGNsMTVTM04iLCJtYWMiOiI4OTdlM2ZjMmQ2NzY3MDcwNGM2NDI1NDYyODlhODA5MmUwMTA5MjNjZjBmOGFlNjA1NDYzYTk1ZjQyNzkzNDI1IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:42:59', '2025-08-12 08:42:59')
-- ","Time:":0.54} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.6} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.76} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.23} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.36} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.31} 
[2025-08-12 08:42:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.29} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":1.07} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.77} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.76} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.52} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.61} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.58} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.76} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:43:00] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":2.94} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6IjlHeHFGT3U4VWRHamhtVkQwSjBwbUE9PSIsInZhbHVlIjoiWmNQYWJLZElselVtRS81cHVpSEthVEdMckduL2xwbG1ITFpoVzBGTmpDNmRBNGk2dUQxVnQ4dmVFZy9sOUVSOEJCZGJncWMyaEtqWWs0U2x6Y0J4amQ4elpNbkFiaDNUMWx3cmwzRS9DUXh3eHd1MW43NnlQMER0K1lacXpuNk4iLCJtYWMiOiIyOWIzMmE4ZGE2YjI2NjQ0ZjU1MmFlZDdhZmRlYWNhNmZjYmI1YzVhZmY5OTI0NWI2OWQxMDA0MGQwMDIwOWVjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjdFbWhRZVpKM0ZIR3FIcU5aZWRIcUE9PSIsInZhbHVlIjoiS3NSN3hCTEVtNUlJQjYzSEZOOXY3UXFOdjJlREhQaXBnc0N5bSt0Z3FUQXJ0cGdqUHZ2QzJ6RE1GYW16YldOelcya3JZaDJaWnRYSGt4VllIUjd0c2pjT1k0Ylc3bzhVN2c5eXZhWUdDeFZHNVBTNHZXMWZWYm94MEp4Z0MramQiLCJtYWMiOiJjYzdlY2EwNjNiMzZlODJhNDE4YzVmODYyMGQyYzk4OTc4YTI0YTQ4OGY2NmRjYzU4NTQ4OGYzOGExNzQ3ZDYwIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962981$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:43:05', '2025-08-12 08:43:05')
-- ","Time:":0.53} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.59} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.76} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.13} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.44} 
[2025-08-12 08:43:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":30.13} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":18.33} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962985$j55$l0$h0; XSRF-TOKEN=eyJpdiI6InNZdi9NVnJyVnlkNVhzcG9xc01IVUE9PSIsInZhbHVlIjoiNi96NnlUNlpIbXYwQUl1LzJPd0lIQUk1NXFHOE9GbjhtNU4yVmErc2orRlNuQ0JVYVhzU051WTd6SFdpWXBPZUZyQWloT1Yrci9HQXpLRExDRndsWHVhUE1WYXpNT3hhRTZvK0k1OGpTalNueEMzRERMN3lvS05EV2xTU3NLaHYiLCJtYWMiOiI4MmQzYTJjYmUxMjFmNDQ4MzAwZWE0NjVhMGFmMzA5NmJjYjMwOWI0ZTc1MzlhMTQ5MWI1OWJlOGRjM2Q1MTJhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IklwWm5MWXNTTTQ5RXhXK3pzcGVTNEE9PSIsInZhbHVlIjoiZUcrVFlBR2lNa3pwM3VOUGF3Rld4ZDlGTGMzOU14bTgzdUR3RDJNOWN6Q0VFb1JGSS9jc2x5WmgvSEdnL3lDWXluN3Z2OWZ4NWdNWVlVdjVJQkhuVGFveFk1Tk5ubm90Vnc1c0svTVJzVmlwc0UycjBDTTEzMFZzM291WS85V3AiLCJtYWMiOiI5NTZmNjI2YTExOGJkYzA3ZTMyMzA3ZjBlODg2N2Y0MmIwZDdiOGYyMTczNzZkZTRmMTE5OTU3MmUwYWRhMTY5IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:49:01', '2025-08-12 08:49:01')
-- ","Time:":0.45} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.45} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.55} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.26} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.5} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.32} 
[2025-08-12 08:49:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":29.12} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":26.94} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962985$j55$l0$h0; XSRF-TOKEN=eyJpdiI6ImNUOUQ0Sjk3MkRNVUxQWTVtdW1jb0E9PSIsInZhbHVlIjoiV2FEeVlqT0sxeERpSzR6dlB6RlJuUnpPTTBnK1I3Yll2aHVzSm1BaDdOT0xhNCtodkQ2SXp0Z0ZBZ3Yxb2d1aUtqSlJrMUZQYTYxZWplMU9MMndmRWM0QVFBR0Z6ckVlblJMbVRRVTJJVlBIMEpOUlpEd0NCSTgxb3M0NkpCV1ciLCJtYWMiOiIwODY4YWQxNDc0YmY1M2VmOGFiN2RhNGQ0M2U2NTM3ZmYxYTRmNzk4ODc1MmFhNjg1MmMwMGQ3MzE1YWE4NzljIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IktmVW1nSndVbXAwbzZLTW40RDNtQnc9PSIsInZhbHVlIjoiWlB4ZEJva3p1SzFWZ1pMK0RITit5dUhOMEpUQXZKanJmeGJTSkdvanBaWXNyNGg1dDUrSzlzd3hyQW9kM1o4eVpkY0NScTd5NUEvSG1VQlVmcGQxb2lKVEUraUZuSzVOUmd0ejZsN0dlV0Z1YnVMQXE5Mzd3Z1REL2taRXNuNzAiLCJtYWMiOiJiZTQ4MmQzNTBkMzM5NzE3ZGIyZWIyZDEyNGJjM2YzMTcyMzEyMWE5ZTljNGQ0ZGU1OGIyNDkwYzRhMGUwNzI2IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:49:06', '2025-08-12 08:49:06')
-- ","Time:":0.55} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.56} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.18} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.41} 
[2025-08-12 08:49:06] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":36.01} 
[2025-08-12 08:53:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":3.48} 
[2025-08-12 08:53:05] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6IjQxUDFCQXNCbTlNaUMyYTU5MElqeVE9PSIsInZhbHVlIjoiaXQ1YW9nbFhGVWJvaFkwQVRHNVAxRHh1cG1tNjVGdVhaM2huK3psZS9HSVVlOEgvN3ZRdjJkZXZVblZCTUdGc0piOFdFVzJqbDdXejlZcEVEbXJSeHpjWkVUWTJsbWE2SWR5c3ZHcGkvZTQwdEN2VHM4aDRDZnhQMUl2QjVEbFQiLCJtYWMiOiIzZDVjNzZjMDRmNDU4NDI1MzgxM2E2MzM5ZWU0NTVhNDBmZjIzMzViZWMzZTlhYWE4ZmIxY2Q0ZTRiMTMxOWMxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJIS1B4ZDcvc29YaG9oVFQ4b2FNQmc9PSIsInZhbHVlIjoialNtNHZPcnUzaEh1VVlkTGNyRkZzbklZZWgralpOc3lJQlBjTFNVRk1qL0gyTHJFb0JUOGFMcjVWTDFqc3kzU2NRVlZlWVdvZjdKQncwcHd4Rmk3SFNmM0NJRTVoWVd6QkExMnk3Z0VlckZzeDcvWnR0cms3bHZSTWhXOUtIdW8iLCJtYWMiOiIzZDRmNzZlMGFhNWM1MWRjZDExNmMzZGQxNGYwY2M3OTZkNTljZGZkYjBkMDg5ODU5MDJkMGQ3ODgyZTAzZmRhIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754963530$j60$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:53:05', '2025-08-12 08:53:05')
-- ","Time:":0.41} 
[2025-08-12 08:53:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.38} 
[2025-08-12 08:53:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 08:53:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.28} 
[2025-08-12 08:53:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":1.86} 
[2025-08-12 08:53:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.28} 
[2025-08-12 08:53:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":22.23} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":24.75} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754963530$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IlM4eldDZ1R3V0lGKzdZWjdqclRhK1E9PSIsInZhbHVlIjoiY0VKa1VHWDlOdy9YZHliZWkyTmZ5Q3cvNkFxaUhvWlkyM1QxQmV3YkhPQkE1R2VaWGRWZUxhcmpJUTY4RTJEYTRLQjRURWxCdGk1VXN5M0tTd2MyZEw0ZjdwNkU4S3dTdVRPeXMvbDJnU0czd0hJK0VVQjJERVcyWVdZeVFIYVAiLCJtYWMiOiJlYmYzNjZjZmY3MmQ2Y2M5N2Q5NWM1ZTY0MjZhNDIzYjJhNDM3MGUwMjJlOWNiN2U0ZjkyYmQwMGViNDRjNDg3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik8yVzVDczU5OXFqV3FxVCtVZVhQMXc9PSIsInZhbHVlIjoiT0tKM3k3T2tNTzNRcWpzMndYa1p0V3VXWmMwV2F5dVVBTzV2RzhvcTVhVEhKa0pXM3NleFZwUFpwMDc2bXRGait5TEIzaENpZjlIY2JWMmVubk5PWVJRckt5Q20zbCtWa1VEKzdjYjZOajlteTNKNVk5VzI5S3RqdDEvZ3NrYWgiLCJtYWMiOiIwZDA5ZDMyNDUwMmJjMzdlZDgzOTBjNGI5YzlkMDE4MTYxYmZlM2E0MTRhMmUwNDYzYjBkNTE3MjhmYzhmNDI0IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:56:07', '2025-08-12 08:56:07')
-- ","Time:":0.59} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.59} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.27} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.58} 
[2025-08-12 08:56:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":39.5} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":21.58} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754963530$j60$l0$h0; XSRF-TOKEN=eyJpdiI6InlUOUZQSExscVVodTAwM0xNVWl1aXc9PSIsInZhbHVlIjoiWGRmYzVPWjBEQlBEUmlUL0lVOWgrTGZ6TnlJd2VSNXgrRWk3NXk5UDhnL0RpOHZLdS9OUjZJV0x2NU1RN1lpazh1NXNIenVnVnRJRUJqdHRyYjZGQVVISnAyekJtNDlVSHpXQ0NJc09iVHo0T1lIVkFlVDhSZlpvQU5GN21QVkwiLCJtYWMiOiIxNzI3YjBiYTA5YWE0Njg1NzUwOTczMTc1YzI5MjQ0M2E5OTJlZDZlNGIyYjA1MmJlYzFkMjI3MTQ1NDE0NTI3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ink1TjU3Z2pXN3NUZ3dCSEt1T0I5MFE9PSIsInZhbHVlIjoiZ2psY2NQbHkxUVRSRXB6Q3dXY1UrRXlGOWtMNmREV2R3RVlDN3hpSkMzYVZVWTZ0R3ByWDA2Y1lJMEdTWE9seS9YTnVzclF4V0pDWEhwbU9JN01iODBvYmJxTDFFYWc4VndoRjNxeWJIajJ1Y0VqUW1ra0ZNZFV5VTB6VFYwYTMiLCJtYWMiOiI0MTIyNDc3MzZiMmY2NzRmMzc0M2MzODdhNzNmOWY4MGU1Y2UwZGQ3MDc2NWI5ZWJiMmJmOWE1Y2M1YmZkOTQ0IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:59:21', '2025-08-12 08:59:21')
-- ","Time:":0.63} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.63} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.78} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.34} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.27} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.58} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.47} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.47} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.8} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.76} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.7} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.55} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.7} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.59} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.72} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.71} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.6} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.38} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 08:59:21] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.42} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":20.18} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.6} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.69} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops`
-- ","Time:":0.59} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.28} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.26} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `group` = '1'
-- ","Time:":0.32} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.41} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` where `job_id` = '1648' limit 1
-- ","Time:":1.44} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` where `job_id` = '1648' limit 1
-- ","Time:":1.32} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.47} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.36} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":12.27} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.37} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` where `user_role`.`user_id` in (1723)
-- ","Time:":0.27} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.48} 
[2025-08-12 08:59:22] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.45} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":3.28} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.63} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_id` = '1648' and `parent_id` is null and `job_comments`.`deleted_at` is null order by `created_at` desc
-- ","Time:":0.84} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"onboard\",\"level\":\"2\",\"career\":\"1\",\"salary_min\":\"1000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"undefined\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=onboard&career=1&group=&is_it=&level=2&salary_currency=undefined&salary_max=20000000&salary_min=1000000&skill_id=2', 'http://recland.local/admin/job/1648/edit', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/admin\\/job\\/1648\\/edit\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InMycVFwcE5ZaS9FVWlBbGtDazAzUmc9PSIsInZhbHVlIjoidG1qWHlWNWsvZ0x0U1d3aU5ZbVplR0xFQ1hJcmsyMVRiREF6bUlVbmxCaGdmZXBNVSsvWmZDdS8zK0ZZbmlVS0YzYUJOS014SEVpWXh6WVJhaUZOTEY1cWVlU0dWdThweStwZnh6YmRxZk9JTmJEY0Ixa25ORGpNSEhjeGMwSVYiLCJtYWMiOiIwMDYwYjY2Y2RhMDEyYjgyZDAxMzIxY2YwYWE4MDFiODRhNTZlYTJhMjFkMWM3Y2QxMDQzMmFkYTE5MGRjNThjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im93Y1BCSmhGTFRLUGJmY1NOWnQ5YkE9PSIsInZhbHVlIjoiS3pUOVRwcXBnNUtnZ2REWFBWdXpJNnlRMk4xWTlDMXJWUzdwZkRjRlFpbUY0OTJpemZZU1FMK3NjUm9CS1lNTlZBSWE0b2J4bzRGRFhMNGt0QmZUM0hvc3BqSGpHcjhJU0ZWdDRzK2QrQ1UwdUc4dUFiZ0dxTThpKzJTaEdJT2QiLCJtYWMiOiI0ZmE1YzhlYzFmZDJiOTdmZTdhMTJhZWRkMzMzYWIwM2Q1MTc5NjNmMzE4ZjZmNWVkNWEwYjExNmFjOTRkZWJmIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-12 08:59:24', '2025-08-12 08:59:24')
-- ","Time:":3.04} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":3.24} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":3.14} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.76} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1'))
-- ","Time:":3.6} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":23.24} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1')) order by `id` desc limit 10 offset 0
-- ","Time:":9.24} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `job` where `job`.`id` in (1648)
-- ","Time:":0.32} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `companies` where `companies`.`id` in (84)
-- ","Time:":0.26} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select `users`.`email`, `job`.`id` as `laravel_through_key` from `users` inner join `job` on `job`.`employer_id` = `users`.`id` where `users`.`type` = 'employer' and `job`.`id` in ('1648')
-- ","Time:":0.33} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select `email`, `name`, `mobile`, `id` from `users` where `users`.`id` in ('512')
-- ","Time:":0.27} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `candidate_name`, `candidate_email`, `candidate_mobile`, `cv_public`, `cv_private`, `submit_cv_id` from `submit_cv_metas` where `submit_cv_metas`.`submit_cv_id` in (3165, 3167)
-- ","Time:":0.41} 
[2025-08-12 08:59:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.4} 
[2025-08-12 08:59:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":2.81} 
[2025-08-12 08:59:25] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":12.74} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":4.24} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":3.8} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":1.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.58} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.55} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.57} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.15} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.43} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.5} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.24} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.23} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.21} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.22} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.21} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":1.92} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":1.86} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:51] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":1.99} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.51} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.45} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.62} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.54} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.31} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:00:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:02:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job`
-- ","Time:":12.28} 
[2025-08-12 09:02:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1'
-- ","Time:":6.14} 
[2025-08-12 09:02:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":4.66} 
[2025-08-12 09:02:37] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.47} 
[2025-08-12 09:02:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job`
-- ","Time:":12.65} 
[2025-08-12 09:02:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1'
-- ","Time:":7.89} 
[2025-08-12 09:02:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":5.78} 
[2025-08-12 09:02:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` order by `id` desc limit 25
-- ","Time:":1.2} 
[2025-08-12 09:02:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` in (84, 217, 286, 487, 491, 693, 694, 695, 696)
-- ","Time:":0.61} 
[2025-08-12 09:02:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` in (1723, 4629, 5421, 6139, 6156, 7372, 7412, 7415, 7456)
-- ","Time:":0.59} 
[2025-08-12 09:02:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `job`.`id` from `job`) as `job_aggregator`
-- ","Time:":0.27} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":2.38} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.37} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.43} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops`
-- ","Time:":0.38} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.27} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.26} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `group` = '1'
-- ","Time:":0.31} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.39} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` where `job_id` = '1648' limit 1
-- ","Time:":1.14} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` where `job_id` = '1648' limit 1
-- ","Time:":1.02} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.31} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.34} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":9.94} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.35} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` where `user_role`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.68} 
[2025-08-12 09:02:45] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.51} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":20.93} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.46} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_id` = '1648' and `parent_id` is null and `job_comments`.`deleted_at` is null order by `created_at` desc
-- ","Time:":0.51} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":2.81} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":24.95} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":22.14} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.61} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1'))
-- ","Time:":3.45} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1')) order by `id` desc limit 10 offset 0
-- ","Time:":9.61} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `job` where `job`.`id` in (1648)
-- ","Time:":0.31} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `companies` where `companies`.`id` in (84)
-- ","Time:":0.27} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select `users`.`email`, `job`.`id` as `laravel_through_key` from `users` inner join `job` on `job`.`employer_id` = `users`.`id` where `users`.`type` = 'employer' and `job`.`id` in ('1648')
-- ","Time:":0.33} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select `email`, `name`, `mobile`, `id` from `users` where `users`.`id` in ('512')
-- ","Time:":0.27} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `candidate_name`, `candidate_email`, `candidate_mobile`, `cv_public`, `cv_private`, `submit_cv_id` from `submit_cv_metas` where `submit_cv_metas`.`submit_cv_id` in (3165, 3167)
-- ","Time:":0.36} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.36} 
[2025-08-12 09:02:47] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"onboard\",\"level\":\"2\",\"career\":\"1\",\"salary_min\":\"1000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"undefined\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=onboard&career=1&group=&is_it=&level=2&salary_currency=undefined&salary_max=20000000&salary_min=1000000&skill_id=2', 'http://recland.local/admin/job/1648/edit', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/admin\\/job\\/1648\\/edit\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlB2eDZISE1HaitMNnNhZ2Q1ZzJPaHc9PSIsInZhbHVlIjoiMVVBOGhnVWZsVFFWS3BNQkR3azVMdVRMZHVLR2kycTJrTFlkUngyei9PYkJpRTUzQmRHKzg4a1E0dXJKU0xMWmJtUVQreTJSdFZaOG12V2lNUnJjWU83em43aEdUa2g4bjJvRThRN3Z4eFdsc3doZ3UwSnA2V2ptZXRIK3BWTDAiLCJtYWMiOiJkNjlhZjFmZGFlZDY4ZDY0NjQ2OWUzNDIzNGM0OTQyNDZlOTNhZmFkNzEyZTkyNzUzODNhNDdjM2RjNWRjZjQyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Imw1MU4zVE9CdWxRZVNXWFlidDFWekE9PSIsInZhbHVlIjoialVxTjBqVnRjSVVmcG9aRHZZVmlOUXhDbTdYWVB3Wi80YkhyazBuQU83RE5ZdnVxVW1KdVh6ZHdHK1ZOL1lidXlwU3g2YS9BcWEvVCtub0xuWnFMSW1nVnhKZlA3dnEycHVJeW1NVS9GeVVGaWJyS1p0QWZLNmJja2Q0N25VMTciLCJtYWMiOiJlODliMDk0YjZkMTgyN2I3NmQ1YWU0Mzg0MTMwNTg5OThhOGY3YjAyMDI0MDk1NzQ1MDU4MjRhNDJmZGI0ZGE2IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-12 09:02:47', '2025-08-12 09:02:47')
-- ","Time:":27.09} 
[2025-08-12 09:02:48] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":3.06} 
[2025-08-12 09:02:48] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":24.28} 
[2025-08-12 09:09:03] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job`
-- ","Time:":3.42} 
[2025-08-12 09:09:03] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1'
-- ","Time:":8.64} 
[2025-08-12 09:09:03] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":8.7} 
[2025-08-12 09:09:03] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.54} 
[2025-08-12 09:09:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job`
-- ","Time:":2.53} 
[2025-08-12 09:09:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1'
-- ","Time:":5.03} 
[2025-08-12 09:09:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'
-- ","Time:":5.12} 
[2025-08-12 09:09:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` order by `id` desc limit 25
-- ","Time:":1.54} 
[2025-08-12 09:09:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` in (84, 217, 286, 487, 491, 693, 694, 695, 696)
-- ","Time:":0.73} 
[2025-08-12 09:09:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` in (1723, 4629, 5421, 6139, 6156, 7372, 7412, 7415, 7456)
-- ","Time:":0.5} 
[2025-08-12 09:09:04] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as total_rows from (select `job`.`id` from `job`) as `job_aggregator`
-- ","Time:":0.23} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":3.67} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job/create', 'http://recland.local/employer/job', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6IjlSOWZzdTFIM29DRFNXcUoxc2VKeGc9PSIsInZhbHVlIjoibmNpcDRjNkdiZG1iNGFFTE1wOTg2L3hoOEh6VmY2cnc2OVk4RWlJQ3dDNWphdGc3dHBYbXdTMVVnMmVRTTdFSm5Sa01vODU1bFdlYWp2U3p3ZlJnR1VXOG1NNVlhaFVJeEtpSHpEbmowb1VQZ2VDMGJkSVpQalo2a25wZCt0M04iLCJtYWMiOiI0YWFjZjI5Y2ZhMjBjNjAzYzlkYTE5YjYwZjM2Y2UyMWQ5NjM5MWY0OGMxZDhkMzU3M2EwYzgwNTBlMmEwZjJiIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkVwdzZubVVudW14WUhtbjZXRVBDYWc9PSIsInZhbHVlIjoiMGprQkpqNkFDTmFoMHBBT3l5akozTFJuZUZCejNZTkd3VjFKSkIxb0szc1NuYk81UHFtODF5cDlneGIvdmVFVEYvUzdoWWNHZkZ4LzBsTnVWMTFpenVwaE5rSGwxbVEzQVFQOThPLzl5TTBCNlQwbEw4YkV3U0Y5bkpRU0lPNzAiLCJtYWMiOiI5MDg1MzViODE5OGNhZTI1YzhlMWJmNTE5Y2Q0MWUzYzAxNGE5YWNhMmNlZGVkY2FiM2MyYTNiYmUzNzZlNjY0IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754963962$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:09:05', '2025-08-12 09:09:05')
-- ","Time:":0.73} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.67} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.82} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.36} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.3} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1
-- ","Time:":0.56} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-create' limit 1
-- ","Time:":0.53} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.46} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":1.13} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.89} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.94} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.5} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.47} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.38} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.49} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.41} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.5} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.47} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.48} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 09:09:05] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":24.83} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6IjZPVlBlRXFpTDFSbnRRZnVNOEpadGc9PSIsInZhbHVlIjoiQ1JZK3JxUjFuRnY4UmxEUTd0UTlFdjRLSjJHWW82ZWErVE5lY09jQnplWHJSYW51OUlQdnlTRjRNajVaSEV3L2RjaC9sbFZ4VzQ1Y0IxMXhpVTFUVnhhOHI2RkFkaEQxbDYyVlFRdUNPMDI4TnZzSnBQSjRybFVub2ZISk5FVTkiLCJtYWMiOiIzYTU5MmViZmZlYmY1ZWNlMTgzNWQxMzMyOGFkODNiOWEwNzliMzlhZjQ0OGJjNzY2ODIyMjA1OWIyOTY3NGQ2IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ik05UEVJV3NJWmc2OUhJQ3dxN2NDSXc9PSIsInZhbHVlIjoiV21aWWQzVjl4QVBwSk5QNnhKcnNaZ3BiTVg3NkdtSFBBUFROMW1TMHd4NXc2ZzRSUExpM3pHTkNaUmpQK2VkM2lFajJDczBwUEhQQitmR1ozNFl6emtFd042SnVLWDBxcVhKOW01L2MxNHlZVWpjSUJlREkyWmxmbmlwY21Sbk0iLCJtYWMiOiJlOWEwYzQwYzI2NzI1ZjVmYWI0ZTVkZGMwODkzMjkxYTc0YWQzY2I0YzAwNGU4ZGE0ZDRhMDE3NTI2NDM2ZGQzIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964546$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:09:09', '2025-08-12 09:09:09')
-- ","Time:":0.58} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.58} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":2.6} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.32} 
[2025-08-12 09:09:09] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":28.71} 
[2025-08-12 09:09:59] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"career\":\"1\"}', 'http://recland.local/ajax/get-skill-main-it?career=1', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IkwrUkZaeHhPRjZ3SVJJYmowdW1NVFE9PSIsInZhbHVlIjoiRCtUZHRpdXVvRXZlNWRLbW1md3BzenlETjNSWlorNWtHWHNGeldubURFS0JDVGRwcjhxS0VPMGhZYjhnTHQ4YVNoQWRNZlovK25ZYWI5ZDV4ZDVuR013QUFYVkxxLytsZ0dBMmNqYUJVQUE0a01GcGhXVWxwSzg2akVEamVodXgiLCJtYWMiOiJhZWVmM2Y3MTBkMDgwYTUwODcwMGNlYmFjZjM4OGI0NTJkODQ3M2UxMWYxYjg1OThlMDUwN2MxYTMxMTFkYmM3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Imt2TXQyNktYTnR1d1VSZTFCQW1HZnc9PSIsInZhbHVlIjoibDlJN2dyY1BFa3RZbXpnM25ELzk1YUxLdmNCUEsvbm9iR0d3c3J0a3NsYjBVTVl3TlNsN29zUlNxSHpuL0VqQUZFeU9RNU4rUlg0aHZuNk5hSHJMVGJuQlZGYXdhcG9VRmk0d0VQbU1SV2R4YnNqRE9pTmRZNm1hRFkrbE5aTkEiLCJtYWMiOiJiYjZlZWVmOGM1NWU2MmYwYTAwOTk2OWNjMWUzZmUyMjZhNDc4NTU5MDUyNzE1N2VjYTg1YmQ1NDc3MzhhN2FlIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:09:59', '2025-08-12 09:09:59')
-- ","Time:":20.6} 
[2025-08-12 09:09:59] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops`
-- ","Time:":0.77} 
[2025-08-12 09:10:01] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":\"1\",\"is_it\":\"false\"}', 'http://recland.local/ajax/get-level?group=1&is_it=false', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6InMxTkl6MWFPUld2dFpLTWs1UHBubEE9PSIsInZhbHVlIjoieUVPTTFEeWFZYlhZUzdSUjRRcUh0U2NraU9zaGdXd09sV0M3QmRDR3cyc0RkVU1rR3dIRFFHRk1EM2JpY25aMHN0bkxWVHpMMEprNWoybXJIemJYaGMrbmMwRzVqREdPM3FuRUErbGZZbUwvNEhEcHJJN0NvdnhqQUZZK2RlTFIiLCJtYWMiOiIwZjliZGIwNDNlMGUxY2JjNDc4ZTFiYWU5N2YzNzlkNTk2NTIwZTk1MWE0ODA5NGEwNDA4YmU4NzgxMDBmNDE0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InRGVTN2SEtYMWJjeitBMW5RUDJFSlE9PSIsInZhbHVlIjoiQ21BbVhZaDlnWkNDSnpOazM3Nk81VGxScUthUUk3ZVhHQkZLaDJEdHlneGJ3UlpJRXlYVUNBbHFNU0hWN1Rja0pEV2ZEdDFObE5Lb01MaWVWMURTVWcvRDFZeVVRN3FiUjYwYkUwMU1YM1h5TXRZTE1mL2RSNWsvNkF6SThGVVAiLCJtYWMiOiJkNWY1ZGJiZDZlY2M3ODAyYTA3OTliMzBmMjE0MmVmMzIwYmZjYjY0ZjUxOTljZjQyMGFjOWFkYzU5ZjI4MmU0IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:01', '2025-08-12 09:10:01')
-- ","Time:":4.05} 
[2025-08-12 09:10:01] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `group` = '1'
-- ","Time:":0.73} 
[2025-08-12 09:10:09] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"onboard\",\"level\":\"3\",\"career\":\"1\",\"salary_min\":\"10000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"VND\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=onboard&career=1&group=&is_it=&level=3&salary_currency=VND&salary_max=20000000&salary_min=10000000&skill_id=2', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6InZXTWlzNWFOQ2pVbmYybk9HR3RZUHc9PSIsInZhbHVlIjoiYmRtYXAyd1VVRkp1em8rQXBDUDRsOFU2b2RqWEQ3SThnRmFlTXBiL3JqOUpDalVIbFVWNU8rVTdSOEpBMkhXMHFtT1NEdVJrd01qUS9FQmMyYktRbTRkc3ZLN1huQkxNTmwwa3VSb2Q3YmR2N2ZtTzVkVmhPTG9RRFYrNTVNY0wiLCJtYWMiOiIzNmEwNzdiNDA2ODY0NWRiYWY4ZGM0OTBkNmMxNTRhNjc1ZGIyYTAzYTQwYzhiMjkwMDA3ZjljYjBlZmQ4NTFlIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkMzTEJUUlcxM3JYSGQwREFXcG5mcFE9PSIsInZhbHVlIjoiQlJIMzA2RTZqa1ZTdDN0VnNHdXQ0OGFsOENYUExHamtWUW55K0tsV3FnMTE1TFFaOFNCdkdxbFVIOUpWdDZYUkUyUTFmYldxcFVCTW04SWZVZjdmeFd2K3MrVzY5WWRacG5HcVI3SWphWHR1OFMxNUROQWkyYjY3Q1VjY211bU0iLCJtYWMiOiI2NWY1M2FjMWZkOGRjOGQwZTYzZjUwNGI0NTI5ZTIzMzljOWRhY2VkODZkNmQ3NTA1YjMyZTQxYTNkOTIzMzI4IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:09', '2025-08-12 09:10:09')
-- ","Time:":2.6} 
[2025-08-12 09:10:13] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"cv\",\"level\":\"3\",\"career\":\"1\",\"salary_min\":\"10000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"VND\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_currency=VND&salary_max=20000000&salary_min=10000000&skill_id=2', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IlJGZitlb3IrWUoyU21hdkUreTFaL1E9PSIsInZhbHVlIjoiOGZ3eC9JQVp2emQwNFJOOFFIYk9wZUgzKzhnY3d1a0pPSURxTTZVSmJUUDBqdFA2ODdsa2FmOEoweUd4bXB3UFlXNWFSOHArcUFlYjZ0RVdTRzdoZC8xSzEwWHdOWDBwSEI3cWlJc3Q0WmJuS0RITEcyNmtYVnR2L1NrYWIvemMiLCJtYWMiOiJjNTRhOGM4YzFhN2M0YzdiNGU0ZTU1ZTBmMTZmZjhkMmVkNTlkN2I2NDg2N2RmYjdkNDA3YjdmNWVmODU3MTAxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlJtblpGOTdHZlRuUlh4UTNoN1l0WGc9PSIsInZhbHVlIjoiL3k4cE9lRU1uUENNT1VCSU1UWXcrQytKT0xrT0JvUVR0cHlDa3lGcXRBSVBxZUFNQTVOV3ZtM2Q4S0k0S1d0S0grS1B5OXlEZUMzaVRoVHY0OUhSSWpXRzRucEUxL0MyNUl5b1ZLRk40aXhWUTZ6bFljUjl5T3B0OVloRitHelAiLCJtYWMiOiI2YzIxYWQ3YzI1YTg5N2FmNzU1MzhhODZmMTQ4Y2JlZDlhYjg5ZDU2NWZkNmRkYzMwNDNlMjRlMWRlM2VhNDNhIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:13', '2025-08-12 09:10:13')
-- ","Time:":21.39} 
[2025-08-12 09:10:13] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `level_by_job_tops`.`id` = '3' limit 1
-- ","Time:":0.67} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-check-coupon' limit 1
-- ","Time:":20.84} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IkszTHF4a1ZJOTNFZHo2Rm5sL0lZL0E9PSIsInZhbHVlIjoibEZPRCtWL3l6d01Wd3BvM1dONjE2b0xvSzdXV3plSHlGWjZDNktvSldqZ0Z0RmR0R1paNE9idDd5ZUQxYmIyVUNpUjlCUVhhMFl5czdmdG9veS9PMGZPMjhjV085OUZJWHdXdnhQb2cySVRHWWlUMk5qUVMzazFwY1VaSVZVZmIiLCJtYWMiOiJlNmRlMTc2NzAwZjcwY2U0MzRiYzkyYjMxN2JkMTExNGEzNzFmYTc4YTQ5ZDNlNGNlMDBkZjBlMmE1MzQ5Y2FkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjBXZDVsc3d1Z3M0bDd4QmNTYTlTcEE9PSIsInZhbHVlIjoiUUxtUmthNlVRUVVaMmdaQ3VzWWNYSmlITmUyN1FVUWZOZnJhRENyalNHVFowR0VOM2RVTUdaL1JoVFlLMTFUSC9Fdzd4NWRVV2tSZ1pXNUppaUVveEpMMDEydlJyL1JwNHhtRGY0YzZnNC9UU2tOS28xNHJXOXdDTmZrQld3TUIiLCJtYWMiOiIwOTI1YmZmNjhhOTg2MjYxNGEwYjM4OTEwNTkyMzNlMjViMDlhOGM5NmY2MmU0OGU1MGZjZTdjNzE3ZjEzZjk1IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:23', '2025-08-12 09:10:23')
-- ","Time:":0.53} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.56} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.74} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.13} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1
-- ","Time:":0.42} 
[2025-08-12 09:10:23] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')
-- ","Time:":24.12} 
[2025-08-12 09:10:24] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"cv\",\"level\":\"3\",\"career\":\"1\",\"salary_min\":\"10000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"VND\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_currency=VND&salary_max=20000000&salary_min=10000000&skill_id=2', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6ImxVTE1WcWZlTGlUZm90Q043M2Y1RkE9PSIsInZhbHVlIjoicE1YZUlVL2lDOVZaOFBjK2krWWhSRUlxbW5zQXJiZE45YUREc0REK2JvWFVrSmM0bndjWHd4a3ZSQTVLdkFwM3hxM0w5aWI4WGk0NDdob2JRcFZFbHBucnNmY0VGRGZYZHNPeGFlK2FDMVQzYW5nQU03SFVtSHFudHM1YS9HaXoiLCJtYWMiOiIwMDFjMmY5NjY4YzYxZGJmOTllYTJkM2M3YmZmZWE5ZjRmMzdhYmUxYzJlZTk0ODAxYWI4NzNjZDMyMjE0NDdjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Inlta3lNZ0JVcnNEaGRHSHZNbmtxNmc9PSIsInZhbHVlIjoiNU1BTERRMEZhYVhDa2hWZmViVTg4UERrdml4M1dTMG0vTCtFakgxQ21kOWRaRmtLb1BFa0lMdGx2aFBVUFpuMlhzdU9VZjQyejZIb3l3aWhuYVYyeE4rNUZRN2N0WXp6K29MYkZPbXg0bWJqbWVMN09lTk1PeGdTN2IyNU9sVWEiLCJtYWMiOiJkYzQ4NDBlYWJlNWZlODk2MTllOWZjN2M1YmQyMWQxNWJhM2M0ZDY1MGI3ODE3YjE0NWQyYTMxYWYwMjI0ZmU0IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:24', '2025-08-12 09:10:24')
-- ","Time:":19.48} 
[2025-08-12 09:10:24] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `level_by_job_tops`.`id` = '3' limit 1
-- ","Time:":0.7} 
[2025-08-12 09:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"cv\",\"level\":\"3\",\"career\":\"1\",\"salary_min\":\"10000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"VND\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_currency=VND&salary_max=20000000&salary_min=10000000&skill_id=2', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6Ii9ZaElEZ1BoZENVL2tMZndzN3FaWGc9PSIsInZhbHVlIjoiYU5QUUxuOGFPT3ovME5ITHBhS3Q5UGIxaHpRcDhFMEo0UHFZZ011Z2MwT0U3a3VVTGdFaTBVVHdsUi9tQ2NFR1VrVERRWmt0clgwUmx1RzRjc3pIRVFJUmN4M21yN0xoamVhOWpuNFJYL0FaSWJpb0ZFckpZRElKeWRTQnArUmwiLCJtYWMiOiJhYzU2Nzg2ODA2NDIyOWUwNmRhNWNhYmM0YWE2NmRmNzVhMmM2YTIxOTJlYzYzOGEwMWU3NGE3MmQwYWRjMTQ5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImVFZmpRc1h3aVZXYVpRVFk3OUw4Rmc9PSIsInZhbHVlIjoidUFSZGNiZWYvditZQmhFV3lhZlJObjIvbmJTY2t5U1dLcW5ZNmwvZzdmZTZ3Vm5qc0d1b0Z0VWFFZVF3a21wQllOUmZmMko4cUhCbzBVdkF5ZkpLUkpGYmh2SENqeTRIL0wvNlNGYTRtVVN2bVVDL0JVWjNmV0o1TDJlRGlYQmUiLCJtYWMiOiJlMDUyZTFiOTRkMWVkZGI5YTIxNzQ0MjA4ZDAyMjc3YTlkYTQ5MTIzYmU4OWM3ZWE3NmE2YjAzYWY4NWRhMjI3IiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:26', '2025-08-12 09:10:26')
-- ","Time:":2.98} 
[2025-08-12 09:10:26] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `level_by_job_tops`.`id` = '3' limit 1
-- ","Time:":0.67} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-store' limit 1
-- ","Time:":21.48} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\",\"coupon_code\":null,\"collaborator_cost\":null,\"name\":\"L\\u1eadp tr\\u00ecnh PHP\",\"expire_at\":\"18\\/08\\/2025\",\"vacancies\":\"5\",\"career\":\"1\",\"skill\":\"2\",\"rank\":\"3\",\"salary_currency\":\"VND\",\"salary_min\":\"10000000\",\"salary_currency_max\":\"VND\",\"salary_max\":\"20000000\",\"bonus_type\":\"cv\",\"bonus\":\"2000000\",\"type\":\"part-time\",\"address\":[{\"area\":\"ha-noi\",\"address\":\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\"},null],\"jd_description\":\"<p>NEWBIE2025<\\/p>\",\"jd_request\":\"<p>NEWBIE2025<\\/p>\",\"jd_welfare\":\"<p>NEWBIE2025<\\/p>\"}', 'http://recland.local/employer/job/store', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"2399\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundaryMAevSAZOKefzwIaK\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; XSRF-TOKEN=eyJpdiI6IjFRTjdDNFdHSDJVQng3eTUyRWhjWHc9PSIsInZhbHVlIjoieUIva0o0c1dJTmN1M1EyandOMll0OXcrNGlwcFZYaEJWcjg0UU5leTZhemtmSjhBeXJVNk5kRFFORG00Wkp1bVd2Rk9BMVhqZ3huSlR2Y01KWXZ3dnVkMVRZbEx0bVNHNTBOTGUvbjRUeE51QWh4bjgwaUdZQmpyS1JmVWowQnciLCJtYWMiOiI3MTFiMDMzNjU2NjVjOGQ2NTgwYjg2ZjNmNjhlN2NiZTIxY2M0MDUyZDY0NDJkMzIzMjcyOWZlY2ZlZDhlY2FhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InhQZHBXYzgyN0JIN2EwYVVzaDhFNGc9PSIsInZhbHVlIjoibm5PVDJ2dWE3QldlN2tuRVhEcHhEQy9FV2NjbFc1SWZkQ01vUlF6aSs3Wkg5V1V5UFNha3RZeXV0cWtzSGRmR2RWLzZOUE1xYzN3VTgxN0ozMlpWZ0Y2bHFxRVpmOVV6Q2F3b0MrTE9ZQm4ydWhtUmEwMWkvVVdXQ0ZqRmZLVUwiLCJtYWMiOiJhMjIyMjFmZWYxMTIyZjIwZmVkZDYyY2QwMjQ4MjgyMWZiZDY2ODIyYThjZDlhYWZiN2NiNmMxN2NhYjgyNTllIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964629$j59$l0$h0\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:29', '2025-08-12 09:10:29')
-- ","Time:":0.56} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.56} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.75} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.32} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.18} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.53} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job` (`name`, `slug`, `company_id`, `employer_id`, `expire_at`, `vacancies`, `type`, `urgent`, `remote`, `career`, `rank`, `skill_id`, `bonus`, `bonus_type`, `skills`, `address`, `jd_description`, `jd_request`, `jd_welfare`, `salary_min`, `salary_max`, `salary_currency`, `coupon_code`, `is_active`, `updated_at`, `created_at`) values ('Lập trình PHP', 'lap-trinh-php-fC4uZ2Oz', '84', '1723', '2025-08-18', '5', 'part-time', '0', '0', '1', '3', '2', '2000000', 'cv', 'Môi trường/ Xử lý chất thải', '[{\"area\":\"ha-noi\",\"address\":\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\"},null]', '<p>NEWBIE2025</p>', '<p>NEWBIE2025</p>', '<p>NEWBIE2025</p>', '10000000', '20000000', 'VND', '', '0', '2025-08-12 09:10:29', '2025-08-12 09:10:29')
-- ","Time:":0.96} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":0.57} 
[2025-08-12 09:10:29] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\"name\":\"L\\u1eadp tr\\u00ecnh PHP\",\"slug\":\"lap-trinh-php-fC4uZ2Oz\",\"company_id\":84,\"employer_id\":1723,\"expire_at\":\"2025-08-18\",\"vacancies\":\"5\",\"type\":\"part-time\",\"urgent\":0,\"remote\":0,\"career\":\"1\",\"rank\":\"3\",\"skill_id\":\"2\",\"bonus\":\"2000000\",\"bonus_type\":\"cv\",\"skills\":\"M\\u00f4i tr\\u01b0\\u1eddng\\/ X\\u1eed l\\u00fd ch\\u1ea5t th\\u1ea3i\",\"address\":\"[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\\\u00e0 AC, ng\\\\u00f5 78 Duy T\\\\u00e2n - C\\\\u1ea7u Gi\\\\u1ea5y - H\\\\u00e0 N\\\\u1ed9i\\\"},null]\",\"jd_description\":\"<p>NEWBIE2025<\\/p>\",\"jd_request\":\"<p>NEWBIE2025<\\/p>\",\"jd_welfare\":\"<p>NEWBIE2025<\\/p>\",\"salary_min\":\"10000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"VND\",\"coupon_code\":null,\"is_active\":0,\"id\":1649}', 'created', '1649', 'App\\Models\\Job', '1', 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://recland.local/employer/job/store', '2025-08-12 09:10:29', '2025-08-12 09:10:29')
-- ","Time:":1.0} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Listeners\\NotifyGoogleIndexingApi', 'sync', '{\"uuid\":\"77dc9355-bda3-46fd-855e-2ab962ef2977\",\"displayName\":\"App\\\\Listeners\\\\NotifyGoogleIndexingApi\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Events\\\\CallQueuedListener\",\"command\":\"O:36:\\\"Illuminate\\\\Events\\\\CallQueuedListener\\\":19:{s:5:\\\"class\\\";s:37:\\\"App\\\\Listeners\\\\NotifyGoogleIndexingApi\\\";s:6:\\\"method\\\";s:6:\\\"handle\\\";s:4:\\\"data\\\";a:1:{i:0;O:21:\\\"App\\\\Events\\\\JobUpdated\\\":2:{s:3:\\\"job\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:14:\\\"App\\\\Models\\\\Job\\\";s:2:\\\"id\\\";i:1649;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:6:\\\"action\\\";s:7:\\\"created\\\";}}s:5:\\\"tries\\\";N;s:13:\\\"maxExceptions\\\";N;s:7:\\\"backoff\\\";N;s:10:\\\"retryUntil\\\";N;s:7:\\\"timeout\\\";N;s:17:\\\"shouldBeEncrypted\\\";b:0;s:3:\\\"job\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}}\"}}', '2025-08-12 09:10:31', '2025-08-12 09:10:31')
-- ","Time:":0.69} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1649' limit 1
-- ","Time:":0.96} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_seo` (`job_id`, `title_vi`, `title_en`, `keyword_vi`, `keyword_en`, `updated_at`, `created_at`) values ('1649', 'Lập trình PHP', 'Lập trình PHP', 'Môi trường/ Xử lý chất thải', 'Môi trường/ Xử lý chất thải', '2025-08-12 09:10:31', '2025-08-12 09:10:31')
-- ","Time:":0.29} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_meta` (`job_id`, `priority`, `updated_at`, `created_at`) values ('1649', 'New', '2025-08-12 09:10:31', '2025-08-12 09:10:31')
-- ","Time:":0.26} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\RegisterJob', 'sync', '{\"uuid\":\"b39e0f74-8fb5-4efe-b311-f23bd0e0c9ac\",\"displayName\":\"App\\\\Notifications\\\\RegisterJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1723;}s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:29:\\\"App\\\\Notifications\\\\RegisterJob\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1723;s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"}}', '2025-08-12 09:10:31', '2025-08-12 09:10:31')
-- ","Time:":1.02} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` = '1723' limit 1
-- ","Time:":0.81} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.54} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.24} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` in (1723)
-- ","Time:":3.89} 
[2025-08-12 09:10:31] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 09:10:33] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\"<EMAIL>\"]', '[\"<EMAIL>\"]', '[]', '[Recland] [TẠO JOB MỚI THÀNH CÔNG]', '<!DOCTYPE html>

<html>



<body width=\"100%\"

    style=\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\">

    <center style=\"width: 100%; background-color: #f1f1f1;\">

        <div style=\"max-width: 600px; margin: 0 auto;\">

            <table align=\"center\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\"

                style=\"margin: auto;\">

                <tr>

                    <td>

                        <div style=\"text-align: center;padding: 25px 0;background: #FCFCFE;\">

                            <img style=\"max-width: 100%\"

                                src=\"http://recland.local/frontend/assets_v2/images/graphics/logo-250.png?v=689aa298e8c36\">

                        </div>

                    </td>

                </tr>

            </table>

            <div style=\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\">

                <div

                    style=\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;

                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=689aa298e8c48);

                background-repeat: no-repeat;background-size: 100%;\">

                    <table>

                        <tr>

                            <td>

                                <div

                                    style=\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\">

                                    <div style=\"margin-bottom: 14px\">

                                                                            </div>

                                    <div>

                                                                            </div>



                                </div>

                            </td>

                        </tr>

                    </table>

                    <table style=\"width: 100%\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"

                        width=\"100%\">

                        <tr>

                            <td>

                                    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        Xin chào, Hậu Test

        <br>

        Recland thông báo job của bạn đã được tạo mới thành công.

        <p><b>Trân trọng,</b></p>

        <p><i>Đội ngũ Recland.</i></p>

    </div>

    <div style=\"border: 5px solid #F7F7F7;\"></div>

    <div

        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">

        Hello, Hậu Test

        <br>

        Recland announces that your job has been successfully created.

        <p><b>Best regards,</b></p>

        <p><i>Recland team.</i></p>

    </div>

                            </td>

                        </tr>

                        <tr>

                            <td>

                                <div style=\"padding:12px 0\">

                                    <div

                                        style=\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\">

                                        <div style=\"margin-bottom: 12px;text-align: center\">

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=689aa298e8c76\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=689aa298e8c81\"></a>

                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img

                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=689aa298e8c8c\"></a>

                                        </div>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\">

                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>

                                        <p

                                            style=\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\">

                                            © 2022 Recland.co</p>

                                    </div>

                                </div>



                            </td>

                        </tr>

                    </table>

                </div>

            </div>

        </div>

    </center>

</body>



</html>

', '12d82a386e28a0a0ed26b170edfef643', '0', '2025-08-12 09:10:33', '2025-08-12 09:10:33')
-- ","Time:":0.78} 
[2025-08-12 09:10:34] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\"uuid\":\"eacdcac8-606e-4bcb-85d6-cdc0d252daca\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:5635:\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/assets_v2\\/images\\/graphics\\/logo-250.png?v=689aa298e8c36\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689aa298e8c48);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Xin ch\\u00e0o, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland th\\u00f4ng b\\u00e1o job c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c t\\u1ea1o m\\u1edbi th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Hello, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland announces that your job has been successfully created.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689aa298e8c76\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689aa298e8c81\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689aa298e8c8c\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:4:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:40:\\\"[Recland] [T\\u1ea0O JOB M\\u1edaI TH\\u00c0NH C\\u00d4NG]\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:2:\\\"[]\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}', '2025-08-12 09:10:34', '2025-08-12 09:10:34')
-- ","Time:":0.74} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` where `hash` = '1f256ac9dcb7bc2317c51047224616c5' and `created_at` >= '2025-08-12 09:05:34'
-- ","Time:":275.33} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\RegisterJob', 'sync', '{\"uuid\":\"e44d6cc0-a36f-423c-b2d9-56726f73f54d\",\"displayName\":\"App\\\\Notifications\\\\RegisterJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1723;}s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:29:\\\"App\\\\Notifications\\\\RegisterJob\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1723;s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"}}', '2025-08-12 09:10:35', '2025-08-12 09:10:35')
-- ","Time:":0.55} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` = '1723' limit 1
-- ","Time:":0.65} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.77} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.29} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` in (1723)
-- ","Time:":6.08} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `users`.`id` in (1723)
-- ","Time:":0.5} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select `value`, `key` from `settings` where `key` like '%settings.vi.notification%'
-- ","Time:":6.16} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select `value`, `key` from `settings` where `key` like '%settings.en.notification%'
-- ","Time:":4.43} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d', 'App\\Notifications\\RegisterJob', '{\"content_vi\":\"Nh\\u00e0 tuy\\u1ec3n d\\u1ee5ng \\u0111\\u00e3 t\\u1ea1o job m\\u1edbi th\\u00e0nh c\\u00f4ng.\",\"content_en\":\"The employer has successfully created a new job.\"}', '', '1723', 'App\\Models\\User', '2025-08-12 09:10:35', '2025-08-12 09:10:35')
-- ","Time:":0.38} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":19.32} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/job', 'http://recland.local/employer/job/create', '[\"vi\",\"en-us\",\"en\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.**********.**********; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964629$j59$l0$h0; XSRF-TOKEN=eyJpdiI6IndZUHNKUVkwY1IxRm1nTHVKd3R0enc9PSIsInZhbHVlIjoiUkNURXp1TVBIWEkwUDVjQ2trditPcW05aXhVbTE0cmFXTFl2dEY1UlVicmhDYm53RkRPbUxobVI2Q0tTdzR5SnNZeTY5TXFuQm9Xa1FyaUFId0t4TmFjR0tqREl3T2kwQnppSjZjdU1hT0N2blo0bzdGa214WTBuMWJHdEc3c2QiLCJtYWMiOiI1YTNlYjE1MTBjNmE2Y2M1ODQzY2NlZDhjMTJjZDkwODg4MDNlODJkZmE2ZmY4NmZmMDk5YTQ4N2RkY2ZkNDE4IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Ikd3N1ZmUy94dXVPMit2WnZiSWoveEE9PSIsInZhbHVlIjoiMjlRdG1HQ1ZrUGdFcXBXellsbnBiZXN6OXF4dUxlMFc1ZXJHQXFkUkVhdDVFZHhuTTJaMkpWak5sVGl4cGxvRE8zOXF2dCs5MzNqclJsT3plbStubHNwNVV2Vmw1Y0RQTjRiYlJLbld4Szd5QUZ1SmxmS241WHhUbVY4NXJTcHYiLCJtYWMiOiI5ZWFjOWIxMTc2ZjNjZmFmMTYyMDdjN2QyY2ExYjQ5NzFlNDQ0ZjkwMDViMzk3N2NmYTc1N2Y4OWRlMGY0NGYyIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:35', '2025-08-12 09:10:35')
-- ","Time:":0.55} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.58} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.77} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` where 0 = 1
-- ","Time:":0.31} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1
-- ","Time:":4.16} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` where `key` = 'employer-job' limit 1
-- ","Time:":0.44} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `employer_id` = '1723' and `company_id` = '84'
-- ","Time:":7.77} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `employer_id` = '1723' and `company_id` = '84' order by `id` desc limit 10 offset 0
-- ","Time:":17.6} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` where `submit_cvs`.`job_id` in (483, 1645, 1646, 1647, 1648, 1649)
-- ","Time:":5.12} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '1723'
-- ","Time:":3.08} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `job` where `is_active` = '0' and `employer_id` = '1723'
-- ","Time:":3.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1649' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.45} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1648' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.43} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1647' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1646' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '1645' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `submit_cvs`.`job_id` = '483' and `submit_cvs`.`job_id` is not null and `status` not in ('1', '2')
-- ","Time:":1.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1
-- ","Time:":0.33} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc
-- ","Time:":0.4} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc
-- ","Time:":0.37} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.41} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.46} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.45} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.39} 
[2025-08-12 09:10:35] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` where `employer_types`.`user_id` in (1723)
-- ","Time:":0.4} 
[2025-08-12 09:10:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":22.5} 
[2025-08-12 09:10:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.61} 
[2025-08-12 09:10:38] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.66} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops`
-- ","Time:":0.54} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.38} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` where `id` = '2' limit 1
-- ","Time:":0.36} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` where `group` = '1'
-- ","Time:":0.49} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.62} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` where `job_id` = '1648' limit 1
-- ","Time:":2.51} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` where `job_id` = '1648' limit 1
-- ","Time:":2.13} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select `name` from `skills` order by `id` desc limit 50
-- ","Time:":0.36} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.4} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":15.35} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.41} 
[2025-08-12 09:10:39] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` where `user_role`.`user_id` in (1723)
-- ","Time:":0.3} 
[2025-08-12 09:10:40] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` where `job`.`id` = '1648' limit 1
-- ","Time:":0.84} 
[2025-08-12 09:10:40] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":0.52} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":19.53} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.58} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_id` = '1648' and `parent_id` is null and `job_comments`.`deleted_at` is null order by `created_at` desc
-- ","Time:":0.73} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\"group\":null,\"is_it\":null,\"bonus_type\":\"onboard\",\"level\":\"2\",\"career\":\"1\",\"salary_min\":\"1000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"undefined\",\"skill_id\":\"2\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=onboard&career=1&group=&is_it=&level=2&salary_currency=undefined&salary_max=20000000&salary_min=1000000&skill_id=2', 'http://recland.local/admin/job/1648/edit', '[\"en-us\",\"en\",\"vi\",\"nl\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36 Edg\\/*********\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/admin\\/job\\/1648\\/edit\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,vi;q=0.8,nl;q=0.7\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkVMSHpDSWwyWUFud2J6bmZGcm8wcUE9PSIsInZhbHVlIjoiZkpoeW5zOWtqdmMvM0FmcStGbDFuNnl5NVh6S2tNaFVYelpUY0Y4TUxlajgwS21PdktDbHFGeit1S2YraEh1ck9qd2Z0S3htZzk2cVpGSEt4SGQrYU0vRmNoeEY2VkU4WDBIUUpJNHYxbUV6NWpqVThBWkthVVlEQlNzSC9QU2wiLCJtYWMiOiI5YWY4OTEwMjk4Yzg1N2NkYjczY2Q5NTQ1ZjYyOWIyZTBiNWY2ZWEyMjA2ZjQzNzQxYmVjNDdiMmY5M2FjNDViIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImxGZnBJSnhaOWttTTEzOWtWdGF6bnc9PSIsInZhbHVlIjoiM0FRd2MrRmp0UWdFTlMyUlRmeGwwa2s4YUpVamhyK2FiaTJwcWk2ZUkyQ0VjY2poV1lWZjRFZlVkWWFvT29Gd2oxNlVFR2UxdnU1alJ1TkJSeFpVNlJrSWVoaytCdEJ4N3BIaVZUczVXQjNQSXpkWEcvQkFHYkVRRGlGN1JCLzMiLCJtYWMiOiIyMzc3MTM1OTBhNTc2YzI3NDNkZmEyZTE2OWMyYzU3ZjEwN2Q2ZGY5ZmFmZWJlNzE2ZWIzMWE3OTE5OWY2OGViIiwidGFnIjoiIn0%3D\"]}', 'WebKit', 'Windows', 'Edge', '127.0.0.1', '', '', '2025-08-12 09:10:41', '2025-08-12 09:10:41')
-- ","Time:":20.85} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1' limit 1
-- ","Time:":23.5} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)
-- ","Time:":0.59} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select count(*) as aggregate from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1'))
-- ","Time:":3.48} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1')) order by `id` desc limit 10 offset 0
-- ","Time:":12.94} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `job` where `job`.`id` in (1648)
-- ","Time:":0.42} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `name` from `companies` where `companies`.`id` in (84)
-- ","Time:":0.43} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `users`.`email`, `job`.`id` as `laravel_through_key` from `users` inner join `job` on `job`.`employer_id` = `users`.`id` where `users`.`type` = 'employer' and `job`.`id` in ('1648')
-- ","Time:":0.33} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `email`, `name`, `mobile`, `id` from `users` where `users`.`id` in ('512')
-- ","Time:":0.25} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select `id`, `candidate_name`, `candidate_email`, `candidate_mobile`, `cv_public`, `cv_private`, `submit_cv_id` from `submit_cv_metas` where `submit_cv_metas`.`submit_cv_id` in (3165, 3167)
-- ","Time:":0.36} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":27.03} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `id` = '1723' limit 1
-- ","Time:":0.35} 
[2025-08-12 09:10:41] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":10.42} 
[2025-08-12 09:10:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` where `companies`.`id` = '84' limit 1
-- ","Time:":2.32} 
[2025-08-12 09:10:42] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` where `type` = 'employer' and `company_id` = '84'
-- ","Time:":14.52} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":4.98} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":6.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":2.01} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.52} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":1.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":1.45} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.91} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.48} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.52} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.52} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":2.02} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":0.62} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":0.61} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.53} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.59} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.48} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.48} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.6} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:07] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":3.81} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":3.64} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.85} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.24} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.55} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":1.73} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.43} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.24} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.58} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.48} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.48} 
[2025-08-12 09:12:20] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":4.08} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.53} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":4.76} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":1.08} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.24} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.96} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":1.49} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:52] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":1.51} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.24} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.29} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.65} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.55} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.27} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.5} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.5} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.54} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.52} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.48} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.26} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.28} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.51} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.55} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:53] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":4.29} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs` limit 1
-- ","Time:":4.02} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_albums` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `about_us_album_photos` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `audits` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `banners` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bonus` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `bug_reports` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `categories` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":1.26} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `companies` limit 1
-- ","Time:":0.61} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `contact_uses` limit 1
-- ","Time:":0.43} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `coupon_code` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `deposits` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `discuss` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.5} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `email_logs` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employee_roles` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `employer_types` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `information_contacts` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.51} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_search_cvs` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_users` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_cvs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":2.01} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `itnavi_user_external_sources` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job` limit 1
-- ","Time:":1.95} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_comments` where `job_comments`.`deleted_at` is null limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_logs` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_meta` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_seo` limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `job_tops` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_job_tops` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `level_by_skill_mains` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `meta_data` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `password_resets` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payins` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payin_months` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payment_debits` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_logs` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `payout_log_histories` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `posts` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_meta` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `post_seo` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `reports` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `roles` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `seos` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `settings` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skills` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `skill_mains` limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_books` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_discuss` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.42} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_payment` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_history_status` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.4} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cv_metas` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_onboards` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `submit_cvs_payment_debits` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `survey_fields` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.48} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `testimonials` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `top_recs` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.47} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `transactions` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `users` limit 1
-- ","Time:":0.46} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_infos` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_job_cares` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_role` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.3} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `user_survey_results` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.5} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallets` limit 1
-- ","Time:":0.25} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.31} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `wallet_transactions` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.38} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cvs` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`deleted_at` is null limit 1
-- ","Time:":0.45} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.6} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`deleted_at` is null limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_books` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.5} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_discuss` limit 1
-- ","Time:":0.34} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_history_status` limit 1
-- ","Time:":0.39} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.51} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_evaluates` limit 1
-- ","Time:":0.33} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_history_buys` limit 1
-- ","Time:":0.41} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.49} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_qas` limit 1
-- ","Time:":0.35} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_saves` limit 1
-- ","Time:":0.32} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.36} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `warehouse_cv_selling_buy_onboards` limit 1
-- ","Time:":0.37} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.44} 
[2025-08-12 09:12:58] local.DEBUG: [SQL EXEC] {"SQL:":"
select * from `zalopay_transactions` limit 1
-- ","Time:":0.36} 
