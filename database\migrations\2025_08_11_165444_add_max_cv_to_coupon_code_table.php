<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coupon_code', function (Blueprint $table) {
            $table->integer('max_cv')->nullable()->after('collaborator_cost')->comment('Giới hạn số lượng CV tối đa');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coupon_code', function (Blueprint $table) {
            $table->dropColumn('max_cv');
        });
    }
};
