# Workflow: <PERSON><PERSON><PERSON> thiện validation mã khuyến mại và thêm field admin edit

**<PERSON><PERSON><PERSON> thực hiện:** 12/08/2025  
**<PERSON><PERSON><PERSON> tiêu:** <PERSON><PERSON>i thiện xử lý mã khuyến mại trong frontend và thêm field coupon_code vào form edit admin

## Tác vụ đã hoàn thành

### 1. <PERSON><PERSON>i thiện xử lý mã khuyến mại trong frontend create form

**File:** `resources/views/frontend/pages/employer/create.blade.php`

#### 1.1 Thêm validation khi submit form
- Thêm kiểm tra `couponApplied` trước khi submit form
- Hiển thị alert nếu mã khuyến mại không hợp lệ
- Xóa giá trị mã khuyến mại không hợp lệ trước khi submit

```javascript
// Kiểm tra mã khuyến mại trướ<PERSON> khi submit
@if(config('app.enable_coupon_code', true))
let couponCode = $('#coupon-code').val().trim();
if (couponCode !== '' && !couponApplied) {
    alert('Mã khuyến mại không hợp lệ. Vui lòng kiểm tra lại hoặc để trống nếu không sử dụng.');
    $('#coupon-code').focus();
    return;
}

// Nếu mã khuyến mại không hợp lệ, xóa giá trị trước khi submit
if (couponCode !== '' && !couponApplied) {
    $('#coupon-code').val('');
}
@endif
```

#### 1.2 Thêm validation khi thay đổi input
- Thêm event handler `input` để theo dõi thay đổi
- Reset trạng thái khi người dùng thay đổi mã sau khi đã áp dụng thành công
- Hiển thị thông báo warning khi mã thay đổi

```javascript
$('#coupon-code').on('input', function() {
    let code = $(this).val().trim();
    
    // Nếu người dùng thay đổi mã sau khi đã áp dụng thành công
    if (couponApplied && code !== $(this).data('applied-code')) {
        resetCouponState();
        $('#coupon-message')
            .removeClass('text-success')
            .addClass('text-warning')
            .text('Mã khuyến mại đã thay đổi. Vui lòng nhấn Tab hoặc click ra ngoài để kiểm tra lại.');
    }
});
```

#### 1.3 Cập nhật function applyCoupon
- Lưu mã đã áp dụng thành công vào data attribute
- Cập nhật class CSS để xử lý text-warning

```javascript
function applyCoupon(data, message) {
    couponApplied = true;
    
    // Lưu mã đã áp dụng để so sánh khi thay đổi
    $('#coupon-code').data('applied-code', $('#coupon-code').val().trim());

    // Hiển thị thông báo thành công
    $('#coupon-message')
        .removeClass('text-danger text-warning')
        .addClass('text-success')
        // ... rest of function
}
```

#### 1.4 Cập nhật function resetCouponState
- Xóa data attribute khi reset trạng thái

```javascript
function resetCouponState() {
    couponApplied = false;
    
    // Xóa dữ liệu mã đã áp dụng
    $('#coupon-code').removeData('applied-code');
    
    // ... rest of function
}
```

### 2. Thêm field coupon_code vào form edit admin

#### 2.1 Cập nhật view admin edit
**File:** `resources/views/admin/pages/job/edit.blade.php`

- Thêm field coupon_code sau phần bonus
- Hiển thị giá trị hiện tại từ database
- Thêm validation error display
- Thêm helper text

```html
{{-- Mã khuyến mại --}}
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label class="form-label">Mã khuyến mại</label>
            <input type="text" name="coupon_code" 
                class="form-control @if ($errors->has('coupon_code')) is-invalid @endif"
                value="{{ old('coupon_code', $data->coupon_code) }}"
                placeholder="Nhập mã khuyến mại (tùy chọn)">
            @if ($errors->has('coupon_code'))
            <span class="text-danger">{{ $errors->first('coupon_code') }}</span>
            @endif
            <small class="form-text text-muted">
                Mã khuyến mại được áp dụng cho tin tuyển dụng này
            </small>
        </div>
    </div>
</div>
```

#### 2.2 Cập nhật Admin JobService
**File:** `app/Services/Admin/JobService.php`

- Thêm field `coupon_code` vào data array trong method `updateService`

```php
'coupon_code' => $params['coupon_code'] ?? null,
```

#### 2.3 Cập nhật validation rules
**File:** `app/Http/Requests/Admin/JobRequest.php`

- Thêm validation rule cho coupon_code trong cả POST và PUT methods

```php
'coupon_code' => 'nullable|string|max:255',
```

## Kết quả đạt được

1. **Frontend validation cải thiện:**
   - Ngăn chặn submit form với mã khuyến mại không hợp lệ
   - Theo dõi thay đổi input và reset trạng thái khi cần
   - Cải thiện UX với thông báo rõ ràng

2. **Admin có thể quản lý mã khuyến mại:**
   - Xem mã khuyến mại hiện tại của tin tuyển dụng
   - Chỉnh sửa hoặc xóa mã khuyến mại
   - Validation đảm bảo dữ liệu hợp lệ

## Lưu ý kỹ thuật

- Sử dụng `data('applied-code')` để lưu trạng thái mã đã áp dụng
- Validation ở cả frontend (UX) và backend (security)
- Tương thích với config `enable_coupon_code` để bật/tắt tính năng
- Không ảnh hưởng đến logic xử lý mã khuyến mại hiện có

## Files đã thay đổi

1. `resources/views/frontend/pages/employer/create.blade.php` - Cải thiện validation frontend
2. `resources/views/admin/pages/job/edit.blade.php` - Thêm field coupon_code
3. `app/Services/Admin/JobService.php` - Xử lý update coupon_code
4. `app/Http/Requests/Admin/JobRequest.php` - Thêm validation rules
