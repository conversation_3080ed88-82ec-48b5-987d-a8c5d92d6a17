{"__meta": {"id": "01K2E0N8W283SSY951HT6ZRVY7", "datetime": "2025-08-12 09:09:59", "utime": **********.683456, "method": "GET", "uri": "/ajax/get-skill-main-it?career=1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[09:09:59] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.60346, "xdebug_link": null, "collector": "log"}, {"message": "[09:09:59] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.603798, "xdebug_link": null, "collector": "log"}, {"message": "[09:09:59] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\\\"career\\\":\\\"1\\\"}', 'http:\\/\\/recland.local\\/ajax\\/get-skill-main-it?career=1', 'http:\\/\\/recland.local\\/employer\\/job\\/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"x-csrf-token\\\":[\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"application\\\\\\/json, text\\\\\\/javascript, *\\\\\\/*; q=0.01\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/job\\\\\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IkwrUkZaeHhPRjZ3SVJJYmowdW1NVFE9PSIsInZhbHVlIjoiRCtUZHRpdXVvRXZlNWRLbW1md3BzenlETjNSWlorNWtHWHNGeldubURFS0JDVGRwcjhxS0VPMGhZYjhnTHQ4YVNoQWRNZlovK25ZYWI5ZDV4ZDVuR013QUFYVkxxLytsZ0dBMmNqYUJVQUE0a01GcGhXVWxwSzg2akVEamVodXgiLCJtYWMiOiJhZWVmM2Y3MTBkMDgwYTUwODcwMGNlYmFjZjM4OGI0NTJkODQ3M2UxMWYxYjg1OThlMDUwN2MxYTMxMTFkYmM3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Imt2TXQyNktYTnR1d1VSZTFCQW1HZnc9PSIsInZhbHVlIjoibDlJN2dyY1BFa3RZbXpnM25ELzk1YUxLdmNCUEsvbm9iR0d3c3J0a3NsYjBVTVl3TlNsN29zUlNxSHpuL0VqQUZFeU9RNU4rUlg0aHZuNk5hSHJMVGJuQlZGYXdhcG9VRmk0d0VQbU1SV2R4YnNqRE9pTmRZNm1hRFkrbE5aTkEiLCJtYWMiOiJiYjZlZWVmOGM1NWU2MmYwYTAwOTk2OWNjMWUzZmUyMjZhNDc4NTU5MDUyNzE1N2VjYTg1YmQ1NDc3MzhhN2FlIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:09:59', '2025-08-12 09:09:59')\\n-- \",\n    \"Time:\": 20.6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.670883, "xdebug_link": null, "collector": "log"}, {"message": "[09:09:59] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops`\\n-- \",\n    \"Time:\": 0.77\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.673901, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.186648, "end": **********.683481, "duration": 0.49683308601379395, "duration_str": "497ms", "measures": [{"label": "Booting", "start": **********.186648, "relative_start": 0, "end": **********.578044, "relative_end": **********.578044, "duration": 0.*****************, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.578057, "relative_start": 0.*****************, "end": **********.683483, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.591055, "relative_start": 0.****************, "end": **********.597117, "relative_end": **********.597117, "duration": 0.006062030792236328, "duration_str": "6.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET ajax/get-skill-main-it", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Frontend\\AjaxController@getSkillMainIT<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=432\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/ajax", "as": "ajax-get-skill-main-it", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=432\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/AjaxController.php:432-437</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02137, "accumulated_duration_str": "21.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\\\"career\\\":\\\"1\\\"}', 'http://recland.local/ajax/get-skill-main-it?career=1', 'http://recland.local/employer/job/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"x-csrf-token\\\":[\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"application\\/json, text\\/javascript, *\\/*; q=0.01\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/job\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IkwrUkZaeHhPRjZ3SVJJYmowdW1NVFE9PSIsInZhbHVlIjoiRCtUZHRpdXVvRXZlNWRLbW1md3BzenlETjNSWlorNWtHWHNGeldubURFS0JDVGRwcjhxS0VPMGhZYjhnTHQ4YVNoQWRNZlovK25ZYWI5ZDV4ZDVuR013QUFYVkxxLytsZ0dBMmNqYUJVQUE0a01GcGhXVWxwSzg2akVEamVodXgiLCJtYWMiOiJhZWVmM2Y3MTBkMDgwYTUwODcwMGNlYmFjZjM4OGI0NTJkODQ3M2UxMWYxYjg1OThlMDUwN2MxYTMxMTFkYmM3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Imt2TXQyNktYTnR1d1VSZTFCQW1HZnc9PSIsInZhbHVlIjoibDlJN2dyY1BFa3RZbXpnM25ELzk1YUxLdmNCUEsvbm9iR0d3c3J0a3NsYjBVTVl3TlNsN29zUlNxSHpuL0VqQUZFeU9RNU4rUlg0aHZuNk5hSHJMVGJuQlZGYXdhcG9VRmk0d0VQbU1SV2R4YnNqRE9pTmRZNm1hRFkrbE5aTkEiLCJtYWMiOiJiYjZlZWVmOGM1NWU2MmYwYTAwOTk2OWNjMWUzZmUyMjZhNDc4NTU5MDUyNzE1N2VjYTg1YmQ1NDc3MzhhN2FlIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:09:59', '2025-08-12 09:09:59')", "type": "query", "params": [], "bindings": ["GET", "{\"career\":\"1\"}", "http://recland.local/ajax/get-skill-main-it?career=1", "http://recland.local/employer/job/create", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IkwrUkZaeHhPRjZ3SVJJYmowdW1NVFE9PSIsInZhbHVlIjoiRCtUZHRpdXVvRXZlNWRLbW1md3BzenlETjNSWlorNWtHWHNGeldubURFS0JDVGRwcjhxS0VPMGhZYjhnTHQ4YVNoQWRNZlovK25ZYWI5ZDV4ZDVuR013QUFYVkxxLytsZ0dBMmNqYUJVQUE0a01GcGhXVWxwSzg2akVEamVodXgiLCJtYWMiOiJhZWVmM2Y3MTBkMDgwYTUwODcwMGNlYmFjZjM4OGI0NTJkODQ3M2UxMWYxYjg1OThlMDUwN2MxYTMxMTFkYmM3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Imt2TXQyNktYTnR1d1VSZTFCQW1HZnc9PSIsInZhbHVlIjoibDlJN2dyY1BFa3RZbXpnM25ELzk1YUxLdmNCUEsvbm9iR0d3c3J0a3NsYjBVTVl3TlNsN29zUlNxSHpuL0VqQUZFeU9RNU4rUlg0aHZuNk5hSHJMVGJuQlZGYXdhcG9VRmk0d0VQbU1SV2R4YnNqRE9pTmRZNm1hRFkrbE5aTkEiLCJtYWMiOiJiYjZlZWVmOGM1NWU2MmYwYTAwOTk2OWNjMWUzZmUyMjZhNDc4NTU5MDUyNzE1N2VjYTg1YmQ1NDc3MzhhN2FlIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 09:09:59", "2025-08-12 09:09:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.6505458, "duration": 0.0206, "duration_str": "20.6ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 96.397}, {"sql": "select * from `job_tops`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/JobTopRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobTopRepository.php", "line": 22}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 144}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/AjaxController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\AjaxController.php", "line": 435}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.673199, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "JobTopRepository.php:22", "source": {"index": 14, "namespace": null, "name": "app/Repositories/JobTopRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobTopRepository.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobTopRepository.php&line=22", "ajax": false, "filename": "JobTopRepository.php", "line": "22"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops`", "hash": "e483b570db4a4d95eef34975f1de4502d417ef682ca65f1939c31565ac3f693d"}, "start_percent": 96.397, "width_percent": 3.603}]}, "models": {"data": {"App\\Models\\JobTop": {"retrieved": 57, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobTop.php&line=1", "ajax": false, "filename": "JobTop.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}}, "count": 58, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 57}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/job/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/ajax/get-skill-main-it?career=1", "action_name": "ajax-get-skill-main-it", "controller_action": "App\\Http\\Controllers\\Frontend\\AjaxController@getSkillMainIT", "uri": "GET ajax/get-skill-main-it", "controller": "App\\Http\\Controllers\\Frontend\\AjaxController@getSkillMainIT<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=432\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/ajax", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=432\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/AjaxController.php:432-437</a>", "middleware": "web, localization, visit-website", "duration": "497ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1249896481 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>career</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249896481\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-300580093 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-300580093\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-378654779 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6IkwrUkZaeHhPRjZ3SVJJYmowdW1NVFE9PSIsInZhbHVlIjoiRCtUZHRpdXVvRXZlNWRLbW1md3BzenlETjNSWlorNWtHWHNGeldubURFS0JDVGRwcjhxS0VPMGhZYjhnTHQ4YVNoQWRNZlovK25ZYWI5ZDV4ZDVuR013QUFYVkxxLytsZ0dBMmNqYUJVQUE0a01GcGhXVWxwSzg2akVEamVodXgiLCJtYWMiOiJhZWVmM2Y3MTBkMDgwYTUwODcwMGNlYmFjZjM4OGI0NTJkODQ3M2UxMWYxYjg1OThlMDUwN2MxYTMxMTFkYmM3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Imt2TXQyNktYTnR1d1VSZTFCQW1HZnc9PSIsInZhbHVlIjoibDlJN2dyY1BFa3RZbXpnM25ELzk1YUxLdmNCUEsvbm9iR0d3c3J0a3NsYjBVTVl3TlNsN29zUlNxSHpuL0VqQUZFeU9RNU4rUlg0aHZuNk5hSHJMVGJuQlZGYXdhcG9VRmk0d0VQbU1SV2R4YnNqRE9pTmRZNm1hRFkrbE5aTkEiLCJtYWMiOiJiYjZlZWVmOGM1NWU2MmYwYTAwOTk2OWNjMWUzZmUyMjZhNDc4NTU5MDUyNzE1N2VjYTg1YmQ1NDc3MzhhN2FlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378654779\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1787989900 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787989900\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1076171435 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 02:09:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076171435\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-612302859 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612302859\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/ajax/get-skill-main-it?career=1", "action_name": "ajax-get-skill-main-it", "controller_action": "App\\Http\\Controllers\\Frontend\\AjaxController@getSkillMainIT"}, "badge": null}}