{"__meta": {"id": "01K2DY852MNGYTA10FZY32A6KA", "datetime": "2025-08-12 08:27:52", "utime": **********.661111, "method": "GET", "uri": "/employer", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 9, "messages": [{"message": "[08:27:52] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.295382, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.295844, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-dashboard' limit 1\\n-- \",\n    \"Time:\": 27.26\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.357059, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/employer', 'http:\\/\\/recland.local\\/login', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/login\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InBJL0s5S1R5UTlQSUw3ZHl0OEJSK3c9PSIsInZhbHVlIjoiempNNG41Zms5ZXpPS3NGNFUwR1JPR0krcUZoOVdiN1NxS0dUdmtZWkpwVjMrT0h2VjhuMURFTDlzTXNsYUw4NmcwUVFaVmswNlRuOVZFS0pSYzBTam9lMWUvRzY1SVh2T3FScEpYdlFVN0RYVzdsUVF5WnY5dnA4c1dwWU5zVjYiLCJtYWMiOiI1YmNlODdhNTRjZmY1Zjc3Y2YxMWNiOGM0N2ZlMWRhZjY5N2NiMjYwM2VjNzEzZmRmYThjMGFlYTQxNzhhZmI0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlJqbXNUQlRTL0hVYjlKWkFPM21scFE9PSIsInZhbHVlIjoiNXFKNnJ2R2J2NWhnTHNMTDBYVGh5aDVmdWd3UjJUN0JxTGVEckpOQllqTW1WNTc2NE5JM2IyWUlRQ0l6dExQZWcyUG5PVXdOeHhvYmljZDlNdWMza2JLQlE5Z3Jub2NuaEdjd296c3Y4VUJEbUQvZE92NlY2WVo4QzE2K1VUc1AiLCJtYWMiOiI3NmE0NjE0Yzk4NzIwMzA3NGIzZDEzMmQyMWE3NjM0NDFkYzNjNzQ5NjU2NWJjNzRjNzA3ZWRlM2JiZGEwNjQxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962070$j24$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:52', '2025-08-12 08:27:52')\\n-- \",\n    \"Time:\": 0.55\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.516976, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-dashboard' limit 1\\n-- \",\n    \"Time:\": 0.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.550504, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `banners` where `is_active` = '1' and `position` = 'home-ntd-banner' and `type` = '1'\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.554514, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `id`, `name`, `slug`, `logo` from `companies` where `home` = '1' and `is_active` = '1'\\n-- \",\n    \"Time:\": 1.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.559187, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `testimonials` where `type` = '1' and `is_active` = '1'\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.56205, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:52] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `categories`\\n-- \",\n    \"Time:\": 0.62\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.647116, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754962071.844824, "end": **********.661144, "duration": 0.8163199424743652, "duration_str": "816ms", "measures": [{"label": "Booting", "start": 1754962071.844824, "relative_start": 0, "end": **********.266331, "relative_end": **********.266331, "duration": 0.****************, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.266343, "relative_start": 0.*****************, "end": **********.661147, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.282036, "relative_start": 0.****************, "end": **********.291174, "relative_end": **********.291174, "duration": 0.009137868881225586, "duration_str": "9.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "60MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "1x frontend.pages.employer.index", "param_count": null, "params": [], "start": **********.573276, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/index.blade.phpfrontend.pages.employer.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.index"}, {"name": "1x frontend.pages.employer.employer_v2", "param_count": null, "params": [], "start": **********.576124, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/employer_v2.blade.phpfrontend.pages.employer.employer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Femployer_v2.blade.php&line=1", "ajax": false, "filename": "employer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.employer_v2"}, {"name": "1x frontend.layouts.v2", "param_count": null, "params": [], "start": **********.642692, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.phpfrontend.layouts.v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fv2.blade.php&line=1", "ajax": false, "filename": "v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.v2"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.64462, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.v2.home_header", "param_count": null, "params": [], "start": **********.650343, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.phpfrontend.inc_layouts.v2.home_header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhome_header.blade.php&line=1", "ajax": false, "filename": "home_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.home_header"}, {"name": "1x frontend.inc_layouts.v2.hh_footer", "param_count": null, "params": [], "start": **********.65344, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/hh_footer.blade.phpfrontend.inc_layouts.v2.hh_footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhh_footer.blade.php&line=1", "ajax": false, "filename": "hh_footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.hh_footer"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.65567, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}, {"name": "1x frontend.inc_layouts.v2.contactus", "param_count": null, "params": [], "start": **********.656276, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/contactus.blade.phpfrontend.inc_layouts.v2.contactus", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fcontactus.blade.php&line=1", "ajax": false, "filename": "contactus.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.contactus"}]}, "route": {"uri": "GET employer", "middleware": "web, localization, visit-website, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "employer-dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:137-160</a>"}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03125000000000001, "accumulated_duration_str": "31.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.330086, "duration": 0.027260000000000003, "duration_str": "27.26ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "66c7743e75d9ecedbdaa32761a6c1bad45af73bcbd94805400ab00c7698da8a4"}, "start_percent": 0, "width_percent": 87.232}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer', 'http://recland.local/login', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/login\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InBJL0s5S1R5UTlQSUw3ZHl0OEJSK3c9PSIsInZhbHVlIjoiempNNG41Zms5ZXpPS3NGNFUwR1JPR0krcUZoOVdiN1NxS0dUdmtZWkpwVjMrT0h2VjhuMURFTDlzTXNsYUw4NmcwUVFaVmswNlRuOVZFS0pSYzBTam9lMWUvRzY1SVh2T3FScEpYdlFVN0RYVzdsUVF5WnY5dnA4c1dwWU5zVjYiLCJtYWMiOiI1YmNlODdhNTRjZmY1Zjc3Y2YxMWNiOGM0N2ZlMWRhZjY5N2NiMjYwM2VjNzEzZmRmYThjMGFlYTQxNzhhZmI0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlJqbXNUQlRTL0hVYjlKWkFPM21scFE9PSIsInZhbHVlIjoiNXFKNnJ2R2J2NWhnTHNMTDBYVGh5aDVmdWd3UjJUN0JxTGVEckpOQllqTW1WNTc2NE5JM2IyWUlRQ0l6dExQZWcyUG5PVXdOeHhvYmljZDlNdWMza2JLQlE5Z3Jub2NuaEdjd296c3Y4VUJEbUQvZE92NlY2WVo4QzE2K1VUc1AiLCJtYWMiOiI3NmE0NjE0Yzk4NzIwMzA3NGIzZDEzMmQyMWE3NjM0NDFkYzNjNzQ5NjU2NWJjNzRjNzA3ZWRlM2JiZGEwNjQxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962070$j24$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:52', '2025-08-12 08:27:52')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/employer", "http://recland.local/login", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/login\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InBJL0s5S1R5UTlQSUw3ZHl0OEJSK3c9PSIsInZhbHVlIjoiempNNG41Zms5ZXpPS3NGNFUwR1JPR0krcUZoOVdiN1NxS0dUdmtZWkpwVjMrT0h2VjhuMURFTDlzTXNsYUw4NmcwUVFaVmswNlRuOVZFS0pSYzBTam9lMWUvRzY1SVh2T3FScEpYdlFVN0RYVzdsUVF5WnY5dnA4c1dwWU5zVjYiLCJtYWMiOiI1YmNlODdhNTRjZmY1Zjc3Y2YxMWNiOGM0N2ZlMWRhZjY5N2NiMjYwM2VjNzEzZmRmYThjMGFlYTQxNzhhZmI0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlJqbXNUQlRTL0hVYjlKWkFPM21scFE9PSIsInZhbHVlIjoiNXFKNnJ2R2J2NWhnTHNMTDBYVGh5aDVmdWd3UjJUN0JxTGVEckpOQllqTW1WNTc2NE5JM2IyWUlRQ0l6dExQZWcyUG5PVXdOeHhvYmljZDlNdWMza2JLQlE5Z3Jub2NuaEdjd296c3Y4VUJEbUQvZE92NlY2WVo4QzE2K1VUc1AiLCJtYWMiOiI3NmE0NjE0Yzk4NzIwMzA3NGIzZDEzMmQyMWE3NjM0NDFkYzNjNzQ5NjU2NWJjNzRjNzA3ZWRlM2JiZGEwNjQxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962070$j24$l0$h0\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:27:52", "2025-08-12 08:27:52"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.5166929, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 87.232, "width_percent": 1.76}, {"sql": "select * from `seos` where `key` = 'employer-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 140}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.55015, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "66c7743e75d9ecedbdaa32761a6c1bad45af73bcbd94805400ab00c7698da8a4"}, "start_percent": 88.992, "width_percent": 1.376}, {"sql": "select * from `banners` where `is_active` = 1 and `position` = 'home-ntd-banner' and `type` = 1", "type": "query", "params": [], "bindings": [1, "home-ntd-banner", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php", "line": 62}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/BannerService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php", "line": 21}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.554174, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BannerRepository.php:62", "source": {"index": 14, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBannerRepository.php&line=62", "ajax": false, "filename": "BannerRepository.php", "line": "62"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `banners` where `is_active` = ? and `position` = ? and `type` = ?", "hash": "3059d80fc8b8a60aefda83a0c5b766c9f56e35c588c658f4b216f214abf33f69"}, "start_percent": 90.368, "width_percent": 1.312}, {"sql": "select `id`, `name`, `slug`, `logo` from `companies` where `home` = 1 and `is_active` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\CompanyRepository.php", "line": 83}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\CompanyService.php", "line": 23}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 153}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.557679, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:83", "source": {"index": 14, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\CompanyRepository.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FCompanyRepository.php&line=83", "ajax": false, "filename": "CompanyRepository.php", "line": "83"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `id`, `name`, `slug`, `logo` from `companies` where `home` = ? and `is_active` = ?", "hash": "492890de082540e1fb8fb61777a7859a19f08d599af94a993907cdeac1ca6bb5"}, "start_percent": 91.68, "width_percent": 5.056}, {"sql": "select * from `testimonials` where `type` = 1 and `is_active` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/TestimonialService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\TestimonialService.php", "line": 20}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 155}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.561719, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "TestimonialRepository.php:54", "source": {"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FTestimonialRepository.php&line=54", "ajax": false, "filename": "TestimonialRepository.php", "line": "54"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `testimonials` where `type` = ? and `is_active` = ?", "hash": "85fe956c1e6a00482970b8c6831cc24306d6a878a633d923d1d249a76caa5018"}, "start_percent": 96.736, "width_percent": 1.28}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}, {"index": 21, "namespace": "view", "name": "frontend.layouts.v2", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.php", "line": 46}], "start": **********.6466491, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:58", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=58", "ajax": false, "filename": "AppServiceProvider.php", "line": "58"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `categories`", "hash": "7e13c5da69976ceecb727777c5a91eea3e71bf2b1b50247c4cb202cd9a066582"}, "start_percent": 98.016, "width_percent": 1.984}]}, "models": {"data": {"App\\Models\\Company": {"retrieved": 86, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Testimonial": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\Banner": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FBanner.php&line=1", "ajax": false, "filename": "Banner.php", "line": "?"}}}, "count": 98, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 97, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer", "action_name": "employer-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@index", "uri": "GET employer", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=137\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:137-160</a>", "middleware": "web, localization, visit-website", "duration": "819ms", "peak_memory": "64MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1536051776 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1536051776\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-993062561 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-993062561\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-127249895 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://recland.local/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InBJL0s5S1R5UTlQSUw3ZHl0OEJSK3c9PSIsInZhbHVlIjoiempNNG41Zms5ZXpPS3NGNFUwR1JPR0krcUZoOVdiN1NxS0dUdmtZWkpwVjMrT0h2VjhuMURFTDlzTXNsYUw4NmcwUVFaVmswNlRuOVZFS0pSYzBTam9lMWUvRzY1SVh2T3FScEpYdlFVN0RYVzdsUVF5WnY5dnA4c1dwWU5zVjYiLCJtYWMiOiI1YmNlODdhNTRjZmY1Zjc3Y2YxMWNiOGM0N2ZlMWRhZjY5N2NiMjYwM2VjNzEzZmRmYThjMGFlYTQxNzhhZmI0IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlJqbXNUQlRTL0hVYjlKWkFPM21scFE9PSIsInZhbHVlIjoiNXFKNnJ2R2J2NWhnTHNMTDBYVGh5aDVmdWd3UjJUN0JxTGVEckpOQllqTW1WNTc2NE5JM2IyWUlRQ0l6dExQZWcyUG5PVXdOeHhvYmljZDlNdWMza2JLQlE5Z3Jub2NuaEdjd296c3Y4VUJEbUQvZE92NlY2WVo4QzE2K1VUc1AiLCJtYWMiOiI3NmE0NjE0Yzk4NzIwMzA3NGIzZDEzMmQyMWE3NjM0NDFkYzNjNzQ5NjU2NWJjNzRjNzA3ZWRlM2JiZGEwNjQxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962070$j24$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127249895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-66590903 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OLDKqoGpbczGmQ3iONEKK3dsTzLGGTm8QWy0Jh3e</span>\"\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66590903\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1277676817 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:27:52 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277676817\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-170012125 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://recland.local/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170012125\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer", "action_name": "employer-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@index"}, "badge": null}}