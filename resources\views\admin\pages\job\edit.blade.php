@php
use App\Services\Admin\PermissionService;
@endphp
@extends('admin.layouts.app')

@section('css_custom')
<link href="{{ asset2('backend/assets/plugins/fileupload/css/fileupload.css') }}" rel="stylesheet" type="text/css" />

<!-- INTERNAL Fancy File Upload css -->
<link href="{{ asset2('backend/assets/plugins/fancyuploder/fancy_fileupload.css') }}" rel="stylesheet" />

<script>
    const URL_SUBMIT_CV = "{{ route('submit-cv.edit', ':id') }}";
</script>
@endsection

@section('content')
<!--Page header-->
<div class="page-header d-xl-flex d-block">
    <div class="page-leftheader">
        <h4 class="page-title">Chỉnh sửa Job</h4>
    </div>
    <div class="page-rightheader ms-auto">
        <a href="{{ route('job.duplicate', ['job' => $data->id]) }}" class="btn btn-warning btn-lg"
            onclick="return confirm('Bạn có chắc chắn muốn nhân bản tin tuyển dụng này?')">
            <i class="las la-copy"></i> Nhân bản
        </a>
    </div>
</div>

<!--End Page header-->
<div class="row">
    <div class="col-xl-12 col-md-12 col-lg-12">
        <div class="card">
            <div class="card-body">
                <div class="panel panel-primary">
                    <div class=" tab-menu-heading p-0 bg-light">
                        <div class="tabs-menu1 ">
                            <!-- Tabs -->
                            <ul class="nav panel-tabs">
                                <li class=""><a href="#info" class="active" data-toggle="tab">Thông tin</a></li>
                                @if (PermissionService::checkPermission('job-seo-update'))
                                <li><a href="#seo" data-toggle="tab">SEO</a></li>
                                @endif
                                @if (PermissionService::checkPermission('job-meta-update'))
                                <li><a href="#meta" data-toggle="tab">META</a></li>
                                @endif
                                @if (\App\Services\Admin\PermissionService::checkPermission('ref-datatable'))
                                <li><a href="#ref" data-toggle="tab">Giới thiệu ứng viên</a></li>
                                @endif
                                <!-- Thêm tab mới -->
                                <li><a href="#qa" data-toggle="tab">Hỏi đáp</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="panel-body tabs-menu-body">
                        <div class="tab-content">
                            <div class="tab-pane active " id="info">
                                <form action="{{ route('job.update', ['job' => $data->id]) }}"
                                    enctype="multipart/form-data" method="post" class="sbm_form_s">
                                    @csrf
                                    {{ method_field('put') }}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">Tên job <span
                                                        class="text-red">*</span></label>
                                                <input class="form-control @if ($errors->has('name')) is-invalid @endif"
                                                    name="name" value="{{ old('name', $data->name) }}"
                                                    autocomplete="off">
                                                @if ($errors->has('name'))
                                                <span class="text-danger"> {{ $errors->first('name') }} </span>
                                                @endif
                                            </div>

                                            <div class="form-group">
                                                <input type="hidden" name="hdd_company_id" id="hdd_company_id"
                                                    value="{{ isset($companies[0]['id']) ? $companies[0]['id'] : '' }}" />
                                                <label class="form-label"> Công ty <span
                                                        class="text-red">*</span></label>
                                                <div
                                                    class="@if ($errors->has('company_id')) is-invalid-select-2 @endif">
                                                    <select id="company_id"
                                                        class="form-control select2-show-search custom-select company-select @if ($errors->has('company_id')) is-invalid @endif"
                                                        name="company_id" data-placeholder="--- Chọn ---">
                                                        <option label="--- Chọn ---"></option>
                                                        @foreach ($companies as $company)
                                                        <option value="{{ $company->id }}">{{ $company->name }}
                                                        </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                @if ($errors->has('company_id'))
                                                <span class="text-danger"> {{ $errors->first('company_id') }}
                                                </span>
                                                @endif
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label">Nhà tuyển dụng <span
                                                        class="text-red">*</span></label>
                                                <div
                                                    class="@if ($errors->has('employer_id')) is-invalid-select-2 @endif">
                                                    <select id="employer-select"
                                                        class="form-control select2-show-search custom-select  @if ($errors->has('employer_id')) is-invalid @endif"
                                                        name="employer_id" data-placeholder="--- Chọn ---">
                                                        <option label="--- Chọn ---"></option>
                                                        @foreach ($employer as $item)
                                                        <option value="{{ $item->id }}" {{ $data->employer_id ==
                                                            $item->id ? 'selected' : '' }}>
                                                            {{ $item->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                @if ($errors->has('employer_id'))
                                                <span class="text-danger"> {{ $errors->first('employer_id') }}
                                                </span>
                                                @endif
                                            </div>

                                            {{--
                                            <?php--}}
                                                {{--                                                        $arrCareer = [];--}}
                                                {{--                                                        if (old('career')) {--}}
                                                {{--                                                            $arrCareer = array_flip(old('career'));--}}
                                                {{--                                                        } else {--}}
                                                {{--                                                            $arrCareer = explode(',', $data->career);--}}
                                                {{--                                                            $arrCareer = array_flip($arrCareer);--}}
                                                {{--                                                        }--}}
                                                {{--                                                    ?> ?> --}}

                                            {{-- <div class="form-group"> --}}
                                                {{-- <label>{{ $arrLang['linhvuc'] }} <span>*</span></label> --}}
                                                {{-- <div class="multiple-select2"> --}}
                                                    {{-- <select id="career-select" style="width:100%;display: none"
                                                        --}} {{-- class="select2-custom select-max-item" name="career">
                                                        --}}
                                                        {{-- @foreach ($career as $k => $item) --}}
                                                        {{-- <option value="{{ $k }}" {{ $k==$job->career ? 'selected' :
                                                            '' }}> --}}
                                                            {{-- {{ $item }}</option> --}}
                                                        {{-- @endforeach --}}
                                                        {{-- </select> --}}
                                                    {{-- </div> --}}
                                                {{-- @if ($errors->has('career')) --}}
                                                {{-- <span class="text-danger"> {{$errors->first('career')}} </span>
                                                --}}
                                                {{-- @endif --}}
                                                {{-- </div> --}}

                                            <div class="form-group">
                                                <label>{{ $arrLang['soluongtuyen'] }} <span>*</span></label>
                                                <input type="text" class="form-control"
                                                    placeholder="{{ $arrLang['input'] }} {{ lcfirst($arrLang['soluongtuyen']) }}"
                                                    value="{{ $job->vacancies }}" name="vacancies">
                                                @if ($errors->has('vacancies'))
                                                <span class="text-danger"> {{ $errors->first('vacancies') }}
                                                </span>
                                                @endif
                                            </div>

                                            <div class="area-address"
                                                style="{{ $data->remote == 1 ? 'display:none' : '' }}">
                                                @for ($i = 0; $i < 3; $i++) <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            @if ($i == 0)
                                                            <label class="form-label"> Địa điểm <span
                                                                    class="text-red">*</span></label>
                                                            @endif
                                                            <div
                                                                class="@if ($errors->has('address.' . $i . '.area')) is-invalid-select-2 @endif">
                                                                <select name="address[{{ $i }}][area]"
                                                                    class="form-control select2-show-search custom-select is-invalid area{{ $i }}"
                                                                    data-placeholder="Khu vực">
                                                                    <option value="">Khu vực</option>
                                                                    @foreach ($cities as $key => $city)
                                                                    <option @if (old('address.' . $i . '.area' ,
                                                                        @$data->address_value[$i]->area) == $key)
                                                                        selected @endif
                                                                        value="{{ $key }}">
                                                                        {{ $city }}</option>
                                                                    @endforeach
                                                                </select>
                                                            </div>
                                                            @if ($errors->has('address.' . $i . '.area'))
                                                            <div class="text-danger">
                                                                {{ $errors->first('address.' . $i . '.area') }}
                                                            </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="col-md-9">
                                                        @if ($i == 0)
                                                        <label class="form-label"> Địa chỉ cụ thể <span
                                                                class="text-red">*</span></label>
                                                        @endif
                                                        <input name="address[{{ $i }}][address]"
                                                            class="form-control address{{ $i }} @if ($errors->has('address.' . $i . '.address')) is-invalid @endif"
                                                            value="{{ old('address.' . $i . '.address', @$data->address_value[$i]->address) }}">
                                                        @if ($errors->has('address.' . $i . '.address'))
                                                        <div class="text-danger">
                                                            {{ $errors->first('address.' . $i . '.address') }}
                                                        </div>
                                                        @endif
                                                    </div>
                                            </div>
                                            @endfor
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">JD <span class="text-red">
                                                    @if (is_null($data->file_jd))
                                                    *
                                                    @endif
                                                </span></label>
                                            <input class="" type="file" name="file_jd" autocomplete="off"
                                                @if(is_null($data->file_jd)) 'required' @endif>
                                            @if (!is_null($data->file_jd))
                                            <a target="_blank" class="btn btn-link"
                                                href="{{ gen_url_file_s3($data->file_jd) }}">Show</a>
                                            @endif
                                            @if ($errors->has('file_jd'))
                                            <span class="text-danger"> {{ $errors->first('file_jd') }} </span>
                                            @endif
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Trạng thái công việc <span
                                                    class="text-red">*</span></label>
                                            <select class="form-control @if ($errors->has('status')) is-invalid @endif"
                                                name="status">
                                                <option value="">--- Chọn ---</option>
                                                @foreach (config('constant.status_job') as $k => $v)
                                                <option value="{{ $k }}" @if (old('status', $data->status) == $k)
                                                    selected @endif>
                                                    {{ $v }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Hạn nộp <span class="text-red">*</span></label>
                                            <input
                                                class="form-control fc-datepicker @if ($errors->has('expire_at')) is-invalid @endif"
                                                name="expire_at" placeholder="DD/MM/YYYY"
                                                value="{{ old('expire_at', $data->expire_at_value) }}" type="text"
                                                autocomplete="off">
                                            @if ($errors->has('expire_at'))
                                            <span class="text-danger"> {{ $errors->first('expire_at') }}
                                            </span>
                                            @endif
                                        </div>



                                        {{--
                                        <?php--}}
                                                {{--                                                        $arrRank = [];--}}
                                                {{--                                                        if (old('rank')) {--}}
                                                {{--                                                            $arrRank = array_flip(old('rank'));--}}
                                                {{--                                                        } else {--}}
                                                {{--                                                            $arrRank = explode(',', $data->rank);--}}
                                                {{--                                                            $arrRank = array_flip($arrRank);--}}
                                                {{--                                                        }--}}
                                                {{--                                                    ?> ?> --}}

                                        <div class="form-group">
                                            <label>{{ $arrLang['linhvuc'] }} <span>*</span></label>
                                            <div class="multiple-select2">
                                                <select id="career-select" style="width:100%;display: none"
                                                    class="select2-custom select-max-item" name="career">
                                                    @foreach ($career as $k => $item)
                                                    <option value="{{ $k }}" {{ $k==$job->career ? 'selected' : '' }}>
                                                        {{ $item }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            @if ($errors->has('career'))
                                            <span class="text-danger"> {{ $errors->first('career') }} </span>
                                            @endif
                                        </div>




                                        {{--
                                        <?php--}}
                                                {{--                                                        $arrSkill = [];--}}
                                                {{--                                                        if (old('skill')) {--}}
                                                {{--                                                            $arrSkill = old('skill');--}}
                                                {{--                                                        } else {--}}
                                                {{--                                                            $arrSkill = explode(',', $data->skills);--}}
                                                {{--//                                                            $arrSkill = array_flip($arrSkill);--}}
                                                {{--                                                        }--}}
                                                {{--                                                    ?> ?> --}}

                                        <div class="form-group">
                                            <label>{{ $arrLang['kinang'] }} <span>*</span></label>
                                            <div class="multiple-select2">
                                                <select id="skill_id" style="width:100%;display: none"
                                                    class="select2-custom-tags select-max-item" name="skill">
                                                    <option label="--- Chọn ---"></option>
                                                    @foreach ($skills as $k => $item)
                                                    <option value="{{ $k }}" {{ $k==$job->skill_id ? 'selected' : '' }}>
                                                        {{ $item }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            @if ($errors->has('skill'))
                                            <span class="text-danger"> {{ $errors->first('skill') }} </span>
                                            @endif
                                        </div>

                                        <div class="form-group">
                                            {{-- @dd($levels) --}}
                                            <label>{{ $arrLang['capbac'] }} <span>*</span></label>
                                            <div class="multiple-select2">
                                                <select id="rank-select" style="width:100%;display: none"
                                                    class="select2-custom select-max-item" name="rank">
                                                    {{-- @dd($levels); --}}
                                                    @foreach ($levels as $k => $item)
                                                    <option value="{{ $item->id }}" {{ $item->id == $job->rank ?
                                                        'selected' : '' }}>
                                                        {{ $item->name_vi }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            @if ($errors->has('rank'))
                                            <span class="text-danger"> {{ $errors->first('rank') }} </span>
                                            @endif
                                        </div>

                                        <div class="row">
                                            <div class="col-md-9">
                                                <div class="form-group">
                                                    <label class="form-label">Hình thức <span
                                                            class="text-red">*</span></label>
                                                    <div class="@if ($errors->has('type')) is-invalid-select-2 @endif">
                                                        <select
                                                            class="form-control select2-show-search custom-select  @if ($errors->has('type')) is-invalid @endif"
                                                            name="type" data-placeholder="--- Chọn ---">
                                                            <option label="--- Chọn ---"></option>
                                                            @foreach ($type as $k => $item)
                                                            <option value="{{ $k }}" {{ old('type', $data->type) == $k ?
                                                                'selected' : '' }}>
                                                                {{ $item }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    @if ($errors->has('type'))
                                                    <span class="text-danger"> {{ $errors->first('type') }}
                                                    </span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Remote <span
                                                            class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        <input type="checkbox" @if (old('remote', $data->remote) == 1)
                                                        checked @endif
                                                        value="1" name="remote"
                                                        class="custom-switch-input remote">
                                                        <span
                                                            class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="col-md-9">
                                                <div class="form-group">
                                                    <label class="form-label">Loại Job <span
                                                            class="text-red">*</span></label>
                                                    <div
                                                        class="@if ($errors->has('job_type')) is-invalid-select-2 @endif">
                                                        <select
                                                            class="form-control select2-show-search custom-select  @if ($errors->has('job_type')) is-invalid @endif"
                                                            name="job_type" data-placeholder="--- Chọn ---">
                                                            <option label="--- Chọn ---"></option>
                                                            @foreach ($jobType as $k => $item)
                                                            <option value="{{ $k }}" {{ old('job_type', $data->job_type)
                                                                == $k ? 'selected' : '' }}>
                                                                {{ $item }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    @if ($errors->has('job_type'))
                                                    <span class="text-danger">
                                                        {{ $errors->first('job_type') }} </span>
                                                    @endif
                                                </div>
                                            </div>

                                        </div>

                                        <div class="row">
                                            @if (PermissionService::checkPermission('job.active-change-status'))
                                            <div class="col-md-9">
                                                <div class="form-group">
                                                    <label class="form-label">Trạng thái hoạt động <span
                                                            class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        @if (old('flg_status') == 1)
                                                        <input type="checkbox" @if (old('is_active')==1) checked @endif
                                                            value="1" name="is_active" class="custom-switch-input">
                                                        @else
                                                        <input type="checkbox" @if ($data->is_active == 1) checked
                                                        @endif
                                                        value="1" name="is_active"
                                                        class="custom-switch-input">
                                                        @endif
                                                        <span
                                                            class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                        <span class="custom-switch-description mr-2"
                                                            id="status_active">Active</span>
                                                    </label>
                                                </div>
                                            </div>
                                            @endif
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Urgent <span
                                                            class="text-red">*</span></label>
                                                    <label class="custom-switch">
                                                        <input type="checkbox" @if (old('urgent', $data->urgent) == 1)
                                                        checked @endif
                                                        value="1" name="urgent"
                                                        class="custom-switch-input">
                                                        <span
                                                            class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            </div>

                            <div class="row">
                                <div class="col-md-9">
                                    <div class="form-group">
                                        <label class="form-label">JD_Mô tả công việc <span
                                                class="text-red">*</span></label>
                                        <textarea name="jd_description" id="jd_description" rows="10"
                                            cols="80">{{ old('jd_description', $data->jd_description) }}</textarea>
                                        @if ($errors->has('jd_description'))
                                        <span class="text-danger"> {{ $errors->first('jd_description') }}
                                        </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-3"></div>
                            </div>

                            <div class="row">
                                <div class="col-md-9">
                                    <div class="form-group">
                                        <label class="form-label">JD_Yêu cầu công việc <span
                                                class="text-red">*</span></label>
                                        <textarea name="jd_request" id="jd_request" rows="10"
                                            cols="80">{{ old('jd_request', $data->jd_request) }}</textarea>
                                        @if ($errors->has('jd_request'))
                                        <span class="text-danger"> {{ $errors->first('jd_request') }}
                                        </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-3"></div>
                            </div>

                            <div class="row">
                                <div class="col-md-9">
                                    <div class="form-group">
                                        <label class="form-label">JD_Phúc lợi <span class="text-red">*</span></label>
                                        <textarea name="jd_welfare" id="jd_welfare" rows="10"
                                            cols="80">{{ old('jd_welfare', $data->jd_welfare) }}</textarea>
                                        @if ($errors->has('jd_welfare'))
                                        <span class="text-danger"> {{ $errors->first('jd_welfare') }}
                                        </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-3"></div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ $arrLang['salarymin'] }} <span>*</span> </label>
                                        <div class="input-unit">
                                            <input data-toggle="current_mask" data-target="#field_salary_min"
                                                type="text" class="field-input-unit form-control"
                                                value="{{ $job->salary_min }}"
                                                placeholder="{{ $arrLang['input'] }} {{ lcfirst($arrLang['salarymin']) }}">
                                            <input id="field_salary_min" class="field-input-unit" type="hidden" value=""
                                                name="salary_min">
                                        </div>
                                        @if ($errors->has('salary_min'))
                                        <span class="text-danger"> {{ $errors->first('salary_min') }}
                                        </span>
                                        @endif

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ $arrLang['salarymax'] }} <span>*</span> </label>
                                        <div class="input-unit">
                                            <input data-toggle="current_mask" data-target="#field_salary_max"
                                                type="text" class="field-input-unit form-control"
                                                value="{{ $job->salary_max }}"
                                                placeholder="{{ $arrLang['input'] }} {{ lcfirst($arrLang['salarymax']) }}">
                                            <input id="field_salary_max" class="field-input-unit" type="hidden" value=""
                                                name="salary_max">
                                        </div>
                                        @if ($errors->has('salary_max'))
                                        <span class="text-danger"> {{ $errors->first('salary_max') }}
                                        </span>
                                        @endif

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Salary Currency <span class="text-red">*</span></label>
                                    <div class="@if ($errors->has('salary_currency')) is-invalid-select-2 @endif">
                                        <select
                                            class="form-control select2-show-search custom-select  @if ($errors->has('salary_currency')) is-invalid @endif"
                                            name="salary_currency" data-placeholder="--- Chọn ---">
                                            <option label="--- Chọn ---"></option>
                                            @foreach ($currency as $item)
                                            <option value="{{ $item }}" {{ old('salary_currency', $data->
                                                salary_currency) == $item ? 'selected' : '' }}>
                                                {{ $item }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @if ($errors->has('salary_currency'))
                                    <span class="text-danger"> {{ $errors->first('salary_currency') }}
                                    </span>
                                    @endif
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ $arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng' }}
                                            <span>*</span></label>
                                        <select id="recruitment-type" style="width:100%;display: none" class=""
                                            name="bonus_type">
                                            {{-- <option value="onboard">Onboard</option>
                                            <option value="interview">Interview</option>
                                            <option value="cv">CV</option> --}}
                                            <option value="">
                                                {{ config('settings.' . app()->getLocale() .
                                                '.rec_cv_selling.chonhinhthuc') }}
                                            </option>
                                            @foreach ($bonusType as $item)
                                            <option value="{{ $item }}" {{ $item==$job->bonus_type ? 'selected' : '' }}>
                                                {{ $item }}</option>
                                            @endforeach
                                        </select>
                                        @if ($errors->has('bonus_type'))
                                        <span class="text-danger"> {{ $errors->first('bonus_type') }}
                                        </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>{{ $arrLang['chiphituyendung'] ?? 'Chi phí cho 1 lần tuyển dụng' }}
                                            <span>*</span></label>
                                        <input type="number" id="bonus" name="bonus" class="form-control"
                                            value="{{ $job->bonus }}">
                                        <label style="font-weight: normal; font-size: 14px;" id="bonus_msg"
                                            class="form-text text-muted mt-2 d-block">Giá tối thiểu là <span
                                                id="min_price"></span> vnđ</label>
                                        @if ($errors->has('bonus'))
                                        <span class="text-danger"> {{ $errors->first('bonus') }} </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-item">
                                        <label class="form-label">{{ $arrLang['tongtien'] ?? 'Tổng chi phí' }}</label>
                                        <input type="text" id="total-cost" readonly class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-item">
                                        <label class="form-label">Phí trả cho CTV</label>
                                        <div class="input-group">
                                            <input type="text" id="bonus_for_ctv" readonly class="form-control"
                                                value="{{ $job->bonus_for_ctv }}">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary calculate-bonus" type="button">
                                                    <i class="fas fa-calculator"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-item">
                                        <label class="form-label">Phí trả cho CTV (Tự nhập)</label>
                                        <input type="text" id="manual_bonus_for_ctv" name="manual_bonus_for_ctv"
                                            class="form-control" value="{{ $job->manual_bonus_for_ctv }}" {{
                                            PermissionService::checkPermission('job.custom-price') ? '' : 'disabled' }}>
                                    </div>
                                </div>
                            </div>

                            {{-- Mã khuyến mại --}}
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Mã khuyến mại</label>
                                        <input type="text" name="coupon_code"
                                            class="form-control @if ($errors->has('coupon_code')) is-invalid @endif"
                                            value="{{ old('coupon_code', $data->coupon_code) }}"
                                            placeholder="Nhập mã khuyến mại (tùy chọn)">
                                        @if ($errors->has('coupon_code'))
                                        <span class="text-danger">{{ $errors->first('coupon_code') }}</span>
                                        @endif
                                        <small class="form-text text-muted">
                                            Mã khuyến mại được áp dụng cho tin tuyển dụng này
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                {{-- <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Phí thanh toán <span class="text-red">*</span></label>
                                        <input data-toggle="current_mask" data-target="#payment_fee_value"
                                            class="form-control @if ($errors->has('payment_fee')) is-invalid @endif"
                                            value="{{old('payment_fee', $data->payment_fee)}}" autocomplete="off">
                                        <input id="payment_fee_value" type="hidden"
                                            value="{{old('payment_fee', $data->payment_fee)}}" name="payment_fee">
                                        @if ($errors->has('payment_fee'))
                                        <span class="text-danger"> {{$errors->first('payment_fee')}} </span>
                                        @endif
                                    </div>
                                </div> --}}
                                {{-- <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label">Incentive <span class="text-red">*</span></label>
                                        <input data-toggle="current_mask" data-target="#incentive_value"
                                            class="form-control @if ($errors->has('incentive')) is-invalid @endif"
                                            name="incentive" value="{{old('incentive', $data->incentive)}}"
                                            autocomplete="off">
                                        <input id="incentive_value" type="hidden"
                                            value="{{old('incentive', $data->incentive)}}" name="incentive">
                                        @if ($errors->has('incentive'))
                                        <span class="text-danger"> {{$errors->first('incentive')}} </span>
                                        @endif

                                    </div>
                                </div> --}}
                                {{-- <div class="col-md-3">
                                    <label class="form-label">Bonus Currency <span class="text-red">*</span></label>
                                    <div class="@if ($errors->has('bonus_currency')) is-invalid-select-2 @endif">
                                        <select
                                            class="form-control select2-show-search custom-select  @if ($errors->has('bonus_currency')) is-invalid @endif"
                                            name="bonus_currency" data-placeholder="--- Chọn ---">
                                            <option label="--- Chọn ---"></option>
                                            @foreach ($currency as $item)
                                            <option value="{{$item}}" {{(old('bonus_currency', $data->bonus_currency))
                                                == $item ? 'selected' : ''}}>{{$item}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @if ($errors->has('bonus_currency'))
                                    <span class="text-danger"> {{$errors->first('bonus_currency')}} </span>
                                    @endif
                                </div> --}}
                                {{-- </div> --}}

                            {{-- <div class="row"> --}}
                                {{-- <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Bonus self apply <span
                                                class="text-red">*</span></label>
                                        <input data-toggle="current_mask" data-target="#bonus_self_apply_value"
                                            class="form-control @if ($errors->has('bonus_self_apply')) is-invalid @endif"
                                            value="{{old('bonus_self_apply', $data->bonus_self_apply)}}"
                                            autocomplete="off">
                                        <input id="bonus_self_apply_value" type="hidden"
                                            value="{{old('bonus_self_apply', $data->bonus_self_apply)}}"
                                            name="bonus_self_apply">
                                        @if ($errors->has('bonus_self_apply'))
                                        <span class="text-danger"> {{$errors->first('bonus_self_apply')}} </span>
                                        @endif
                                    </div>
                                </div> --}}
                                {{-- <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label">Incentive self apply <span
                                                class="text-red">*</span></label>
                                        <input data-toggle="current_mask"
                                            data-target="#bonus_self_apply_incentive_value"
                                            class="form-control @if ($errors->has('bonus_self_apply_incentive')) is-invalid @endif"
                                            value="{{ old('bonus_self_apply_incentive', $data->bonus_self_apply_incentive) }}"
                                            autocomplete="off">
                                        <input id="bonus_self_apply_incentive_value" type="hidden"
                                            value="{{ old('bonus_self_apply_incentive', $data->bonus_self_apply_incentive) }}"
                                            name="bonus_self_apply_incentive">
                                        @if ($errors->has('bonus_self_apply_incentive'))
                                        <span class="text-danger">
                                            {{ $errors->first('bonus_self_apply_incentive') }} </span>
                                        @endif

                                    </div>
                                </div> --}}
                                {{-- <div class="col-md-3">
                                    <label class="form-label">Bonus Currency self apply <span
                                            class="text-red">*</span></label>
                                    <div
                                        class="@if ($errors->has('bonus_self_apply_currency')) is-invalid-select-2 @endif">
                                        <select
                                            class="form-control select2-show-search custom-select  @if ($errors->has('bonus_self_apply_currency')) is-invalid @endif"
                                            name="bonus_self_apply_currency" data-placeholder="--- Chọn ---">
                                            <option label="--- Chọn ---"></option>
                                            @foreach ($currency as $item)
                                            <option value="{{ $item }}" {{ old('bonus_self_apply_currency', $data->
                                                bonus_self_apply_currency) == $item ? 'selected' : '' }}>
                                                {{ $item }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @if ($errors->has('bonus_self_apply_currency'))
                                    <span class="text-danger">
                                        {{ $errors->first('bonus_self_apply_currency') }} </span>
                                    @endif
                                </div> --}}
                            </div>

                            <div class="form-group">
                                <label class="form-label">Level Job <span class="text-red">*</span></label>
                                <div class="@if($errors->has('level')) is-invalid-select-2 @endif">
                                    <select
                                        class="form-control select2-show-search custom-select @if($errors->has('level')) is-invalid @endif"
                                        name="level" data-placeholder="--- Chọn ---">
                                        <option label="--- Chọn ---"></option>
                                        @foreach($level as $k => $item)
                                        <option value="{{$k}}" {{old('level', $data->level ?? 0) == $k ? 'selected' :
                                            ''}}>{{$item}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                @if($errors->has('level'))
                                <span class="text-danger"> {{$errors->first('level')}} </span>
                                @endif
                            </div>

                            <div class="form-group">
                                <label class="form-label">Note</label>
                                <textarea rows="5" cols="70" name="note">{{ $data->note }}</textarea>
                                @if ($errors->has('note'))
                                <span class="text-danger"> {{ $errors->first('note') }} </span>
                                @endif
                            </div>

                            <!-- Hướng dẫn Level Job -->
                            <div class="alert alert-info">
                                <h5><i class="las la-info-circle"></i> Hướng dẫn Level Job</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Level 0 - Thường:</strong>
                                        <ul class="mb-0">
                                            <li>Job tuyển dụng thông thường</li>
                                            <li>Dành cho tất cả CTV</li>
                                            <li>Không yêu cầu đặc biệt</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Level 1 - Cao cấp:</strong>
                                        <ul class="mb-0">
                                            <li>Job tuyển dụng cao cấp</li>
                                            <li>Chỉ dành cho CTV có level = 1</li>
                                            <li>Có yêu cầu số lượng submit CV cao</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer text-right">
                                <input type="hidden" class="flg_status" name="flg_status"
                                    value="{{ old('flg_status') }}" />
                                <a href="{{ backpack_url('jobs-management') }}" class="btn btn-danger btn-lg">Close</a>
                                <button class="btn btn-success btn-lg sbm_form" type="button">Submit</button>
                            </div>


                            </form>
                        </div>
                        @if (\App\Services\Admin\PermissionService::checkPermission('job-seo-update'))
                        <div class="tab-pane " id="seo">
                            <form action="{{ route('job-seo-update', ['job' => $data->id]) }}"
                                enctype="multipart/form-data" method="post">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Title VN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control @if ($errors->has('seo_title_vi')) is-invalid @endif"
                                                name="seo_title_vi"
                                                value="{{ old('seo_title_vi', $dataSeo ? $dataSeo->title_vi : '') }}"
                                                autocomplete="off">
                                            @if ($errors->has('seo_title_vi'))
                                            <span class="text-danger">
                                                {{ $errors->first('seo_title_vi') }} </span>
                                            @endif

                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Description VN <span
                                                    class="text-red">*</span></label>
                                            <input
                                                class="form-control @if ($errors->has('seo_description_vi')) is-invalid @endif"
                                                name="seo_description_vi"
                                                value="{{ old('seo_description_vi', $dataSeo ? $dataSeo->description_vi : '') }}"
                                                autocomplete="off">
                                            @if ($errors->has('seo_description_vi'))
                                            <span class="text-danger">
                                                {{ $errors->first('seo_description_vi') }} </span>
                                            @endif
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Keyword VN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control @if ($errors->has('seo_keyword_vi')) is-invalid @endif"
                                                name="seo_keyword_vi"
                                                value="{{ old('seo_keyword_vi', $dataSeo ? $dataSeo->keyword_vi : '') }}"
                                                autocomplete="off">
                                            @if ($errors->has('seo_keyword_vi'))
                                            <span class="text-danger">
                                                {{ $errors->first('seo_keyword_vi') }} </span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Title EN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control @if ($errors->has('seo_title_en')) is-invalid @endif"
                                                name="seo_title_en"
                                                value="{{ old('seo_title_en', $dataSeo ? $dataSeo->title_en : '') }}"
                                                autocomplete="off">
                                            @if ($errors->has('seo_title_en'))
                                            <span class="text-danger">
                                                {{ $errors->first('seo_title_en') }} </span>
                                            @endif
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Description EN <span
                                                    class="text-red">*</span></label>
                                            <input
                                                class="form-control @if ($errors->has('seo_description_en')) is-invalid @endif"
                                                name="seo_description_en"
                                                value="{{ old('seo_description_en', $dataSeo ? $dataSeo->description_en : '') }}"
                                                autocomplete="off">
                                            @if ($errors->has('seo_description_en'))
                                            <span class="text-danger">
                                                {{ $errors->first('seo_description_en') }} </span>
                                            @endif
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Keyword EN <span class="text-red">*</span></label>
                                            <input
                                                class="form-control @if ($errors->has('seo_keyword_en')) is-invalid @endif"
                                                name="seo_keyword_en"
                                                value="{{ old('seo_keyword_en', $dataSeo ? $dataSeo->keyword_en : '') }}"
                                                autocomplete="off">
                                            @if ($errors->has('seo_keyword_en'))
                                            <span class="text-danger">
                                                {{ $errors->first('seo_keyword_en') }} </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="form-label">Image share</label>
                                        <input type="file" class="dropify" name="image"
                                            data-default-file="{{ isset($dataSeo->image) ? gen_url_file_s3($dataSeo->image, '', false) : '' }}"
                                            data-height="180" />
                                    </div>
                                </div>

                                <div class="card-footer text-right">
                                    <a href="{{ route('job.index') }}" class="btn btn-danger btn-lg">Close</a>
                                    <button class="btn btn-success btn-lg">Submit</button>
                                </div>
                            </form>
                        </div>
                        @endif
                        @if (\App\Services\Admin\PermissionService::checkPermission('job-meta-update'))
                        <div class="tab-pane " id="meta">
                            <form action="{{ route('job-meta-update', ['job' => $data->id]) }}"
                                enctype="multipart/form-data" method="post">
                                @csrf
                                <div class="form-group">
                                    <label class="form-label">Độ ưu tiên <span class="text-red">*</span></label>
                                    <select class="form-control @if ($errors->has('priority')) is-invalid @endif"
                                        name="priority" data-placeholder="--- Chọn ---">
                                        <option label="--- Chọn ---"></option>
                                        @foreach ($priority as $k => $v)
                                        <option value="{{ $v }}" {{ old('priority', $dataMeta->priority) == $v ?
                                            'selected' : '' }}>
                                            {{ $v }}</option>
                                        @endforeach
                                    </select>
                                    @if ($errors->has('priority'))
                                    <span class="text-danger"> {{ $errors->first('priority') }} </span>
                                    @endif
                                </div>
                                <div class="form-group">
                                    <label class="form-label"> Script JS </label>
                                    <textarea class="form-control @if ($errors->has('meta_script')) is-invalid @endif"
                                        name="meta_script"
                                        autocomplete="off">{{ old('meta_script', $dataMeta->script) }}</textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Hiển thị trên trang chủ <span
                                            class="text-red">*</span></label>
                                    <label class="custom-switch">
                                        <input type="checkbox" @if (old('show_home_page', $dataMeta->is_show) == 1)
                                        checked @endif value="1"
                                        name="show_home_page" class="custom-switch-input remote">
                                        <span class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                    </label>
                                </div>

                                <div class="card-footer text-right">
                                    <a href="{{ route('job.index') }}" class="btn btn-danger btn-lg">Close</a>
                                    <button class="btn btn-success btn-lg">Submit</button>
                                </div>
                            </form>
                        </div>
                        @endif
                        @if (\App\Services\Admin\PermissionService::checkPermission('ref-datatable'))
                        <div class="tab-pane" id="ref">
                            <div class="row">
                                <div class="col-xl-12 col-md-12 col-lg-12">
                                    {{ $datatable->render() }}
                                </div>
                            </div>
                        </div>
                        @endif
                        <!-- Thêm nội dung tab hỏi đáp -->
                        <div class="tab-pane" id="qa">
                            <div class="row">
                                <div class="col-xl-12 col-md-12 col-lg-12">
                                    <div id="job-comments">
                                        <job-comments :job-id="{{ $data->id }}"></job-comments>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </form>

</div>
</div>

@endsection


@section('scripts')
<script src="{{ asset2('frontend/asset/js/additional-methods.min.js') }}"></script>
<script src="{{ asset2('frontend/asset/ckeditor/ckeditor.js') }}"></script>
<!-- INTERNAL File uploads js -->
<script src="{{ asset2('backend/assets/plugins/fileupload/js/dropify.js') }}"></script>
<script src="{{ asset2('backend/assets/js/filupload.js') }}"></script>
<script src="{{ asset2('js/admin/box-job-comments.js') }}"></script>
<script>
    $(document).ready(function() {
            CKEDITOR.replace('jd_description', {
                filebrowserImageBrowseUrl: '/file-manager/ckeditor'
            });
            CKEDITOR.replace('jd_request', {
                filebrowserImageBrowseUrl: '/file-manager/ckeditor'
            });
            CKEDITOR.replace('jd_welfare', {
                filebrowserImageBrowseUrl: '/file-manager/ckeditor'
            });

            $('[name="is_active"]').prop("checked") ? $('#status_active').html('Active') : $('#status_active').html(
                'Inactive');
            $('[name="is_active"]').change(function() {
                if ($(this).prop("checked")) {
                    $('#status_active').html('Active');
                } else {
                    $('#status_active').html('Inactive');
                }
            });

            var dateToday = new Date();
            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy',
                minDate: dateToday,
            })

            $(document).on('click', '.ti-import', function() {
                let url = $(this).data('value');
                let route = '{{ route('download-file') }}' + '?url=' + url;

                window.open(route, '_blank');
            });

            $(document).on('click', '.fa-eye', function() {
                let url = $(this).data('value');
                window.open(url, '_blank');
            });

            $(document).on('click', '.sbm_form', function() {
                $('.flg_status').val('1');
                // kieerm tồn tại permission
                @php
                    if (!PermissionService::checkPermission('job.custom-price')) {
                @endphp
                if ($('#bonus').val() < min_price) {
                    alert('Giá hoa hồng không được nhỏ hơn giá tối thiểu');
                    return;
                }
                @php
                    }
                @endphp
                $('.sbm_form_s').get(0).submit();
            });

            $(".js-example-tokenizer").select2({
                tags: true,
            })

            $(".remote").click(function() {
                let value = $("[name='remote']:checked").val();
                if (value) {
                    $('.area-address').css('display', 'none');
                } else {
                    $('.area-address').css('display', 'block');
                }
            })

            var clicks = 0;
            $('.company-select').change(function() {
                let id = $(this).val();
                let oldId = '{{ $data->company_id }}'
                // if (id != oldId) {
                let url = '{{ route('ajax-company.show', ':id') }}';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'get',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        let companyObj = res[0];
                        let listEmployer = res[1];
                        let arrAddress = JSON.parse(companyObj.address);
                        if (clicks == 0) {
                            // first click
                        } else {
                            // second click
                            // for (let i = 0; i < 3; i++) {
                            //     if (arrAddress[i]) {
                            //         $('.area' + i).val(arrAddress[i].area).trigger('change');
                            //         $('.address' + i).val(arrAddress[i].address);
                            //     } else {
                            //         $('.area' + i).val('').trigger('change');
                            //         $('.address' + i).val('');
                            //     }
                            // }
                        }
                        ++clicks;

                        var output = [];
                        var oldEmployer = '{!! json_encode($employer) !!}'
                        var employer = JSON.parse(oldEmployer);
                        $.each(listEmployer, function(key, value) {
                            if (value.id == employer[0].id) {
                                output.push('<option value="' + value.id +
                                    '" selected>' + value.name + '</option>');
                            } else {
                                output.push('<option value="' + value.id + '">' + value
                                    .name + '</option>');
                            }
                        });
                        $('#employer-select').html(output.join(''));
                    }
                });
                // }
            });

            //chang skill
            let url = '{{ route('ajax-job.list-skill') }}';
            $("#skill_id").select2({
                tags: true,
                ajax: {
                    url: url,
                    type: "post",
                    dataType: 'json',
                    delay: 550,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });

            //chang company
            let urlCompany = '{{ route('ajax-employer.change-company') }}';
            $("#company_id").select2({
                ajax: {
                    url: urlCompany,
                    type: "post",
                    dataType: 'json',
                    delay: 250,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });
            $("#company_id").val($('#hdd_company_id').val()).trigger('change');
        })

        $(document).ready(function() {

            // function getSkillIT(career = "") {
            //     $('.skill-main').select2({
            //         dropdownParent: "#modal-sell-cv",
            //         // tags: true,
            //         placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang') }}',
            //         allowClear: true,
            //         ajax: {
            //             url: '{{ route('ajax-get-skill-main-it') }}' + '?career=' + career,
            //             type: "get",
            //             dataType: 'json',
            //             headers: {
            //                 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //             },
            //             data: function(params) {
            //                 return {
            //                     searchTerm: params.term // search term
            //                 };
            //             },
            //             processResults: function(response) {
            //                 return {
            //                     results: $.map(response, function(item) {
            //                         return {
            //                             ...item,
            //                             text: item.name_en
            //                         }
            //                     })
            //                 };
            //             },
            //         },
            //     })
            // }

            function getMinPrice() {
                $('#bonus').prop('disabled', true);
                // if ($('#skill_id').select2('data')[0]) {
                // let group = $('#skill_id').select2('data')[0].group;
                // let isIT = $('#skill_id').select2('data')[0].is_it;
                // }
                if (typeof group == 'undefined' || !group) group = '';
                if (typeof isIT == 'undefined' || !isIT) isIT = '';

                console.log('group: ' + group, 'isIT: ' + isIT);

                let level = $('#rank-select').val();
                let skill_id = $('#skill_id').val();
                let bonus_type = $('#recruitment-type').val();
                let career = $('#career-select').val();
                let salary_min = $('#field_salary_min').val();
                let salary_max = $('#field_salary_max').val();
                let salary_currency = $('.salary_currency').val();
                console.log('level: ' + level);

                if (bonus_type == 'onboard' && salary_min == '') {
                    $('#bonus_msg').html(
                        'Vui lòng nhập lương tối thiểu & hình thức tuyển dụng để tính chi phí tối thiểu');
                    return;
                }
                if (!level || !bonus_type || !skill_id) {
                    $('#bonus_msg').html('Vui lòng chọn cấp bậc, hình thức tuyển dụng và kỹ năng chính để xem giá');
                    return;
                }


                console.log('level: ' + level, 'bonus_type: ' + bonus_type, 'career: ' + career, 'isIT: ' + isIT,
                    'group: ' + group);
                $.ajax({
                    url: '{{ route('ajax-get-min-submit-price') }}' + '?group=' + group + '&is_it=' +
                        isIT + '&bonus_type=' + bonus_type + '&level=' + level + '&career=' + career +
                        '&salary_min=' + salary_min + '&salary_max=' + salary_max + '&salary_currency=' + salary_currency + '&skill_id=' +
                        skill_id,
                    type: "get",
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#bonus').prop('disabled', false);
                        min_price = res.data.min_price;
                        $('#min_price').html(formatCurrency(min_price));
                        $('#bonus_msg').html('Giá tối thiểu là ' + formatCurrency(min_price) + ' vnđ');
                        // if (res) {
                        //     if (type == 'cv') {
                        //         $('.min-price').html(formatCurrency(res.price_cv_min))
                        //         $('.max-price').html(formatCurrency(res.price_cv_max))
                        //         $("input[name='min-price']").val(res.price_cv_min);
                        //         $("input[name='max-price']").val(res.price_cv_max);
                        //     } else if (type == 'interview') {
                        //         $('.min-price').html(formatCurrency(res.price_interview_min))
                        //         $('.max-price').html(formatCurrency(res.price_interview_max))
                        //         $("input[name='min-price']").val(res.price_interview_min);
                        //         $("input[name='max-price']").val(res.price_interview_max);
                        //     }
                        // }
                    }
                })
            }

            function getSkillIT(career = "") {
                let url = '{{ route('ajax-get-skill-main-it') }}?career=' + career;
                $("#skill_id").select2({
                    tags: true,
                    placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}',
                    allowClear: true,
                    maximumSelectionLength: 3,
                    ajax: {
                        url: url,
                        type: "get",
                        dataType: 'json',
                        delay: 550,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                        cache: false
                    }
                });
                // $('#skill_id').select2({
                //     // dropdownParent: "#modal-sell-cv",
                //     // tags: true,
                //     placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang') }}',
                //     allowClear: true,
                //     ajax: {
                //         url: '{{ route('ajax-get-skill-main-it') }}' + '?career=' + career,
                //         type: "get",
                //         dataType: 'json',
                //         headers: {
                //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                //         },
                //         data: function (params) {
                //             return {
                //                 searchTerm: params.term // search term
                //             };
                //         },
                //         processResults: function (response) {
                //             return {
                //                 results: $.map(response, function (item) {
                //                     return {
                //                         ...item,
                //                         text: item.name_en
                //                     }
                //                 })
                //             };
                //         },
                //     },
                // })
            }

            function loadInitData() {
                // load du lieu vao skill va cap bac
                let career = $('#career-select').val();
                let url = '{{ route('ajax-get-skill-main-it') }}?career=' + career;
                console.log(url);

                $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        let skillSelect = $('#skill_id');
                        skillSelect.empty(); // Xóa tất cả options hiện tại

                        // Thêm option mặc định
                        skillSelect.append($('<option>', {
                            value: '',
                            text: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}'
                        }));

                        // Thêm các options từ dữ liệu trả về
                        $.each(response, function(index, item) {
                            skillSelect.append($('<option>', {
                                value: item.id,
                                text: item.name_en
                            }));
                        });
                        getSkillIT(career);
                        $('#skill_id').val({{ $job->skill_id }}).trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error("Lỗi khi tải dữ liệu kỹ năng:", error);
                    }
                });


                $.ajax({
                    url: '{{ route('ajax-get-level-by-career') }}' +
                        '?career_id={{ $job->career }}&skill_id={{ $job->skill_id }}',
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        let rankSelect = $('#rank-select');
                        rankSelect.empty(); // Xóa tất cả options hiện tại

                        // Thêm option mặc định
                        rankSelect.append($('<option>', {
                            value: '',
                            text: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}'
                        }));

                        // Thêm các options từ dữ liệu trả về
                        $.each(response, function(index, item) {
                            rankSelect.append($('<option>', {
                                value: item.id,
                                text: item.name_vi
                            }));
                        });
                        getLevel({
                            group: '{{ $skill && $skill->group ? $skill->group : 0 }}',
                            isIT: '{{ $isIt ? 'true' : 'false' }}'
                        });
                        $('#rank-select').val({{ $job->rank }}).trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error("Lỗi khi tải dữ liệu kỹ năng:", error);
                    }
                });
                // $.each(skill, function(key, value) {
                //     output.push('<option value="' + value.id + '">' + value.name + '</option>');
                // });
                $('#career-select').val({{ $job->career }}).trigger('change');
                $('#recruitment-type').val('{{ $job->bonus_type }}').trigger('change');
            }
            // loadInitData();

            $('#career-select').change(function() {
                let career = $(this).val();
                $("#skill_id").empty();
                getSkillIT(career);
            });

            $('#rank-select').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#recruitment-type').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('[data-target="#field_salary_max"]').blur(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#skill_id').change(function(e) {
                if ($(this).val()) {
                    $('#rank-select').val(null).trigger('change');
                    $('#recruitment-type').val(null).trigger('change');
                    let group = $(this).select2('data')[0].group;
                    let isIT = $(this).select2('data')[0].is_it;
                    getLevel({
                        group,
                        isIT
                    });
                }
            });
            getMinPrice();
            calculateTotalCost();

            function getLevel({
                group,
                isIT
            }) {
                var career_id = $('#career-select').val();
                var skill_id = $('#skill_id').val();
                $('#rank-select').select2({
                    // dropdownParent: "#modal-sell-cv",
                    // tags: true,
                    placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_cv_selling.chonlevel') }}',
                    allowClear: true,
                    ajax: {
                        url: '{{ route('ajax-get-level-by-career') }}' + '?career_id=' + career_id +
                            '&skill_id=' + skill_id,
                        type: "get",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                    },
                })
            }

            $(".select2-custom-tags").select2({
                tags: true,
                placeholder: '{{ $arrLang['input'] }} {{ $arrLang['kinang'] }}',
                allowClear: true
            });

            // Khởi tạo select2 cho trường hình thức tuyển dụng
            $('#recruitment-type').select2({
                placeholder: '{{ $arrLang['chon'] ?? 'Chọn' }} {{ lcfirst($arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng') }}',
                allowClear: true,
                minimumResultsForSearch: -1,
                // dropdownParent: "#modal-sell-cv",
                // dropdownCssClass: 'customer-dropdown-select2'
            });
            // Tính tổng tiền khi thay đổi chi phí hoặc số lượng tuyển
            $('#bonus, input[name="vacancies"]').on('input', function() {
                calculateTotalCost();
            });

            function calculateTotalCost() {
                var cost = parseFloat($('#bonus').val()) || 0;
                var bonus_for_ctv = parseFloat($('#bonus_for_ctv').val()) || 0;
                var vacancies = parseInt($('input[name="vacancies"]').val()) || 0;
                var total = cost * vacancies;
                $('#total-cost').val(total.toLocaleString('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }));
                // $('#bonus_for_ctv').val(bonus_for_ctv.toLocaleString('vi-VN', {
                //     style: 'currency',
                //     currency: 'VND'
                // }));
            }

            $('[data-toggle="add-item"]').click(function() {
                // let html = $('.item-clone').html();
                let countHtmlAddress = $('.render-address').length;
                if (countHtmlAddress <= 3) {
                    let cities = '{!! json_encode($cities) !!}';
                    cities = JSON.parse(cities);
                    let options;
                    $.each(cities, function(key, value) {
                        options = options + '<option value="' + key + '">' + value + '</option>';
                    });
                    let html = '';
                    html += '<div class="row item render-address">'
                    html +=
                        '<div class="col-md-3"><div class="form-item"><select style="width: 100%" class="select-clone area' +
                        (countHtmlAddress - 1) + '" name="address[' + (countHtmlAddress - 1) + '][area]">'
                    html += options
                    html += '</select></div></div>'
                    html +=
                        '<div class="col-md-9"><div class="row"><div class="col-md-11 col-form-item"><div class="form-item">'
                    html += '<input type="text" placeholder="Địa chỉ cụ thể" name="address[' + (
                            countHtmlAddress - 1) + '][address]" class="address' + (countHtmlAddress - 1) +
                        '">'
                    html += '</div></div><div class="col-md-1 col-remove-btn">'
                    html += '<a href="javascript:void(0)" data-toggle="removeitem">'
                    html +=
                        '<img src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-remove.svg') }}">'
                    html += '</a></div></div></div>'


                    $('.group-field-repeater').append(html);
                    $('.group-field-repeater .select-clone').select2();
                }
            })
            $('body').on('click', '[data-toggle="removeitem"]', function() {
                $(this).parents('.item').remove();
            })
            CKEDITOR.replace('jd_description');
            CKEDITOR.replace('jd_request');
            CKEDITOR.replace('jd_welfare');

            var dateToday = new Date();
            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy',
                minDate: dateToday,
            });

            let urlCompany = '{{ route('ajax-list-company') }}';
            $("#company_id").select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['congty']) }}',
                allowClear: true,
                ajax: {
                    url: urlCompany,
                    type: "get",
                    dataType: 'json',
                    delay: 250,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });
            $("#company_id").val($('#hdd_company_id').val()).trigger('change');

            $('#company_id').change(function() {
                let id = $(this).val();
                let url = '{{ route('ajax-detail-company', ':id') }}';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'get',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        let companyObj = res[0];
                        let listEmployer = res[1];
                        let arrAddress = JSON.parse(companyObj.address);
                        $.each(arrAddress, function(key, value) {
                            if (key == 0) {

                            } else if (value['area']) {
                                $('[data-toggle="add-item"]').trigger('click');
                            }
                            $('.area' + key).val(value.area).trigger('change');
                            $('.address' + key).val(value.address);
                        });

                        var output = [];
                        $.each(listEmployer, function(key, value) {
                            output.push('<option value="' + value.id + '">' + value
                                .name + '</option>');
                        });
                        $('#employer-select').html(output.join(''));
                    }
                });
            });

            $("#form-job").validate({
                ignore: [],
                debug: false,
                rules: {
                    name: {
                        required: true,
                    },
                    expire_at: {
                        required: true,
                    },
                    company_id: {
                        required: true,
                    },
                    vacancies: {
                        required: true,
                        number: true,
                        digits: true,
                    },
                    employer_id: {
                        required: true,
                    },
                    'rank[]': {
                        required: true,
                    },
                    'career[]': {
                        required: true,
                    },
                    type: {
                        required: true,
                    },
                    'skill[]': {
                        required: true,
                    },
                    salary_min: {
                        required: true,
                        number: true,
                    },
                    salary_max: {
                        required: true,
                        number: true,
                    },
                    file_jd: {
                        @if (!$job->file_jd)
                            required: true,
                        @endif
                        extension: "pdf,docx",
                        filesize: 5, // <- 5 MB
                    },
                    jd_description: {
                        ckrequired: true,
                    },
                    jd_request: {
                        ckrequired: true,
                    },
                    jd_welfare: {
                        ckrequired: true,
                    },
                    "address[0][area]": {
                        required: true
                    },
                    "address[0][address]": {
                        required: true
                    },

                },
                messages: {
                    name: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    expire_at: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    company_id: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    vacancies: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                        digits: '{{ __('frontend/validation.number') }}',
                    },
                    employer_id: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    'rank[]': {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    'career[]': {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    type: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    'skill[]': {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    salary_min: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                    },
                    salary_max: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                    },
                    file_jd: {
                        required: '{{ __('frontend/validation.required') }}',
                        extension: '{{ __('frontend/validation.format_cv') }}',
                        filesize: '{{ __('frontend/validation.file_max') }}',
                    },
                    jd_description: {
                        ckrequired: '{{ __('frontend/validation.required') }}',
                    },
                    jd_request: {
                        ckrequired: '{{ __('frontend/validation.required') }}',
                    },
                    jd_welfare: {
                        ckrequired: '{{ __('frontend/validation.required') }}',
                    },
                    "address[0][area]": {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    "address[0][address]": {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                },

                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else if (elem.hasClass("field-input-unit")) {
                        elem.parent().addClass(errorClass);
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        console.log(errorClass);
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    console.log(elem);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-input-unit")) {
                        element = elem.parent();
                        error.insertAfter(element);
                    } else if (elem.hasClass("file-browserinput")) {
                        element = elem.parents('.input-file');
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-ck")) {
                        element = elem.parents('.ck-custom');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            $.validator.addMethod('regex', function(value) {
                var regex = /^[0-9()+.-]*$/;
                return value.trim().match(regex);
            });

            $.validator.addMethod('filesize', function(value, element, param) {
                return this.optional(element) || (element.files[0].size <= param * 1000000)
            }, 'File size must be less than {0} MB');

            $(document).on('click', '.btn-save', function() {
                if ($("#form-job").valid()) {
                    $('#form-job').submit();
                }
            });

            $('#type-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['hinhthuc']) }}',
                allowClear: true
            });

            $('#career-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['linhvuc']) }}',
                allowClear: true,
                maximumSelectionLength: 3
            });

            $('#rank-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['capbac']) }}',
                allowClear: true,
                maximumSelectionLength: 3
            });

            $('#employer-select').select2({
                placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['nhatuyendung']) }}',
                allowClear: true
            });

            $('.address-select').select2({
                placeholder: '{{ lcfirst($arrLang['diadiem']) }}',
                allowClear: true
            });

            let url = '{{ route('ajax-list-skill') }}';
            // $("#skill_id").select2({
            //     tags: true,
            //     placeholder: '{{ $arrLang['chon'] }} {{ lcfirst($arrLang['kinang']) }}',
            //     allowClear: true,
            //     maximumSelectionLength: 3,
            //     ajax: {
            //         url: url,
            //         type: "get",
            //         dataType: 'json',
            //         delay: 550,
            //         headers: {
            //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //         },
            //         data: function(params) {
            //             return {
            //                 searchTerm: params.term // search term
            //             };
            //         },
            //         processResults: function(response) {
            //             return {
            //                 results: response
            //             };
            //         },
            //         cache: true
            //     }
            // });

            $('.salary_currency').change(function() {
                let val = $(this).val();
                $('.salary_currency_max').val(val).trigger('change');
            });

            $('.btn-reload').click(function() {
                location.reload();
            })

            $(document).on('click', '.show-iframe', function() {
                let url = $(this).data('url');
                $(".iframe").attr('src', url);
                $("#modal-cv").modal('show');
            });

            $(document).on('click', '.back-header', function() {
                $("#modal-cv").modal('hide');
            });
            $('#status').select2({
                minimumResultsForSearch: Infinity
            });
            $('#is_active').select2({
                minimumResultsForSearch: Infinity
            });

            $('#bonus').change(function() {
                calculateBonusCtv();
            });

            $('#recruitment-type').change(function() {
                calculateBonusCtv();
            });

            $('.calculate-bonus').click(function() {
                calculateBonusCtv();
            });

            function calculateBonusCtv() {
                
                let bonus = $('#bonus').val();
                let bonus_type = $('#recruitment-type').val();
                
                if (!bonus || !bonus_type) {
                    alert('Vui lòng điền đầy đủ thông tin chi phí, số lượng tuyển, kỹ năng, cấp bậc và hình thức tuyển dụng');
                    return;
                }

                $.ajax({
                    url: '{{ route("ajax-calculate-bonus-ctv") }}',
                    type: 'POST',
                    data: {
                        bonus: bonus,
                        bonus_type: bonus_type
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#bonus_for_ctv').val(formatCurrency(response.bonus_for_ctv));
                        } else {
                            alert(response.message || 'Có lỗi xảy ra khi tính toán');
                        }
                    },
                    error: function() {
                        alert('Có lỗi xảy ra khi tính toán');
                    }
                });
            }
            // Hàm format tiền tệ
            function formatCurrency(amount) {
                return new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }).format(amount);
            }
        })
</script>
<!-- Thêm Vue và component -->
<script src="{{ asset('js/app.js') }}"></script>
<script>
    new Vue({
            el: '#job-comments'
        });
</script>
@endsection