{"__meta": {"id": "01K2E0PG8VM5FT2GSVKAGNFBYS", "datetime": "2025-08-12 09:10:40", "utime": **********.028071, "method": "GET", "uri": "/admin/job/1648/edit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 17, "messages": [{"message": "[09:10:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 22.5\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.986412, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)\\n-- \",\n    \"Time:\": 0.61\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.99493, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `job`.`id` = '1648' limit 1\\n-- \",\n    \"Time:\": 0.66\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.99935, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops`\\n-- \",\n    \"Time:\": 0.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.001712, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops` where `id` = '2' limit 1\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.004583, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops` where `id` = '2' limit 1\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.00878, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `level_by_job_tops` where `group` = '1'\\n-- \",\n    \"Time:\": 0.49\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.011028, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `job`.`id` = '1648' limit 1\\n-- \",\n    \"Time:\": 0.62\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.013155, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_meta` where `job_id` = '1648' limit 1\\n-- \",\n    \"Time:\": 2.51\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.017186, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_seo` where `job_id` = '1648' limit 1\\n-- \",\n    \"Time:\": 2.13\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.020929, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `name` from `skills` order by `id` desc limit 50\\n-- \",\n    \"Time:\": 0.36\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.025004, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '84' limit 1\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.027356, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `type` = 'employer' and `company_id` = '84'\\n-- \",\n    \"Time:\": 15.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.044017, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.045939, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:39] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `user_role` where `user_role`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.3\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.048676, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:40] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `job`.`id` = '1648' limit 1\\n-- \",\n    \"Time:\": 0.84\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.0143, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:40] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '84' limit 1\\n-- \",\n    \"Time:\": 0.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.016083, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.512309, "end": **********.028112, "duration": 1.5158028602600098, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": **********.512309, "relative_start": 0, "end": **********.883656, "relative_end": **********.883656, "duration": 0.*****************, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.883677, "relative_start": 0.****************, "end": **********.028115, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.905534, "relative_start": 0.*****************, "end": **********.912354, "relative_end": **********.912354, "duration": 0.006819963455200195, "duration_str": "6.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x admin.pages.job.edit", "param_count": null, "params": [], "start": **********.058049, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/job/edit.blade.phpadmin.pages.job.edit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Fpages%2Fjob%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.job.edit"}, {"name": "1x admin.inc_layouts.datatable.table", "param_count": null, "params": [], "start": **********.017725, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/datatable/table.blade.phpadmin.inc_layouts.datatable.table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fdatatable%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.datatable.table"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.020629, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x admin.inc_layouts.side_bar", "param_count": null, "params": [], "start": **********.021839, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.phpadmin.inc_layouts.side_bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fside_bar.blade.php&line=1", "ajax": false, "filename": "side_bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.side_bar"}, {"name": "1x admin.inc_layouts.header", "param_count": null, "params": [], "start": **********.023339, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/header.blade.phpadmin.inc_layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.header"}, {"name": "1x admin.inc_layouts.footer", "param_count": null, "params": [], "start": **********.02405, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/footer.blade.phpadmin.inc_layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.footer"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.025156, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET admin/job/{job}/edit", "middleware": "web, check-admin, check-role", "as": "job.edit", "controller": "App\\Http\\Controllers\\Admin\\JobController@edit<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/JobController.php:84-133</a>"}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.048979999999999996, "accumulated_duration_str": "48.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.96416, "duration": 0.0225, "duration_str": "22.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 0, "width_percent": 45.937}, {"sql": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, {"index": 19, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 20, "namespace": "middleware", "name": "check-role", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRole.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 22, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 21}], "start": **********.994391, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "PermissionService.php:19", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FAdmin%2FPermissionService.php&line=19", "ajax": false, "filename": "PermissionService.php", "line": "19"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "hash": "09b63fe1be07508dfb18b73a990c5ee0249dd740fb6501945ccdb8043c3b971c"}, "start_percent": 45.937, "width_percent": 1.245}, {"sql": "select * from `job` where `job`.`id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 156}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 87}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9987621, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `job`.`id` = ? limit 1", "hash": "f88a85fef5dc70adb996a33ffa653271c0e40e6e958eebc918f2a3cfdbaba770"}, "start_percent": 47.183, "width_percent": 1.347}, {"sql": "select * from `job_tops`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 93}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.001241, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "JobController.php:93", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=93", "ajax": false, "filename": "JobController.php", "line": "93"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops`", "hash": "e483b570db4a4d95eef34975f1de4502d417ef682ca65f1939c31565ac3f693d"}, "start_percent": 48.53, "width_percent": 1.102}, {"sql": "select * from `job_tops` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 94}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.004276, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "JobController.php:94", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=94", "ajax": false, "filename": "JobController.php", "line": "94"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops` where `id` = ? limit 1", "hash": "1bfd90076618dd93defc42058a65a53d6e3761f1201d91e16fa7f1f695c75442"}, "start_percent": 49.633, "width_percent": 0.776}, {"sql": "select * from `job_tops` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 170}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.00849, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingService.php:170", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 170}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FWareHouseCvSellingService.php&line=170", "ajax": false, "filename": "WareHouseCvSellingService.php", "line": "170"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops` where `id` = ? limit 1", "hash": "1bfd90076618dd93defc42058a65a53d6e3761f1201d91e16fa7f1f695c75442"}, "start_percent": 50.408, "width_percent": 0.735}, {"sql": "select * from `level_by_job_tops` where `group` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/LevelByJobTopRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\LevelByJobTopRepository.php", "line": 21}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 172}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0106091, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "LevelByJobTopRepository.php:21", "source": {"index": 14, "namespace": null, "name": "app/Repositories/LevelByJobTopRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\LevelByJobTopRepository.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FLevelByJobTopRepository.php&line=21", "ajax": false, "filename": "LevelByJobTopRepository.php", "line": "21"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `level_by_job_tops` where `group` = ?", "hash": "f995fef5746d1b596de4a0412d67262f529d43944a2f29a7712d18bf19c14e20"}, "start_percent": 51.143, "width_percent": 1}, {"sql": "select * from `job` where `job`.`id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 156}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 100}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0126052, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `job`.`id` = ? limit 1", "hash": "f88a85fef5dc70adb996a33ffa653271c0e40e6e958eebc918f2a3cfdbaba770"}, "start_percent": 52.144, "width_percent": 1.266}, {"sql": "select * from `job_meta` where `job_id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobMetaRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobMetaRepository.php", "line": 13}, {"index": 16, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 305}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 101}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.014745, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "JobMetaRepository.php:13", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobMetaRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobMetaRepository.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobMetaRepository.php&line=13", "ajax": false, "filename": "JobMetaRepository.php", "line": "13"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_meta` where `job_id` = ? limit 1", "hash": "09032322ddf5999d1ad23948334282f93608772864355fcc886b4ea5b140dd64"}, "start_percent": 53.41, "width_percent": 5.125}, {"sql": "select * from `job_seo` where `job_id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobSeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobSeoRepository.php", "line": 13}, {"index": 16, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 310}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.018869, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "JobSeoRepository.php:13", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobSeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobSeoRepository.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobSeoRepository.php&line=13", "ajax": false, "filename": "JobSeoRepository.php", "line": "13"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_seo` where `job_id` = ? limit 1", "hash": "8f1dae58f00721492063c9782b980990ec969f0f23c92e92ca2b4119e6222f03"}, "start_percent": 58.534, "width_percent": 4.349}, {"sql": "select `name` from `skills` order by `id` desc limit 50", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SkillRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SkillRepository.php", "line": 64}, {"index": 14, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 528}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 172}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.024714, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "SkillRepository.php:64", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SkillRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SkillRepository.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSkillRepository.php&line=64", "ajax": false, "filename": "SkillRepository.php", "line": "64"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `name` from `skills` order by `id` desc limit 50", "hash": "39c8ce3e3ccb0f0611e4e603d54866b828dad39a2aac32d490aca8a5b82cb28f"}, "start_percent": 62.883, "width_percent": 0.735}, {"sql": "select * from `companies` where `companies`.`id` = 84 limit 1", "type": "query", "params": [], "bindings": [84], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Admin/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\CompanyService.php", "line": 163}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.027026, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? limit 1", "hash": "cc1297ee2bc563ebfe1a5606d32a0266e1723b55aa2725a781741e51d0fb88ab"}, "start_percent": 63.618, "width_percent": 0.817}, {"sql": "select * from `users` where `type` = 'employer' and `company_id` = 84", "type": "query", "params": [], "bindings": ["employer", 84], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 177}, {"index": 15, "namespace": null, "name": "app/Services/Admin/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\CompanyService.php", "line": 164}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0287368, "duration": 0.015349999999999999, "duration_str": "15.35ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:177", "source": {"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=177", "ajax": false, "filename": "UserRepository.php", "line": "177"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `type` = ? and `company_id` = ?", "hash": "50e2651ef65fee1e6bfdfa38a3165d3eef3b4a87f8f89f45d596bfd1bbd1c2f5"}, "start_percent": 64.434, "width_percent": 31.339}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, {"index": 17, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 156}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 117}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.045598, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:96", "source": {"index": 16, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=96", "ajax": false, "filename": "UserRepository.php", "line": "96"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 95.774, "width_percent": 0.837}, {"sql": "select * from `user_role` where `user_role`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, {"index": 22, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 156}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 117}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0484471, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:96", "source": {"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=96", "ajax": false, "filename": "UserRepository.php", "line": "96"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `user_role` where `user_role`.`user_id` in (1723)", "hash": "9fca1e5a30e20c1de59afd1eeeb503483febe4f0420eb57dd58fb1bd405df992"}, "start_percent": 96.611, "width_percent": 0.612}, {"sql": "select * from `job` where `job`.`id` = 1648 limit 1", "type": "query", "params": [], "bindings": [1648], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/JobSeo.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\JobSeo.php", "line": 35}, {"index": 26, "namespace": "view", "name": "admin.pages.job.edit", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/job/edit.blade.php", "line": 742}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.013544, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "JobSeo.php:35", "source": {"index": 20, "namespace": null, "name": "app/Models/JobSeo.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\JobSeo.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobSeo.php&line=35", "ajax": false, "filename": "JobSeo.php", "line": "35"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `job`.`id` = ? limit 1", "hash": "47559d9dc6d9934ac24ab049bef05e7c7b62e130d8bb931e50b6730076cdfb30"}, "start_percent": 97.223, "width_percent": 1.715}, {"sql": "select * from `companies` where `companies`.`id` = 84 limit 1", "type": "query", "params": [], "bindings": [84], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/JobSeo.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\JobSeo.php", "line": 35}, {"index": 27, "namespace": "view", "name": "admin.pages.job.edit", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/job/edit.blade.php", "line": 742}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.015646, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? limit 1", "hash": "cc1297ee2bc563ebfe1a5606d32a0266e1723b55aa2725a781741e51d0fb88ab"}, "start_percent": 98.938, "width_percent": 1.062}]}, "models": {"data": {"App\\Models\\JobTop": {"retrieved": 59, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobTop.php&line=1", "ajax": false, "filename": "JobTop.php", "line": "?"}}, "App\\Models\\LevelByJobTop": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FLevelByJobTop.php&line=1", "ajax": false, "filename": "LevelByJobTop.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Job": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\JobMeta": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobMeta.php&line=1", "ajax": false, "filename": "JobMeta.php", "line": "?"}}, "App\\Models\\JobSeo": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobSeo.php&line=1", "ajax": false, "filename": "JobSeo.php", "line": "?"}}}, "count": 80, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 80}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/jobs-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/job/1648/edit", "action_name": "job.edit", "controller_action": "App\\Http\\Controllers\\Admin\\JobController@edit", "uri": "GET admin/job/{job}/edit", "controller": "App\\Http\\Controllers\\Admin\\JobController@edit<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/JobController.php:84-133</a>", "middleware": "web, check-admin, check-role", "duration": "1.52s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1466637389 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1466637389\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1218856154 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1218856154\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2146078733 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://recland.local/admin/jobs-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Iml1c1Z1V3Y1ZHMrY2R2UFNweWw4VGc9PSIsInZhbHVlIjoiV3dFUGZFZHBDQzVSb3JmQzc0Z0pOa0VOYXRpMlFUM3g3QzhWZkl4R3I0MU9YeGNFVVl0OXRpOG5Fam9oUnBqZkwvRnVnOFRnNkhyK1V1dnBIczc1WjN4SUdSc1ZoeVQvNnBNN29UaDg2bnhmZ0xHM1FpSkJrYUNUekNaRVc3NEkiLCJtYWMiOiI3ODI1ZWQ3NzMxNWQxZTZiMDRkMWJkNTkxZDJlYmMxNjM0MDE1YjFhMGEyNjIxN2JjMDc1N2YwNDQwOTU4OTg3IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InppRzNEOXF0OUNXWFhEQVJ4WHVEYkE9PSIsInZhbHVlIjoiU0RYUEFLdG9hZ0RnN2xyMVV5VGsxNUtud1hCOFlVOGFuQ3JiWm1qdVlWRU90eHp0Zzdlclprbk9MaE1WdjJIMzZOVVpSaVRwNkxONm5LckUyQTBrZGZCSGgwbnpZK3llWC8wdXV2SCt6WG9xbVJuR3lkTjI0c0hXODNxbENqVzciLCJtYWMiOiJlMzQwMTk4MjVhOTZmZjBhNjBkYjQyMDgzZTRiMzU3OGQ3YTY0ZDc1ZWFiOTQ5YmYyMWVlMWFhZmY5ZWU5MjYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146078733\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-861102156 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6n7BHowcS6we7JFkDGDZRBjg4hsvpTWKVgnVcqpE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861102156\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1668369212 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 02:10:39 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668369212\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-335136999 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://recland.local/admin/jobs-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335136999\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/job/1648/edit", "action_name": "job.edit", "controller_action": "App\\Http\\Controllers\\Admin\\JobController@edit"}, "badge": null}}