<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CouponCode extends Model
{
    use HasFactory;

    protected $table = 'coupon_code';

    protected $fillable = [
        'code',
        'min_recruitment_cost',
        'default_recruitment_cost',
        'collaborator_cost',
        'max_cv'
    ];

    protected $casts = [
        'min_recruitment_cost' => 'integer',
        'default_recruitment_cost' => 'integer',
        'collaborator_cost' => 'integer',
        'max_cv' => 'integer'
    ];

    /**
     * Tìm mã khuyến mại theo code
     */
    public static function findByCode($code)
    {
        return self::where('code', $code)->first();
    }

    /**
     * Kiểm tra mã khuyến mại có hợp lệ không
     */
    public function isValid()
    {
        return !empty($this->code);
    }
}
