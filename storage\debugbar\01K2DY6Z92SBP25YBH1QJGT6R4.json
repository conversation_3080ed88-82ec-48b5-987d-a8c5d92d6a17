{"__meta": {"id": "01K2DY6Z92SBP25YBH1QJGT6R4", "datetime": "2025-08-12 08:27:13", "utime": **********.978384, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 8, "messages": [{"message": "[08:27:06] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.573484, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:06] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.574547, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local', '', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6Inh5dGVlenIvRU5NQnNkWmIyNlJOQVE9PSIsInZhbHVlIjoiRmJEU09vcUVLc2RsQnU0YlpQMlRlVXRaVFVON2pWQUFlQVRPZU1CbVF4QjU3RjVJL3pyNjF4cFBTUzZIVDRNVUMyc09LYld2N3ZyUm1VOTlkYUxMU2xUU1hFQjJ5bS9oRGQ3dXJBRmIxa0hnWmRLSW1USVY3QUNqNFJ4dGh1bjciLCJtYWMiOiI5ZmIwOTMyZTEyODUxMjczNTYzYjQzNWUxMzZmY2YyYTAyOGI0ZmQ5YTYyZWVmMzcyMWI2ZjcxZGRkZmMyZjAxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im04Y1phdDg0bDNQQkVZalp4MW5ySkE9PSIsInZhbHVlIjoiV2R5ZmpXLysxRzltYklQbXlWNUh1SkEwanB4NVR3aGd2b0w0ak1vTG1lbSszZnZLeFFlb2xJVVlNMzRJNFhnWWJjWHo4dDBQVDl4QjdTZTBrYlJMRmk3S0RYUEJzOHdJSGVuUjN1b2dmeTNTbEdleGQ2MUtGWW9UTlRNWWp3UzIiLCJtYWMiOiIxNzM0YjE0MTBmYWIyNzkzZjU3OTBlNGM3ZDI5ZjViZmYyMGM3YTMwYTZiMmVmOTY1NGQ1NTE3Y2ZhOGZjYzFiIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:06', '2025-08-12 08:27:06')\\n-- \",\n    \"Time:\": 22.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.014707, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:07] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'home' limit 1\\n-- \",\n    \"Time:\": 0.62\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.019228, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:13] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `testimonials` where `type` = '0' and `is_active` = '1'\\n-- \",\n    \"Time:\": 0.94\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.293986, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:13] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `posts` order by `id` desc limit 1\\n-- \",\n    \"Time:\": 1.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.399796, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:13] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `categories`\\n-- \",\n    \"Time:\": 0.69\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.725701, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:13] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.732448, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754962024.01245, "end": **********.978445, "duration": 9.965995073318481, "duration_str": "9.97s", "measures": [{"label": "Booting", "start": 1754962024.01245, "relative_start": 0, "end": **********.904168, "relative_end": **********.904168, "duration": 1.****************, "duration_str": "1.89s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.904221, "relative_start": 1.****************, "end": **********.978449, "relative_end": 4.0531158447265625e-06, "duration": 8.***************, "duration_str": "8.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.28108, "relative_start": 2.***************, "end": **********.515504, "relative_end": **********.515504, "duration": 0.*****************, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "59MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "1x frontend.pages.home.index", "param_count": null, "params": [], "start": **********.652134, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.home.index"}, {"name": "1x frontend.layouts.v2", "param_count": null, "params": [], "start": **********.723048, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.phpfrontend.layouts.v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fv2.blade.php&line=1", "ajax": false, "filename": "v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.v2"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.724019, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.v2.home_header", "param_count": null, "params": [], "start": **********.727426, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.phpfrontend.inc_layouts.v2.home_header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhome_header.blade.php&line=1", "ajax": false, "filename": "home_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.home_header"}, {"name": "1x frontend.inc_layouts.v2.hh_footer", "param_count": null, "params": [], "start": **********.766639, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/hh_footer.blade.phpfrontend.inc_layouts.v2.hh_footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fhh_footer.blade.php&line=1", "ajax": false, "filename": "hh_footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.hh_footer"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.767617, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.811407, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}, {"name": "1x frontend.inc_layouts.v2.contactus", "param_count": null, "params": [], "start": **********.812502, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/contactus.blade.phpfrontend.inc_layouts.v2.contactus", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fcontactus.blade.php&line=1", "ajax": false, "filename": "contactus.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.contactus"}]}, "route": {"uri": "GET /", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "home", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:50-67</a>"}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02726, "accumulated_duration_str": "27.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local', '', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6Inh5dGVlenIvRU5NQnNkWmIyNlJOQVE9PSIsInZhbHVlIjoiRmJEU09vcUVLc2RsQnU0YlpQMlRlVXRaVFVON2pWQUFlQVRPZU1CbVF4QjU3RjVJL3pyNjF4cFBTUzZIVDRNVUMyc09LYld2N3ZyUm1VOTlkYUxMU2xUU1hFQjJ5bS9oRGQ3dXJBRmIxa0hnWmRLSW1USVY3QUNqNFJ4dGh1bjciLCJtYWMiOiI5ZmIwOTMyZTEyODUxMjczNTYzYjQzNWUxMzZmY2YyYTAyOGI0ZmQ5YTYyZWVmMzcyMWI2ZjcxZGRkZmMyZjAxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im04Y1phdDg0bDNQQkVZalp4MW5ySkE9PSIsInZhbHVlIjoiV2R5ZmpXLysxRzltYklQbXlWNUh1SkEwanB4NVR3aGd2b0w0ak1vTG1lbSszZnZLeFFlb2xJVVlNMzRJNFhnWWJjWHo4dDBQVDl4QjdTZTBrYlJMRmk3S0RYUEJzOHdJSGVuUjN1b2dmeTNTbEdleGQ2MUtGWW9UTlRNWWp3UzIiLCJtYWMiOiIxNzM0YjE0MTBmYWIyNzkzZjU3OTBlNGM3ZDI5ZjViZmYyMGM3YTMwYTZiMmVmOTY1NGQ1NTE3Y2ZhOGZjYzFiIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:06', '2025-08-12 08:27:06')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local", null, "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6Inh5dGVlenIvRU5NQnNkWmIyNlJOQVE9PSIsInZhbHVlIjoiRmJEU09vcUVLc2RsQnU0YlpQMlRlVXRaVFVON2pWQUFlQVRPZU1CbVF4QjU3RjVJL3pyNjF4cFBTUzZIVDRNVUMyc09LYld2N3ZyUm1VOTlkYUxMU2xUU1hFQjJ5bS9oRGQ3dXJBRmIxa0hnWmRLSW1USVY3QUNqNFJ4dGh1bjciLCJtYWMiOiI5ZmIwOTMyZTEyODUxMjczNTYzYjQzNWUxMzZmY2YyYTAyOGI0ZmQ5YTYyZWVmMzcyMWI2ZjcxZGRkZmMyZjAxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im04Y1phdDg0bDNQQkVZalp4MW5ySkE9PSIsInZhbHVlIjoiV2R5ZmpXLysxRzltYklQbXlWNUh1SkEwanB4NVR3aGd2b0w0ak1vTG1lbSszZnZLeFFlb2xJVVlNMzRJNFhnWWJjWHo4dDBQVDl4QjdTZTBrYlJMRmk3S0RYUEJzOHdJSGVuUjN1b2dmeTNTbEdleGQ2MUtGWW9UTlRNWWp3UzIiLCJtYWMiOiIxNzM0YjE0MTBmYWIyNzkzZjU3OTBlNGM3ZDI5ZjViZmYyMGM3YTMwYTZiMmVmOTY1NGQ1NTE3Y2ZhOGZjYzFiIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:27:06", "2025-08-12 08:27:06"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.992232, "duration": 0.02278, "duration_str": "22.78ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 83.566}, {"sql": "select * from `seos` where `key` = 'home' limit 1", "type": "query", "params": [], "bindings": ["home"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0186722, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "4fc5d5cd92e1edd22931af0dfab11a79cf14ebae41ccdc8e68a778258c2710a5"}, "start_percent": 83.566, "width_percent": 2.274}, {"sql": "select * from `testimonials` where `type` = 0 and `is_active` = 1", "type": "query", "params": [], "bindings": [0, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/TestimonialService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\TestimonialService.php", "line": 20}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2936099, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "TestimonialRepository.php:54", "source": {"index": 14, "namespace": null, "name": "app/Repositories/TestimonialRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\TestimonialRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FTestimonialRepository.php&line=54", "ajax": false, "filename": "TestimonialRepository.php", "line": "54"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `testimonials` where `type` = ? and `is_active` = ?", "hash": "381c6702a1846dfebb02526700a260eb3c240f7b2e71910852e1c5484a87aeff"}, "start_percent": 85.84, "width_percent": 3.448}, {"sql": "select * from `posts` order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\PostRepository.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/PostService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\PostService.php", "line": 73}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.398398, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "PostRepository.php:207", "source": {"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\PostRepository.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FPostRepository.php&line=207", "ajax": false, "filename": "PostRepository.php", "line": "207"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `posts` order by `id` desc limit 1", "hash": "03c71db9530baa80c7f279b6ce87376e4f31e539a6b4c82a75ace86a94fe60b2"}, "start_percent": 89.288, "width_percent": 5.723}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 176}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}, {"index": 21, "namespace": "view", "name": "frontend.layouts.v2", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/v2.blade.php", "line": 46}], "start": **********.725086, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:58", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=58", "ajax": false, "filename": "AppServiceProvider.php", "line": "58"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `categories`", "hash": "7e13c5da69976ceecb727777c5a91eea3e71bf2b1b50247c4cb202cd9a066582"}, "start_percent": 95.011, "width_percent": 2.531}, {"sql": "select * from `users` where `id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": "view", "name": "frontend.inc_layouts.v2.home_header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/home_header.blade.php", "line": 8}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.731848, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "f57aeccc1e0beb29028e5e2a4241e4eed6d22790637d6f4352f9bde1e64d2f30"}, "start_percent": 97.542, "width_percent": 2.458}]}, "models": {"data": {"App\\Models\\Category": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Testimonial": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "App\\Models\\Post": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 12, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 11}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/job/detail/nhan-vien-cua-hang-saint-honore-tp31NhMS?lang=vi\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "512", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:50-67</a>", "middleware": "web, localization, visit-website", "duration": "10.05s", "peak_memory": "62MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-156249063 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-156249063\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2074413563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2074413563\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1936152237 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6Inh5dGVlenIvRU5NQnNkWmIyNlJOQVE9PSIsInZhbHVlIjoiRmJEU09vcUVLc2RsQnU0YlpQMlRlVXRaVFVON2pWQUFlQVRPZU1CbVF4QjU3RjVJL3pyNjF4cFBTUzZIVDRNVUMyc09LYld2N3ZyUm1VOTlkYUxMU2xUU1hFQjJ5bS9oRGQ3dXJBRmIxa0hnWmRLSW1USVY3QUNqNFJ4dGh1bjciLCJtYWMiOiI5ZmIwOTMyZTEyODUxMjczNTYzYjQzNWUxMzZmY2YyYTAyOGI0ZmQ5YTYyZWVmMzcyMWI2ZjcxZGRkZmMyZjAxIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6Im04Y1phdDg0bDNQQkVZalp4MW5ySkE9PSIsInZhbHVlIjoiV2R5ZmpXLysxRzltYklQbXlWNUh1SkEwanB4NVR3aGd2b0w0ak1vTG1lbSszZnZLeFFlb2xJVVlNMzRJNFhnWWJjWHo4dDBQVDl4QjdTZTBrYlJMRmk3S0RYUEJzOHdJSGVuUjN1b2dmeTNTbEdleGQ2MUtGWW9UTlRNWWp3UzIiLCJtYWMiOiIxNzM0YjE0MTBmYWIyNzkzZjU3OTBlNGM3ZDI5ZjViZmYyMGM3YTMwYTZiMmVmOTY1NGQ1NTE3Y2ZhOGZjYzFiIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936152237\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1305678718 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OLDKqoGpbczGmQ3iONEKK3dsTzLGGTm8QWy0Jh3e</span>\"\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305678718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1206055712 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:27:13 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206055712\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1806994806 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://recland.local/job/detail/nhan-vien-cua-hang-saint-honore-tp31NhMS?lang=vi</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>512</span>\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806994806\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}