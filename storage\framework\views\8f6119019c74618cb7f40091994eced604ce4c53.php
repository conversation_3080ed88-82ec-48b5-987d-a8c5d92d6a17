
<?php $__env->startSection('css_custom'); ?>
<link rel="stylesheet" href="<?php echo e(asset2('frontend/asset/css/wallet.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset2('frontend/asset/css/modal-filter.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset2('frontend/asset/css/common.css')); ?>">
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content-collaborator'); ?>

<div class="base-layout-edit">
    <div class="header-layout-edit">
        <div class="row">
            <div class="col-md-4 col-6">
                <a class="st1" href="<?php echo e(route('employer-job')); ?>"><img
                        src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/arrow-back.svg')); ?>">
                    <?php echo e(ucfirst(mb_strtolower($arrLang['danhsachcongviec']))); ?>

                </a>
            </div>
            <div class="col-md-4  col-6 title-header-layout-edit">
                <a class="st2" href="javascript:void(0)"><?php echo e($arrLang['themmoicongviec']); ?></a>
            </div>
            <div class="col-md-4"></div>
        </div>
    </div>


    <div class="main-form">
        
        <form action="<?php echo e(route('employer-store')); ?>" id="form-job" method="post" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row">
                <?php if(config('app.enable_coupon_code', true)): ?>
                
                <div class="col-md-12 col-form-item">
                    <div class="form-item">
                        <label>Mã khuyến mại</label>
                        <input type="text" id="coupon-code" name="coupon_code"
                            placeholder="Nhập mã khuyến mại (tùy chọn)" class="form-control">
                        <div id="coupon-message" class="mt-2"
                            style="display: none;    font-size: 12px; line-height: 20px;"></div>
                        <input type="hidden" id="collaborator-cost" name="collaborator_cost">
                    </div>
                </div>
                <?php endif; ?>

                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['tenvieclam']); ?> <span>*</span></label>
                        <input type="text" placeholder="<?php echo e($arrLang['input']); ?> <?php echo e(lcfirst($arrLang['tenvieclam'])); ?>"
                            name="name">
                    </div>
                </div>
                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['hannop']); ?> <span>*</span></label>
                        <input class="fc-datepicker" type="text" placeholder="DD/MM/YYYY" name="expire_at">
                    </div>
                </div>
                
                    
                        
                        
                            
                        
                    
                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['soluongtuyen']); ?> <span>*</span></label>
                        <input type="text" placeholder="<?php echo e($arrLang['input']); ?> <?php echo e(lcfirst($arrLang['soluongtuyen'])); ?>"
                            name="vacancies">
                    </div>
                </div>

                
                    
                        
                        
                            
                        
                    


                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['linhvuc']); ?> <span>*</span></label>
                        <div class="multiple-select2">
                            <select id="career-select" style="width:100%;display: none"
                                class="select2-custom select-max-item" name="career">
                                <?php $__currentLoopData = $career; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($k); ?>"><?php echo e($item); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['kinang']); ?> <span>*</span></label>
                        <div class="multiple-select2">
                            <select id="skill_id" style="width:100%;display: none"
                                class="select2-custom-tags select-max-item" name="skill">
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['capbac']); ?> <span>*</span></label>
                        <div class="multiple-select2">
                            <select id="rank-select" style="width:100%;display: none"
                                class="select2-custom select-max-item" name="rank">
                                
                            </select>
                        </div>
                    </div>
                </div>

                

                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['salarymin']); ?> <span>*</span> </label>
                        <div class="input-unit">
                            <input data-toggle="current_mask" data-target="#field_salary_min" type="text"
                                class="field-input-unit"
                                placeholder="<?php echo e($arrLang['input']); ?> <?php echo e(lcfirst($arrLang['salarymin'])); ?>">
                            <select style="display: none" class="select2-unit select2-hide-input salary_currency"
                                name="salary_currency">
                                <?php $__currentLoopData = $currency; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($item); ?>"><?php echo e($item); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <input id="field_salary_min" class="field-input-unit" type="hidden" value=""
                                name="salary_min">
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['salarymax']); ?> <span>*</span> </label>
                        <div class="input-unit">
                            <input data-toggle="current_mask" data-target="#field_salary_max" type="text"
                                class="field-input-unit"
                                placeholder="<?php echo e($arrLang['input']); ?> <?php echo e(lcfirst($arrLang['salarymax'])); ?>">
                            <select style="display: none;" class="select2-unit select2-hide-input salary_currency_max"
                                name="salary_currency_max" id="salary_currency_max">
                                <?php $__currentLoopData = $currency; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($item); ?>"><?php echo e($item); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <input id="field_salary_max" class="field-input-unit" type="hidden" value=""
                                name="salary_max">
                        </div>
                    </div>
                </div>


                <div class="col-md-4 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng'); ?> <span>*</span></label>
                        <select id="recruitment-type" style="width:100%;display: none" class="" name="bonus_type">
                            
                            <option value="">
                                <?php echo e(config('settings.' . app()->getLocale() . '.rec_cv_selling.chonhinhthuc')); ?>

                            </option>
                            <?php $__currentLoopData = $bonusTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bonusType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($bonusType); ?>"><?php echo e($bonusType); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-4 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['chiphituyendung'] ?? 'Chi phí cho 1 lần tuyển dụng'); ?>

                            <span>*</span></label>
                        <input type="number" id="bonus" name="bonus" class="form-control">
                        <label style="font-weight: normal; font-size: 14px;" id="bonus_msg"
                            class="form-text text-muted mt-2 d-block">Giá tối thiểu là <span id="min_price"></span>
                            vnđ</label>
                    </div>
                </div>

                <div class="col-md-4 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['tongtien'] ?? 'Tổng chi phí'); ?></label>
                        <input type="text" id="total-cost" readonly class="form-control">
                    </div>
                </div>

                <div class="col-md-6 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['hinhthuc']); ?> <span>*</span></label>
                        <select id="type-select" style="width:100%;display: none" class="select2-custom" name="type">
                            <option label="--- Chọn ---"></option>
                            <?php $__currentLoopData = $type; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($k); ?>"><?php echo e($item); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>

                


                <div class="col-md-6 col-form-item">
                    <div class="row pt-4">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-item-switch">
                                        <div class="checkbox-remote">
                                            <label class="switch">
                                                <input type="checkbox" name="remote">
                                                <span class="slider round"></span>
                                            </label>
                                            <?php echo e($arrLang['remote']); ?>

                                        </div>
                                    </div>

                                </div>
                                <div class="col-md-6">
                                    <div class="form-item-switch">
                                        <div class="checkbox-remote">
                                            <label class="switch">
                                                <input type="checkbox" name="urgent">
                                                <span class="slider round"></span>
                                            </label>
                                            <?php echo e($arrLang['urgent']); ?>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-md-6 col-form-item">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-item">
                                        <label><?php echo e($arrLang['diadiem']); ?> <span>*</span>
                                            <a data-toggle="add-item" href="javascript:void(0)">
                                                <img
                                                    src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/icon-add-item.svg')); ?>">
                                            </a></label>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="form-item">
                                        <label><?php echo e($arrLang['diachicuthe']); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="group-field-repeater group-field-repeater-out-row">
                                <?php
                                    $user = auth('client')->user();
                                    ?>
                                <?php if(isset($user->company)): ?>
                                <?php $__currentLoopData = $user->company->address_value; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($value->area): ?>
                                <div class="row item render-address">
                                    <div class="col-md-3">
                                        <div class="form-item">
                                            <select style="width: 100%;display: none"
                                                class="select2-custom area<?php echo e($key); ?> address-select"
                                                name="address[<?php echo e($key); ?>][area]">
                                                <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($k); ?>" <?php echo e($k==$value->area ? 'selected' : ''); ?>>
                                                    <?php echo e($city); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-md-11 col-form-item">
                                                <div class="form-item">
                                                    <input type="text" class="address<?php echo e($key); ?>"
                                                        value="<?php echo e($value->address); ?>"
                                                        placeholder="<?php echo e($arrLang['diachicuthe']); ?>"
                                                        name="address[<?php echo e($key); ?>][address]">
                                                </div>
                                            </div>
                                            <?php if($key != 0): ?>
                                            <div class="col-md-1 col-remove-btn">
                                                <a href="javascript:void(0)" data-toggle="removeitem">
                                                    <img
                                                        src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/icon-remove.svg')); ?>">
                                                </a>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                <div class="row item render-address">
                                    <div class="col-md-3">
                                        <div class="form-item">
                                            <select style="width: 100%" class="select2-custom area0 address-select"
                                                name="address[0][area]">
                                                <option value=""></option>
                                                <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($k); ?>"><?php echo e($city); ?>

                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-md-11 col-form-item">
                                                <div class="form-item">
                                                    <input type="text" class="address0"
                                                        placeholder="<?php echo e($arrLang['diachicuthe']); ?>"
                                                        name="address[0][address]">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="item-clone" style="display: none">
                        <div class="row item render-address">
                            <div class="col-md-3">
                                <div class="form-item">
                                    <select style="width: 100%" class="select-clone">
                                        <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($k); ?>"><?php echo e($city); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-11 col-form-item">
                                        <div class="form-item">
                                            <input type="text" placeholder="<?php echo e($arrLang['diachicuthe']); ?>"
                                                name="address[]">
                                        </div>
                                    </div>
                                    <div class="col-md-1 col-remove-btn">
                                        <a href="javascript:void(0)" data-toggle="removeitem">
                                            <img
                                                src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/icon-remove.svg')); ?>">
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['jdmota']); ?> <span>*</span></label>
                        <div class="ck-custom">
                            <textarea class="field-ck" id="jd_description" rows="10" cols="80"
                                name="jd_description"></textarea>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['jdyeucau']); ?> <span>*</span></label>
                        <div class="ck-custom">
                            <textarea class="field-ck" id="jd_request" rows="10" cols="80" name="jd_request"></textarea>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-form-item">
                    <div class="form-item">
                        <label><?php echo e($arrLang['jdphucloi']); ?> <span>*</span></label>
                        <div class="ck-custom">
                            <textarea class="field-ck" id="jd_welfare" rows="10" cols="80" name="jd_welfare"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="col-12">
            <div class="group-btn">
                <a class="btn btn-reload" href="javascript:void(0)"><?php echo e($arrLang['datlai']); ?></a>
                <button class="btn btn-save"><?php echo e($arrLang['luu']); ?></button>
            </div>
        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset2('frontend/asset/js/additional-methods.min.js')); ?>"></script>
<script src="<?php echo e(asset2('frontend/asset/ckeditor/ckeditor.js')); ?>"></script>
<script>
    let min_price = 0;
        let couponApplied = false;
        jQuery.validator.addMethod("ckrequired", function(value, element) {
            var idname = $(element).attr('id');
            var editor = CKEDITOR.instances[idname];
            var ckValue = GetTextFromHtml(editor.getData()).replace(/<[^>]*>/gi, '').trim();
            if (ckValue.length === 0) {
                $(element).val(ckValue);
            } else {
                $(element).val(editor.getData());
            }
            return $(element).val().length > 0;
        }, "This field is required");

        function GetTextFromHtml(html) {
            var dv = document.createElement("DIV");
            dv.innerHTML = html;
            return dv.textContent || dv.innerText || "";
        }


        $(document).ready(function() {

            // Xử lý mã khuyến mại
            let couponApplied = false;
            let bonus_type_default = '';
            <?php if(config('app.enable_coupon_code', true)): ?>
            // Thêm validation khi người dùng thay đổi input
            $('#coupon-code').on('input', function() {
                let code = $(this).val().trim();

                // Nếu người dùng thay đổi mã sau khi đã áp dụng thành công
                if (couponApplied && code !== $(this).data('applied-code')) {
                    resetCouponState();
                    $('#coupon-message')
                        .removeClass('text-success')
                        .addClass('text-warning')
                        .text('Mã khuyến mại đã thay đổi. Vui lòng nhấn Tab hoặc click ra ngoài để kiểm tra lại.');
                }
            });

            $('#coupon-code').on('blur', function() {
                let code = $(this).val().trim();

                if (code === '') {
                    resetCouponState();
                    return;
                }

                // Gọi AJAX kiểm tra mã khuyến mại
                $.ajax({
                    url: '<?php echo e(route('employer-check-coupon')); ?>',
                    type: 'POST',
                    data: {
                        code: code,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            applyCoupon(response.data, response.message);
                        } else {
                            showCouponError(response.message);
                        }
                    },
                    error: function() {
                        showCouponError('Có lỗi xảy ra khi kiểm tra mã khuyến mại');
                    }
                });
            });

            function applyCoupon(data, message) {
                couponApplied = true;

                // Lưu mã đã áp dụng để so sánh khi thay đổi
                $('#coupon-code').data('applied-code', $('#coupon-code').val().trim());

                // Hiển thị thông báo thành công
                $('#coupon-message')
                    .removeClass('text-danger text-warning')
                    .addClass('text-success')
                    .html(message + '<br><small class="text-info">Khi sử dụng mã khuyến mại, hình thức tuyển dụng sẽ được cố định là CV</small>')
                    .show();

                // Điền giá trị vào các input
                $('#bonus').val(data.default_recruitment_cost).prop('readonly', true);
                $('#collaborator-cost').val(data.collaborator_cost);

                // Ràng buộc hình thức tuyển dụng thành CV
                bonus_type_default = 'cv';
                $('#recruitment-type').val('cv').trigger('change').prop('disabled', true);

                // Cập nhật thông báo giá tối thiểu
                $('#min_price').text(formatCurrency(data.min_recruitment_cost));
                $('#bonus_msg').text('Giá tối thiểu là ' + formatCurrency(data.min_recruitment_cost) + ' vnđ');
                min_price = data.min_recruitment_cost;
                // Tính lại tổng tiền
                calculateTotalCost();
            }

            function showCouponError(message) {
                couponApplied = false;

                $('#coupon-message')
                    .removeClass('text-success')
                    .addClass('text-danger')
                    .text(message);
                $('#coupon-message').css('display', 'block');
                $('#coupon-message').show();

                resetCouponState();
            }

            function resetCouponState() {
                couponApplied = false;

                // Xóa dữ liệu mã đã áp dụng
                $('#coupon-code').removeData('applied-code');

                // $('#coupon-message').hide();
                $('#bonus').prop('readonly', false);
                $('#collaborator-cost').val('');

                // Enable lại select hình thức tuyển dụng
                $('#recruitment-type').prop('disabled', false);
                bonus_type_default = '';
                getMinPrice();
            }
            <?php endif; ?>

            function getSkillIT(career = "") {
                $('.skill-main').select2({
                    dropdownParent: "#modal-sell-cv",
                    // tags: true,
                    placeholder: '<?php echo e(config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang')); ?>',
                    allowClear: true,
                    ajax: {
                        url: '<?php echo e(route('ajax-get-skill-main-it')); ?>' + '?career=' + career,
                        type: "get",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                    },
                })
            }

            function getMinPrice() {
                if (couponApplied) {
                    return;
                }
                $('#bonus').prop('disabled', true);
                // if ($('#skill_id').select2('data')[0]) {
                // let group = $('#skill_id').select2('data')[0].group;
                // let isIT = $('#skill_id').select2('data')[0].is_it;
                // }
                if (typeof group == 'undefined' || !group) group = '';
                if (typeof isIT == 'undefined' || !isIT) isIT = '';

                console.log('group: ' + group, 'isIT: ' + isIT);

                let level = $('#rank-select').val();
                let skill_id = $('#skill_id').val();
                let bonus_type = $('#recruitment-type').val();
                let career = $('#career-select').val();
                let salary_min = $('#field_salary_min').val();
                let salary_max = $('#field_salary_max').val();
                let salary_currency = $('.salary_currency').val();

                if (bonus_type == 'onboard' && salary_max == '') {
                    $('#bonus_msg').html(
                        'Vui lòng nhập lương tối thiểu & hình thức tuyển dụng để tính chi phí tối thiểu');
                    return;
                }
                if (!level || !bonus_type || !skill_id) {
                    $('#bonus_msg').html('Vui lòng chọn cấp bậc, hình thức tuyển dụng và kỹ năng chính để xem giá');
                    return;
                }


                console.log('level: ' + level, 'bonus_type: ' + bonus_type, 'career: ' + career, 'isIT: ' + isIT,
                    'group: ' + group);
                $.ajax({
                    url: '<?php echo e(route('ajax-get-min-submit-price')); ?>' + '?group=' + group + '&is_it=' +
                        isIT + '&bonus_type=' + bonus_type + '&level=' + level + '&career=' + career +
                        '&salary_min=' + salary_min + '&salary_max=' + salary_max + '&salary_currency=' + salary_currency + '&skill_id=' +
                        skill_id,
                    type: "get",
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#bonus').prop('disabled', false);
                        min_price = res.data.min_price;
                        $('#min_price').html(formatCurrency(min_price));
                        $('#bonus_msg').html('Giá tối thiểu là ' + formatCurrency(min_price) + ' vnđ');
                        // if (res) {
                        //     if (type == 'cv') {
                        //         $('.min-price').html(formatCurrency(res.price_cv_min))
                        //         $('.max-price').html(formatCurrency(res.price_cv_max))
                        //         $("input[name='min-price']").val(res.price_cv_min);
                        //         $("input[name='max-price']").val(res.price_cv_max);
                        //     } else if (type == 'interview') {
                        //         $('.min-price').html(formatCurrency(res.price_interview_min))
                        //         $('.max-price').html(formatCurrency(res.price_interview_max))
                        //         $("input[name='min-price']").val(res.price_interview_min);
                        //         $("input[name='max-price']").val(res.price_interview_max);
                        //     }
                        // }
                    }
                })
            }

            function getSkillIT(career = "") {

                let url = '<?php echo e(route('ajax-get-skill-main-it')); ?>?career=' + career;
                $("#skill_id").select2({
                    tags: true,
                    placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['kinang'])); ?>',
                    allowClear: true,
                    maximumSelectionLength: 3,
                    ajax: {
                        url: url,
                        type: "get",
                        dataType: 'json',
                        delay: 550,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                        cache: true
                    }
                });
                // $('#skill_id').select2({
                //     // dropdownParent: "#modal-sell-cv",
                //     // tags: true,
                //     placeholder: '<?php echo e(config('settings.' . app()->getLocale() . '.rec_cv_selling.chonkynang')); ?>',
                //     allowClear: true,
                //     ajax: {
                //         url: '<?php echo e(route('ajax-get-skill-main-it')); ?>' + '?career=' + career,
                //         type: "get",
                //         dataType: 'json',
                //         headers: {
                //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                //         },
                //         data: function (params) {
                //             return {
                //                 searchTerm: params.term // search term
                //             };
                //         },
                //         processResults: function (response) {
                //             return {
                //                 results: $.map(response, function (item) {
                //                     return {
                //                         ...item,
                //                         text: item.name_en
                //                     }
                //                 })
                //             };
                //         },
                //     },
                // })
            }

            $('#career-select').change(function() {
                let career = $(this).val();
                $("#skill_id").empty();
                getSkillIT(career);
            });

            $('#rank-select').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#recruitment-type').change(function(e) {
                if ($(this).val()) {
                    getMinPrice();
                }
            })
            $('#skill_id').change(function(e) {
                if ($(this).val()) {
                    $('#rank-select').val(null).trigger('change');
                    if (couponApplied && bonus_type_default != '') {
                        $('#recruitment-type').val(bonus_type_default).trigger('change').prop('disabled', true);
                    } else {
                        $('#recruitment-type').val(null).trigger('change');
                    }
                    let group = $(this).select2('data')[0].group;
                    let isIT = $(this).select2('data')[0].is_it;
                    getLevel({
                        group,
                        isIT
                    });
                }
            })

            function getLevel({
                group,
                isIT
            }) {
                $('#rank-select').select2({
                    // dropdownParent: "#modal-sell-cv",
                    // tags: true,
                    placeholder: '<?php echo e(config('settings.' . app()->getLocale() . '.rec_cv_selling.chonlevel')); ?>',
                    allowClear: true,
                    ajax: {
                        url: '<?php echo e(route('ajax-get-level')); ?>' + '?group=' + group + '&is_it=' + isIT,
                        type: "get",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: function(params) {
                            return {
                                searchTerm: params.term // search term
                            };
                        },
                        processResults: function(response) {
                            return {
                                results: $.map(response, function(item) {
                                    return {
                                        ...item,
                                        text: item.name_en
                                    }
                                })
                            };
                        },
                    },
                })
            }
            $(".select2-custom-tags").select2({
                tags: true,
                placeholder: '<?php echo e($arrLang['input']); ?> <?php echo e($arrLang['kinang']); ?>',
                allowClear: true
            });
            // Khởi tạo select2 cho trường hình thức tuyển dụng
            $('#recruitment-type').select2({
                placeholder: '<?php echo e($arrLang['chon'] ?? 'Chọn'); ?> <?php echo e(lcfirst($arrLang['hinhthuctuyendung'] ?? 'Hình thức tuyển dụng')); ?>',
                allowClear: true,
                minimumResultsForSearch: -1,
                // dropdownParent: "#modal-sell-cv",
                // dropdownCssClass: 'customer-dropdown-select2'
            });
            // Tính tổng tiền khi thay đổi chi phí hoặc số lượng tuyển
            $('#bonus, input[name="vacancies"]').on('input', function() {
                calculateTotalCost();
            });

            function calculateTotalCost() {
                var cost = parseFloat($('#bonus').val()) || 0;
                var vacancies = parseInt($('input[name="vacancies"]').val()) || 0;
                var total = cost * vacancies;
                $('#total-cost').val(total.toLocaleString('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }));
            }

            $('[data-toggle="add-item"]').click(function() {
                // let html = $('.item-clone').html();
                let countHtmlAddress = $('.render-address').length;
                if (countHtmlAddress <= 3) {
                    let cities = '<?php echo json_encode($cities); ?>';
                    cities = JSON.parse(cities);
                    let options;
                    $.each(cities, function(key, value) {
                        options = options + '<option value="' + key + '">' + value + '</option>';
                    });
                    let html = '';
                    html += '<div class="row item render-address">'
                    html +=
                        '<div class="col-md-3"><div class="form-item"><select style="width: 100%" class="address-select select-clone area' +
                        (countHtmlAddress - 1) + '" name="address[' + (countHtmlAddress - 1) + '][area]">'
                    html += options
                    html += '</select></div></div>'
                    html +=
                        '<div class="col-md-9"><div class="row"><div class="col-md-11 col-form-item"><div class="form-item">'
                    html += '<input type="text" placeholder="Địa chỉ cụ thể" name="address[' + (
                            countHtmlAddress - 1) + '][address]" class="address' + (countHtmlAddress - 1) +
                        '">'
                    html += '</div></div><div class="col-md-1 col-remove-btn">'
                    html += '<a href="javascript:void(0)" data-toggle="removeitem">'
                    html +=
                        '<img src="<?php echo e(asset2('frontend/asset/images/dashboard-ctv/icon-remove.svg')); ?>">'
                    html += '</a></div></div></div>'


                    $('.group-field-repeater').append(html);
                    $('.group-field-repeater .select-clone').select2();
                }
            })
            $('body').on('click', '[data-toggle="removeitem"]', function() {
                $(this).parents('.item').remove();
            })
            CKEDITOR.replace('jd_description');
            CKEDITOR.replace('jd_request');
            CKEDITOR.replace('jd_welfare');

            var dateToday = new Date();
            $(".fc-datepicker").datepicker({
                showOtherMonths: !0,
                selectOtherMonths: !0,
                dateFormat: 'dd/mm/yy',
                minDate: dateToday,
            });

            let urlCompany = '<?php echo e(route('ajax-list-company')); ?>';
            $("#company_id").select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['congty'])); ?>',
                allowClear: true,
                ajax: {
                    url: urlCompany,
                    type: "get",
                    dataType: 'json',
                    delay: 250,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(params) {
                        return {
                            searchTerm: params.term // search term
                        };
                    },
                    processResults: function(response) {
                        return {
                            results: response
                        };
                    },
                    cache: true
                }
            });

            $('#company_id').change(function() {
                let id = $(this).val();
                let url = '<?php echo e(route('ajax-detail-company', ':id')); ?>';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'get',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        let companyObj = res[0];
                        let listEmployer = res[1];
                        let arrAddress = JSON.parse(companyObj.address);
                        $.each(arrAddress, function(key, value) {
                            if (key == 0) {

                            } else if (value['area']) {
                                $('[data-toggle="add-item"]').trigger('click');
                            }
                            $('.area' + key).val(value.area).trigger('change');
                            $('.address' + key).val(value.address);
                        });

                        var output = [];
                        $.each(listEmployer, function(key, value) {
                            output.push('<option value="' + value.id + '">' + value
                                .name + '</option>');
                        });
                        $('#employer-select').html(output.join(''));
                    }
                });
            });

            $("#form-job").validate({
                ignore: [],
                debug: false,
                rules: {
                    name: {
                        required: true,
                    },
                    expire_at: {
                        required: true,
                    },
                    company_id: {
                        required: true,
                    },
                    vacancies: {
                        required: true,
                        number: true,
                        digits: true
                    },
                    employer_id: {
                        required: true,
                    },
                    career: {
                        required: true,
                    },
                    skill: {
                        required: true,
                    },
                    type: {
                        required: true,
                    },
                    rank: {
                        required: true,
                    },
                    salary_min: {
                        required: true,
                        number: true,
                    },
                    salary_max: {
                        required: true,
                        number: true,
                    },
                    file_jd: {
                        // required: true,
                        extension: "pdf,docx",
                        filesize: 5, // <- 5 MB
                    },
                    jd_description: {
                        ckrequired: true,
                    },
                    jd_request: {
                        ckrequired: true,
                    },
                    jd_welfare: {
                        ckrequired: true,
                    },
                    "address[0][area]": {
                        required: true
                    },
                    "address[0][address]": {
                        required: true
                    },
                    bonus_type: {
                        required: true
                    },
                    bonus: {
                        required: true,
                        min: min_price
                    }
                },
                messages: {
                    name: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    expire_at: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    company_id: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    vacancies: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        number: '<?php echo e(__('frontend/validation.number')); ?>',
                        digits: '<?php echo e(__('frontend/validation.number')); ?>',
                    },
                    employer_id: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    career: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    skill: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    type: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    rank: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    salary_min: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        number: '<?php echo e(__('frontend/validation.number')); ?>',
                    },
                    salary_max: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        number: '<?php echo e(__('frontend/validation.number')); ?>',
                    },
                    file_jd: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        extension: '<?php echo e(__('frontend/validation.format_cv')); ?>',
                        filesize: '<?php echo e(__('frontend/validation.file_max')); ?>',
                    },
                    jd_description: {
                        ckrequired: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    jd_request: {
                        ckrequired: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    jd_welfare: {
                        ckrequired: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    "address[0][area]": {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    "address[0][address]": {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                    },
                    bonus_type: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>'
                    },
                    bonus: {
                        required: '<?php echo e(__('frontend/validation.required')); ?>',
                        min: 'Giá trị tối thiểu là ' + min_price + ' vnđ'
                    }

                },

                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else if (elem.hasClass("field-input-unit")) {
                        elem.parent().addClass(errorClass);
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        console.log(errorClass);
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    console.log(elem);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-input-unit")) {
                        element = elem.parent();
                        error.insertAfter(element);
                    } else if (elem.hasClass("file-browserinput")) {
                        element = elem.parents('.input-file');
                        error.insertAfter(element);
                    } else if (elem.hasClass("field-ck")) {
                        element = elem.parents('.ck-custom');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            $.validator.addMethod('regex', function(value) {
                var regex = /^[0-9()+.-]*$/;
                return value.trim().match(regex);
            });

            $.validator.addMethod('filesize', function(value, element, param) {
                return this.optional(element) || (element.files[0].size <= param * 1000000)
            }, 'File size must be less than {0} MB');

            $(document).on('click', '.btn-save', function() {
                $('#bonus_msg').removeClass('error');
                let bonus = $('#bonus').val();
                // alert(min_price);
                if (bonus < min_price) {
                    $('#bonus').focus();
                    $('#bonus_msg').addClass('error');
                    return;
                }

                // Kiểm tra mã khuyến mại trước khi submit
                <?php if(config('app.enable_coupon_code', true)): ?>
                let couponCode = $('#coupon-code').val().trim();
                if (couponCode !== '' && !couponApplied) {
                    alert('Mã khuyến mại không hợp lệ. Vui lòng kiểm tra lại hoặc để trống nếu không sử dụng.');
                    $('#coupon-code').focus();
                    return;
                }

                // Nếu mã khuyến mại không hợp lệ, xóa giá trị trước khi submit
                if (couponCode !== '' && !couponApplied) {
                    $('#coupon-code').val('');
                }
                <?php endif; ?>

                if ($("#form-job").valid()) {
                    $('#form-job').submit();
                }
            });

            $('#type-select').select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['hinhthuc'])); ?>',
                allowClear: true
            });

            $('#career-select').select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['linhvuc'])); ?>',
                allowClear: true,
                maximumSelectionLength: 3
            });

            $('#rank-select').select2({
                placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['capbac'])); ?>',
                allowClear: true,
                maximumSelectionLength: 3
            });

            
            
            
            

            $('.address-select').select2({
                placeholder: '<?php echo e(lcfirst($arrLang['diadiem'])); ?>',
                allowClear: true
            });

            // let url = '<?php echo e(route('ajax-list-skill')); ?>';
            // $("#skill_id").select2({
            //     tags: true,
            //     placeholder: '<?php echo e($arrLang['chon']); ?> <?php echo e(lcfirst($arrLang['kinang'])); ?>',
            //     allowClear: true,
            //     maximumSelectionLength: 3,
            //     ajax: {
            //         url: url,
            //         type: "get",
            //         dataType: 'json',
            //         delay: 550,
            //         headers: {
            //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //         },
            //         data: function(params) {
            //             return {
            //                 searchTerm: params.term // search term
            //             };
            //         },
            //         processResults: function(response) {
            //             return {
            //                 results: response
            //             };
            //         },
            //         cache: true
            //     }
            // });

            $('.salary_currency').change(function() {
                let val = $(this).val();
                $('.salary_currency_max').val(val).trigger('change');
            });

            $('.salary_currency_max').change(function() {
                console.log(1111);
                // let val = $(this).val();
                // $('.salary_currency_max').val(val).trigger('change');
            });

            $('#field_salary_min').change(function() {
                $('#bonus').val('');
            });

            $('.btn-reload').click(function() {
                location.reload();
            })
        })
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('frontend.layouts.employer.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Projects\HRI\RecLand\resources\views/frontend/pages/employer/create.blade.php ENDPATH**/ ?>