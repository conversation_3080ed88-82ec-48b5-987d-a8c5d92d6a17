{"__meta": {"id": "01K2DY9NB0X1P3GN28GD0F9GGZ", "datetime": "2025-08-12 08:28:42", "utime": **********.082261, "method": "POST", "uri": "/employer/job/check-coupon", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 11, "messages": [{"message": "[08:28:41] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.913006, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:41] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.913341, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:41] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-check-coupon' limit 1\\n-- \",\n    \"Time:\": 20.24\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.963644, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"code\\\":\\\"NEWBIE2025\\\",\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\"}', 'http:\\/\\/recland.local\\/employer\\/job\\/check-coupon', 'http:\\/\\/recland.local\\/employer\\/job\\/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"63\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"*\\\\\\/*\\\"],\\\"content-type\\\":[\\\"application\\\\\\/x-www-form-urlencoded; charset=UTF-8\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/job\\\\\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6Im1YQUlzejYxb0kwd3U4ZEhDZ3dsS3c9PSIsInZhbHVlIjoid1JCdUlDb2daZzlsS1lucFRSdnd0M2pZbFpBdVRjbTRIb2s0RytCeThLdzJXcXVGcndCOUNoWEdLTkU3TFdLcFdGbGIwa2JSVlhMRG9OUzg0VFk0a1FNc21iQXBGQ2R5NlREeGdXTERGcWcwN0FQcWFHY1hpZWxMbU9ETEFlQVAiLCJtYWMiOiI3ZDdiOTdhYWMzOGNkMWE0NDEyYThmNWY4MjRlZjkxY2U4Njk4NWI4NGQyZjk1MWFkY2IzZThmNDVkNjc2MjJjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjRCRnQ4Z1B4Sy85MjRVYk1JbS83QWc9PSIsInZhbHVlIjoibGMxdi9XMGJ4VUpMUS94T0E0NEdlWnZ5SHpxWGd0KzhXcS9QaXJjMWloOGNnWmJzZ21oY0F5cnhoS3FvdnRlRitzaVNmQ2J6TU1KY1g1aVZMR2QxK3NFSlJyNTZnaWJEOFBzSDBTRHRYUHVBRXVDK2duTmo4Q2JHN2NzT3B5blgiLCJtYWMiOiJlODhkYmIxNWZhNTBlODcxZWYwNGRiYWZjNGZjNzA4YTNlMGNiZmIwOWZhMmIzMDBmZmMzZmQ1ZTA0YWQ4MDQ4IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:42', '2025-08-12 08:28:42')\\n-- \",\n    \"Time:\": 0.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.002887, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.006652, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.01258, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.014724, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 4.13\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.020388, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `coupon_code` where `code` = 'NEWBIE2025' limit 1\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.022428, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723' and `coupon_code` = 'NEWBIE2025')\\n-- \",\n    \"Time:\": 41.79\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.066518, "xdebug_link": null, "collector": "log"}, {"message": "[08:28:42] LOG.info: Coupon CV limit reached in checkCoupon {\n    \"coupon_code\": \"NEWBIE2025\",\n    \"employer_id\": 1723,\n    \"max_cv\": 2,\n    \"used_cv\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.069095, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.516882, "end": **********.082308, "duration": 0.5654261112213135, "duration_str": "565ms", "measures": [{"label": "Booting", "start": **********.516882, "relative_start": 0, "end": **********.895034, "relative_end": **********.895034, "duration": 0.*****************, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.895044, "relative_start": 0.***************, "end": **********.08231, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.906126, "relative_start": 0.*****************, "end": **********.90956, "relative_end": **********.90956, "duration": 0.0034339427947998047, "duration_str": "3.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST employer/job/check-coupon", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@checkCoupon<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=356\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-check-coupon", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=356\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:356-441</a>"}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06876, "accumulated_duration_str": "68.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-check-coupon' limit 1", "type": "query", "params": [], "bindings": ["employer-check-coupon"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.94365, "duration": 0.020239999999999998, "duration_str": "20.24ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "3d592fd5456e1f15d19d754be38fbf1da42db6bb145a2bbb84bcef03f436c1c6"}, "start_percent": 0, "width_percent": 29.436}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"code\\\":\\\"NEWBIE2025\\\",\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\"}', 'http://recland.local/employer/job/check-coupon', 'http://recland.local/employer/job/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"63\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"*\\/*\\\"],\\\"content-type\\\":[\\\"application\\/x-www-form-urlencoded; charset=UTF-8\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/job\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6Im1YQUlzejYxb0kwd3U4ZEhDZ3dsS3c9PSIsInZhbHVlIjoid1JCdUlDb2daZzlsS1lucFRSdnd0M2pZbFpBdVRjbTRIb2s0RytCeThLdzJXcXVGcndCOUNoWEdLTkU3TFdLcFdGbGIwa2JSVlhMRG9OUzg0VFk0a1FNc21iQXBGQ2R5NlREeGdXTERGcWcwN0FQcWFHY1hpZWxMbU9ETEFlQVAiLCJtYWMiOiI3ZDdiOTdhYWMzOGNkMWE0NDEyYThmNWY4MjRlZjkxY2U4Njk4NWI4NGQyZjk1MWFkY2IzZThmNDVkNjc2MjJjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjRCRnQ4Z1B4Sy85MjRVYk1JbS83QWc9PSIsInZhbHVlIjoibGMxdi9XMGJ4VUpMUS94T0E0NEdlWnZ5SHpxWGd0KzhXcS9QaXJjMWloOGNnWmJzZ21oY0F5cnhoS3FvdnRlRitzaVNmQ2J6TU1KY1g1aVZMR2QxK3NFSlJyNTZnaWJEOFBzSDBTRHRYUHVBRXVDK2duTmo4Q2JHN2NzT3B5blgiLCJtYWMiOiJlODhkYmIxNWZhNTBlODcxZWYwNGRiYWZjNGZjNzA4YTNlMGNiZmIwOWZhMmIzMDBmZmMzZmQ1ZTA0YWQ4MDQ4IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:28:42', '2025-08-12 08:28:42')", "type": "query", "params": [], "bindings": ["POST", "{\"code\":\"NEWBIE2025\",\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"}", "http://recland.local/employer/job/check-coupon", "http://recland.local/employer/job/create", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"63\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"*\\/*\"],\"content-type\":[\"application\\/x-www-form-urlencoded; charset=UTF-8\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6Im1YQUlzejYxb0kwd3U4ZEhDZ3dsS3c9PSIsInZhbHVlIjoid1JCdUlDb2daZzlsS1lucFRSdnd0M2pZbFpBdVRjbTRIb2s0RytCeThLdzJXcXVGcndCOUNoWEdLTkU3TFdLcFdGbGIwa2JSVlhMRG9OUzg0VFk0a1FNc21iQXBGQ2R5NlREeGdXTERGcWcwN0FQcWFHY1hpZWxMbU9ETEFlQVAiLCJtYWMiOiI3ZDdiOTdhYWMzOGNkMWE0NDEyYThmNWY4MjRlZjkxY2U4Njk4NWI4NGQyZjk1MWFkY2IzZThmNDVkNjc2MjJjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjRCRnQ4Z1B4Sy85MjRVYk1JbS83QWc9PSIsInZhbHVlIjoibGMxdi9XMGJ4VUpMUS94T0E0NEdlWnZ5SHpxWGd0KzhXcS9QaXJjMWloOGNnWmJzZ21oY0F5cnhoS3FvdnRlRitzaVNmQ2J6TU1KY1g1aVZMR2QxK3NFSlJyNTZnaWJEOFBzSDBTRHRYUHVBRXVDK2duTmo4Q2JHN2NzT3B5blgiLCJtYWMiOiJlODhkYmIxNWZhNTBlODcxZWYwNGRiYWZjNGZjNzA4YTNlMGNiZmIwOWZhMmIzMDBmZmMzZmQ1ZTA0YWQ4MDQ4IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:28:42", "2025-08-12 08:28:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.002399, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 29.436, "width_percent": 0.814}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.006142, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 30.25, "width_percent": 0.844}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.0119, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 31.094, "width_percent": 1.091}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.0144842, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 32.184, "width_percent": 0.451}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 1723 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.016328, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ef913f8afc9f4d48f551727e02040c91d14047cbc5f6bb1c2d8490bb794fe689"}, "start_percent": 32.635, "width_percent": 6.006}, {"sql": "select * from `coupon_code` where `code` = 'NEWBIE2025' limit 1", "type": "query", "params": [], "bindings": ["NEWBIE2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/CouponCode.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\CouponCode.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 368}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0220969, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CouponCode.php:34", "source": {"index": 15, "namespace": null, "name": "app/Models/CouponCode.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\CouponCode.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCouponCode.php&line=34", "ajax": false, "filename": "CouponCode.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `coupon_code` where `code` = ? limit 1", "hash": "939318fb5089194d16de754e2be6578b41239e2fa435ce0bc6c735f271c7b2fb"}, "start_percent": 38.642, "width_percent": 0.582}, {"sql": "select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723 and `coupon_code` = 'NEWBIE2025')", "type": "query", "params": [], "bindings": [1723, "NEWBIE2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.024882, "duration": 0.04179, "duration_str": "41.79ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:388", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=388", "ajax": false, "filename": "EmployerController.php", "line": "388"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ? and `coupon_code` = ?)", "hash": "925e8ef7a1ffca083a9650558b421896ea2e1d9a524563b39cb08ab61326945d"}, "start_percent": 39.223, "width_percent": 60.777}]}, "models": {"data": {"Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\EmployerType": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\CouponCode": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCouponCode.php&line=1", "ajax": false, "filename": "CouponCode.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 4}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/job/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer/job/check-coupon", "action_name": "employer-check-coupon", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@checkCoupon", "uri": "POST employer/job/check-coupon", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@checkCoupon<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=356\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=356\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:356-441</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "570ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1105476824 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1105476824\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1902405503 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">NEWBIE2025</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902405503\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1585537031 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">63</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1351 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962089$j5$l0$h0; XSRF-TOKEN=eyJpdiI6Im1YQUlzejYxb0kwd3U4ZEhDZ3dsS3c9PSIsInZhbHVlIjoid1JCdUlDb2daZzlsS1lucFRSdnd0M2pZbFpBdVRjbTRIb2s0RytCeThLdzJXcXVGcndCOUNoWEdLTkU3TFdLcFdGbGIwa2JSVlhMRG9OUzg0VFk0a1FNc21iQXBGQ2R5NlREeGdXTERGcWcwN0FQcWFHY1hpZWxMbU9ETEFlQVAiLCJtYWMiOiI3ZDdiOTdhYWMzOGNkMWE0NDEyYThmNWY4MjRlZjkxY2U4Njk4NWI4NGQyZjk1MWFkY2IzZThmNDVkNjc2MjJjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjRCRnQ4Z1B4Sy85MjRVYk1JbS83QWc9PSIsInZhbHVlIjoibGMxdi9XMGJ4VUpMUS94T0E0NEdlWnZ5SHpxWGd0KzhXcS9QaXJjMWloOGNnWmJzZ21oY0F5cnhoS3FvdnRlRitzaVNmQ2J6TU1KY1g1aVZMR2QxK3NFSlJyNTZnaWJEOFBzSDBTRHRYUHVBRXVDK2duTmo4Q2JHN2NzT3B5blgiLCJtYWMiOiJlODhkYmIxNWZhNTBlODcxZWYwNGRiYWZjNGZjNzA4YTNlMGNiZmIwOWZhMmIzMDBmZmMzZmQ1ZTA0YWQ4MDQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585537031\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2089336384 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089336384\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-250116261 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:28:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250116261\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1722214279 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722214279\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer/job/check-coupon", "action_name": "employer-check-coupon", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@checkCoupon"}, "badge": null}}