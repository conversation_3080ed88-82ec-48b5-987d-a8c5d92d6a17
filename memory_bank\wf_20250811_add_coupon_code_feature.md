# Workflow: Thê<PERSON> chức năng mã khuyến mại vào trang tạo job

**Ng<PERSON><PERSON> tạo:** 11/08/2025  
**<PERSON><PERSON> tả:** Thêm chức năng mã khuyến mại vào trang tạo job của Nhà tuyển dụng, cho phép nhập mã để tự động điền chi phí tuyển dụng.

## Công việc đã thực hiện:

### 1. Tạo migration cho bảng coupon_code

**File:** `database/migrations/2025_08_11_160146_create_coupon_code_table.php`

- Tạo bảng `coupon_code` với các trường:
  - `id`: Primary key
  - `code`: Mã khuyến mại (unique)
  - `min_recruitment_cost`: Chi phí tuyển dụng tối thiểu
  - `default_recruitment_cost`: Chi phí tuyển dụng mặc định
  - `collaborator_cost`: <PERSON> phí cho cộng tác viên
  - `created_at`, `updated_at`: Timestamps

### 2. Tạo migration thêm cột coupon_code vào bảng job

**File:** `database/migrations/2025_08_11_160220_add_coupon_code_to_job_table.php`

- Thêm cột `coupon_code` vào bảng `job` để lưu mã khuyến mại được sử dụng
- Cột có kiểu `string`, `nullable`, được đặt sau cột `bonus_currency`

### 3. Tạo model CouponCode

**File:** `app/Models/CouponCode.php`

- Tạo Eloquent model cho bảng `coupon_code`
- Định nghĩa `$fillable` và `$casts` cho các trường
- Thêm method `findByCode()` để tìm mã theo code
- Thêm method `isValid()` để kiểm tra tính hợp lệ

### 4. Cập nhật giao diện trang tạo job

**File:** `resources/views/frontend/pages/employer/create.blade.php`

- Thêm input mã khuyến mại ở đầu form (trước các input khác)
- Input có placeholder "Nhập mã khuyến mại (tùy chọn)"
- Thêm div hiển thị thông báo kết quả kiểm tra mã
- Thêm hidden input để lưu `collaborator_cost`

### 5. Tạo route và controller method kiểm tra mã khuyến mại

**File:** `routes/web.php`
- Thêm route `POST employer/job/check-coupon` với name `employer-check-coupon`

**File:** `app/Http/Controllers/Frontend/EmployerController.php`
- Thêm method `checkCoupon()` để xử lý AJAX request kiểm tra mã
- Trả về JSON response với thông tin chi phí hoặc lỗi

### 6. Thêm JavaScript xử lý mã khuyến mại

**File:** `resources/views/frontend/pages/employer/create.blade.php`

- Thêm event handler cho sự kiện `blur` của input mã khuyến mại
- Gọi AJAX đến endpoint kiểm tra mã
- Xử lý response: hiển thị thông báo, điền giá trị, disable input
- Thêm các function helper: `applyCoupon()`, `showCouponError()`, `resetCouponState()`

### 7. Cập nhật logic lưu job với mã khuyến mại

**File:** `app/Http/Requests/Frontend/JobRequest.php`
- Thêm validation rules cho `coupon_code` và `collaborator_cost`

**File:** `app/Services/Frontend/JobService.php`
- Cập nhật method `createJob()` để lưu trường `coupon_code` vào database

### 8. Chạy migration và tạo dữ liệu test

- Chạy `php artisan migrate` để tạo bảng và cột mới
- Tạo dữ liệu mẫu:
  - Mã `DISCOUNT50`: min=500k, default=1M, collaborator=200k
  - Mã `NEWBIE2025`: min=300k, default=800k, collaborator=150k

## Chức năng hoạt động:

1. **Nhập mã khuyến mại**: Người dùng nhập mã vào input đầu form
2. **Kiểm tra mã**: Khi blur khỏi input, hệ thống gọi AJAX kiểm tra mã
3. **Áp dụng mã hợp lệ**: 
   - Hiển thị thông báo "Đã áp dụng mã khuyến mại thành công"
   - Tự động điền chi phí tuyển dụng mặc định
   - Disable input chi phí (readonly)
   - Cập nhật thông báo giá tối thiểu
4. **Xử lý mã không hợp lệ**: Hiển thị thông báo lỗi màu đỏ
5. **Lưu job**: Mã khuyến mại được lưu vào database cùng với thông tin job

## Ràng buộc giao diện:

- Input chi phí tuyển dụng bị disable khi đã áp dụng mã khuyến mại
- Input chi phí cho cộng tác viên luôn ẩn (type="hidden")
- Hiển thị thông báo trạng thái rõ ràng cho người dùng

## Cải tiến bổ sung (Phiên bản 2):

### 9. Ràng buộc hình thức tuyển dụng khi có mã khuyến mại

**Cập nhật:** `resources/views/frontend/pages/employer/create.blade.php`
- Khi áp dụng mã khuyến mại thành công:
  - Tự động set select `#recruitment-type` thành "cv"
  - Disable select để không cho phép thay đổi
  - Hiển thị thông báo "Khi sử dụng mã khuyến mại, hình thức tuyển dụng sẽ được cố định là CV"
- Khi xóa mã hoặc mã không hợp lệ: Enable lại select

### 10. Thêm giới hạn số lượng CV cho mã khuyến mại

**File:** `database/migrations/2025_08_11_165444_add_max_cv_to_coupon_code_table.php`
- Thêm cột `max_cv` vào bảng `coupon_code` (integer, nullable)

**File:** `app/Models/CouponCode.php`
- Thêm `max_cv` vào fillable và casts

**File:** `app/Observers/SubmitCvObserver.php` (Tạo mới)
- Observer kiểm tra giới hạn CV khi có SubmitCv mới
- Logic: Đếm tổng submit_cv của tất cả job thuộc cùng employer sử dụng cùng mã
- Nếu đạt giới hạn: Tự động chuyển status = 0 (dừng tuyển) cho tất cả job sử dụng mã đó
- Ghi log chi tiết

**File:** `app/Providers/AppServiceProvider.php`
- Đăng ký SubmitCvObserver

### 11. Config tắt chức năng mã khuyến mại

**File:** `config/app.php`
- Thêm config `enable_coupon_code` (default: true)
- Có thể set `ENABLE_COUPON_CODE=false` trong .env để tắt

**File:** `resources/views/frontend/pages/employer/create.blade.php`
- Sử dụng `@if(config('app.enable_coupon_code', true))` để điều khiển hiển thị
- Cả HTML input và JavaScript đều được bao bọc bởi điều kiện này

## Dữ liệu test cập nhật:

- Mã `DISCOUNT50`: max_cv = 10
- Mã `NEWBIE2025`: max_cv = 5

## Files đã thay đổi:

### Phiên bản 1:
1. `database/migrations/2025_08_11_160146_create_coupon_code_table.php` (Tạo mới)
2. `database/migrations/2025_08_11_160220_add_coupon_code_to_job_table.php` (Tạo mới)
3. `app/Models/CouponCode.php` (Tạo mới)
4. `routes/web.php` (Thêm route)
5. `app/Http/Controllers/Frontend/EmployerController.php` (Thêm method)
6. `resources/views/frontend/pages/employer/create.blade.php` (Cập nhật giao diện + JS)
7. `app/Http/Requests/Frontend/JobRequest.php` (Thêm validation)
8. `app/Services/Frontend/JobService.php` (Cập nhật logic lưu)

### Phiên bản 2 (Cải tiến):
9. `database/migrations/2025_08_11_165444_add_max_cv_to_coupon_code_table.php` (Tạo mới)
10. `app/Models/CouponCode.php` (Cập nhật thêm max_cv)
11. `app/Observers/SubmitCvObserver.php` (Tạo mới)
12. `app/Providers/AppServiceProvider.php` (Đăng ký Observer)
13. `config/app.php` (Thêm config enable_coupon_code)
14. `resources/views/frontend/pages/employer/create.blade.php` (Cải tiến ràng buộc + config)

### Phiên bản 3 (Cập nhật Edit Job & CheckCoupon):
15. `resources/views/frontend/pages/employer/edit.blade.php` (Thêm ràng buộc cho edit job)
16. `app/Http/Controllers/Frontend/EmployerController.php` (Cập nhật updateJob và checkCoupon)

## Cải tiến bổ sung (Phiên bản 3):

### 12. Cập nhật trang edit job với ràng buộc coupon

**File:** `resources/views/frontend/pages/employer/edit.blade.php`

**Thay đổi:**
- Thêm thông báo cảnh báo khi job có coupon_code: "Job này đã sử dụng mã khuyến mại. Thông tin chi phí không thể thay đổi"
- Set readonly cho select hình thức tuyển dụng (`bonus_type`) khi có coupon_code
- Set readonly cho input chi phí tuyển dụng (`bonus`) khi có coupon_code
- Thêm hidden input để đảm bảo giá trị được gửi đi
- Thêm CSS styling `.coupon-readonly` với background xám nhạt và cursor not-allowed

### 13. Cập nhật controller update job

**File:** `app/Http/Controllers/Frontend/EmployerController.php`
**Method:** `updateJob($id, JobRequest $request)`

**Logic bảo vệ:**
- Kiểm tra job có coupon_code hay không
- Nếu có: Loại bỏ các trường `bonus_type`, `bonus`, `manual_bonus_for_ctv` khỏi data update
- Ghi log chi tiết khi có cố gắng thay đổi trường bị hạn chế
- Đảm bảo không thể bypass từ frontend

### 14. Cải tiến method checkCoupon

**File:** `app/Http/Controllers/Frontend/EmployerController.php`
**Method:** `checkCoupon(Request $request)`

**Tính năng mới:**
- **Kiểm tra giới hạn CV:** Đếm tổng submit_cv của tất cả job thuộc employer sử dụng cùng mã
- **Validation giới hạn:** Trả về error "Mã khuyến mại đã hết lượt sử dụng" nếu đạt max_cv
- **Response format mới:** Bao gồm `max_cv`, `used_cv`, `remaining_cv`
- **Logging chi tiết:** Ghi log cho cả success và error cases
- **Performance:** Sử dụng whereHas query hiệu quả
- **Edge case:** Xử lý trường hợp max_cv = null (không giới hạn)

**Response format mới:**
```json
{
  "success": true,
  "message": "Đã áp dụng mã khuyến mại thành công",
  "data": {
    "min_recruitment_cost": 500000,
    "default_recruitment_cost": 1000000,
    "collaborator_cost": 200000,
    "max_cv": 10,
    "used_cv": 3,
    "remaining_cv": 7
  }
}
```
