{"__meta": {"id": "01K2E0PBM7WYZYX0XWBD2NYEY2", "datetime": "2025-08-12 09:10:35", "utime": **********.272639, "method": "POST", "uri": "/employer/job/store", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 34, "messages": [{"message": "[09:10:29] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.403899, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.404238, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-store' limit 1\\n-- \",\n    \"Time:\": 21.48\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.456925, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\",\\\"coupon_code\\\":null,\\\"collaborator_cost\\\":null,\\\"name\\\":\\\"L\\\\u1eadp tr\\\\u00ecnh PHP\\\",\\\"expire_at\\\":\\\"18\\\\\\/08\\\\\\/2025\\\",\\\"vacancies\\\":\\\"5\\\",\\\"career\\\":\\\"1\\\",\\\"skill\\\":\\\"2\\\",\\\"rank\\\":\\\"3\\\",\\\"salary_currency\\\":\\\"VND\\\",\\\"salary_min\\\":\\\"10000000\\\",\\\"salary_currency_max\\\":\\\"VND\\\",\\\"salary_max\\\":\\\"20000000\\\",\\\"bonus_type\\\":\\\"cv\\\",\\\"bonus\\\":\\\"2000000\\\",\\\"type\\\":\\\"part-time\\\",\\\"address\\\":[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\\\u00e0 AC, ng\\\\u00f5 78 Duy T\\\\u00e2n - C\\\\u1ea7u Gi\\\\u1ea5y - H\\\\u00e0 N\\\\u1ed9i\\\"},null],\\\"jd_description\\\":\\\"<p>NEWBIE2025<\\\\\\/p>\\\",\\\"jd_request\\\":\\\"<p>NEWBIE2025<\\\\\\/p>\\\",\\\"jd_welfare\\\":\\\"<p>NEWBIE2025<\\\\\\/p>\\\"}', 'http:\\/\\/recland.local\\/employer\\/job\\/store', 'http:\\/\\/recland.local\\/employer\\/job\\/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"2399\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\\\\\/form-data; boundary=----WebKitFormBoundaryMAevSAZOKefzwIaK\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/job\\\\\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6IjFRTjdDNFdHSDJVQng3eTUyRWhjWHc9PSIsInZhbHVlIjoieUIva0o0c1dJTmN1M1EyandOMll0OXcrNGlwcFZYaEJWcjg0UU5leTZhemtmSjhBeXJVNk5kRFFORG00Wkp1bVd2Rk9BMVhqZ3huSlR2Y01KWXZ3dnVkMVRZbEx0bVNHNTBOTGUvbjRUeE51QWh4bjgwaUdZQmpyS1JmVWowQnciLCJtYWMiOiI3MTFiMDMzNjU2NjVjOGQ2NTgwYjg2ZjNmNjhlN2NiZTIxY2M0MDUyZDY0NDJkMzIzMjcyOWZlY2ZlZDhlY2FhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InhQZHBXYzgyN0JIN2EwYVVzaDhFNGc9PSIsInZhbHVlIjoibm5PVDJ2dWE3QldlN2tuRVhEcHhEQy9FV2NjbFc1SWZkQ01vUlF6aSs3Wkg5V1V5UFNha3RZeXV0cWtzSGRmR2RWLzZOUE1xYzN3VTgxN0ozMlpWZ0Y2bHFxRVpmOVV6Q2F3b0MrTE9ZQm4ydWhtUmEwMWkvVVdXQ0ZqRmZLVUwiLCJtYWMiOiJhMjIyMjFmZWYxMTIyZjIwZmVkZDYyY2QwMjQ4MjgyMWZiZDY2ODIyYThjZDlhYWZiN2NiNmMxN2NhYjgyNTllIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j59$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:29', '2025-08-12 09:10:29')\\n-- \",\n    \"Time:\": 0.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.475098, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.479531, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.486576, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.488897, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 4.18\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.494679, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops` where `id` = '2' limit 1\\n-- \",\n    \"Time:\": 0.53\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.57865, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job` (`name`, `slug`, `company_id`, `employer_id`, `expire_at`, `vacancies`, `type`, `urgent`, `remote`, `career`, `rank`, `skill_id`, `bonus`, `bonus_type`, `skills`, `address`, `jd_description`, `jd_request`, `jd_welfare`, `salary_min`, `salary_max`, `salary_currency`, `coupon_code`, `is_active`, `updated_at`, `created_at`) values ('L\\u1eadp tr\\u00ecnh PHP', 'lap-trinh-php-fC4uZ2Oz', '84', '1723', '2025-08-18', '5', 'part-time', '0', '0', '1', '3', '2', '2000000', 'cv', 'M\\u00f4i tr\\u01b0\\u1eddng\\/ X\\u1eed l\\u00fd ch\\u1ea5t th\\u1ea3i', '[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\\\u00e0 AC, ng\\\\u00f5 78 Duy T\\\\u00e2n - C\\\\u1ea7u Gi\\\\u1ea5y - H\\\\u00e0 N\\\\u1ed9i\\\"},null]', '<p>NEWBIE2025<\\/p>', '<p>NEWBIE2025<\\/p>', '<p>NEWBIE2025<\\/p>', '10000000', '20000000', 'VND', '', '0', '2025-08-12 09:10:29', '2025-08-12 09:10:29')\\n-- \",\n    \"Time:\": 0.96\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.611243, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.57\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.614628, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:29] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\\\"name\\\":\\\"L\\\\u1eadp tr\\\\u00ecnh PHP\\\",\\\"slug\\\":\\\"lap-trinh-php-fC4uZ2Oz\\\",\\\"company_id\\\":84,\\\"employer_id\\\":1723,\\\"expire_at\\\":\\\"2025-08-18\\\",\\\"vacancies\\\":\\\"5\\\",\\\"type\\\":\\\"part-time\\\",\\\"urgent\\\":0,\\\"remote\\\":0,\\\"career\\\":\\\"1\\\",\\\"rank\\\":\\\"3\\\",\\\"skill_id\\\":\\\"2\\\",\\\"bonus\\\":\\\"2000000\\\",\\\"bonus_type\\\":\\\"cv\\\",\\\"skills\\\":\\\"M\\\\u00f4i tr\\\\u01b0\\\\u1eddng\\\\\\/ X\\\\u1eed l\\\\u00fd ch\\\\u1ea5t th\\\\u1ea3i\\\",\\\"address\\\":\\\"[{\\\\\\\"area\\\\\\\":\\\\\\\"ha-noi\\\\\\\",\\\\\\\"address\\\\\\\":\\\\\\\"To\\\\\\\\u00e0 AC, ng\\\\\\\\u00f5 78 Duy T\\\\\\\\u00e2n - C\\\\\\\\u1ea7u Gi\\\\\\\\u1ea5y - H\\\\\\\\u00e0 N\\\\\\\\u1ed9i\\\\\\\"},null]\\\",\\\"jd_description\\\":\\\"<p>NEWBIE2025<\\\\\\/p>\\\",\\\"jd_request\\\":\\\"<p>NEWBIE2025<\\\\\\/p>\\\",\\\"jd_welfare\\\":\\\"<p>NEWBIE2025<\\\\\\/p>\\\",\\\"salary_min\\\":\\\"10000000\\\",\\\"salary_max\\\":\\\"20000000\\\",\\\"salary_currency\\\":\\\"VND\\\",\\\"coupon_code\\\":null,\\\"is_active\\\":0,\\\"id\\\":1649}', 'created', '1649', 'App\\\\Models\\\\Job', '1', 'App\\\\Models\\\\User', '', '127.0.0.1', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', 'http:\\/\\/recland.local\\/employer\\/job\\/store', '2025-08-12 09:10:29', '2025-08-12 09:10:29')\\n-- \",\n    \"Time:\": 1\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.619673, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Listeners\\\\NotifyGoogleIndexingApi', 'sync', '{\\\"uuid\\\":\\\"77dc9355-bda3-46fd-855e-2ab962ef2977\\\",\\\"displayName\\\":\\\"App\\\\\\\\Listeners\\\\\\\\NotifyGoogleIndexingApi\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Events\\\\\\\\CallQueuedListener\\\",\\\"command\\\":\\\"O:36:\\\\\\\"Illuminate\\\\\\\\Events\\\\\\\\CallQueuedListener\\\\\\\":19:{s:5:\\\\\\\"class\\\\\\\";s:37:\\\\\\\"App\\\\\\\\Listeners\\\\\\\\NotifyGoogleIndexingApi\\\\\\\";s:6:\\\\\\\"method\\\\\\\";s:6:\\\\\\\"handle\\\\\\\";s:4:\\\\\\\"data\\\\\\\";a:1:{i:0;O:21:\\\\\\\"App\\\\\\\\Events\\\\\\\\JobUpdated\\\\\\\":2:{s:3:\\\\\\\"job\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:14:\\\\\\\"App\\\\\\\\Models\\\\\\\\Job\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1649;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:6:\\\\\\\"action\\\\\\\";s:7:\\\\\\\"created\\\\\\\";}}s:5:\\\\\\\"tries\\\\\\\";N;s:13:\\\\\\\"maxExceptions\\\\\\\";N;s:7:\\\\\\\"backoff\\\\\\\";N;s:10:\\\\\\\"retryUntil\\\\\\\";N;s:7:\\\\\\\"timeout\\\\\\\";N;s:17:\\\\\\\"shouldBeEncrypted\\\\\\\";b:0;s:3:\\\\\\\"job\\\\\\\";N;s:10:\\\\\\\"connection\\\\\\\";N;s:5:\\\\\\\"queue\\\\\\\";N;s:15:\\\\\\\"chainConnection\\\\\\\";N;s:10:\\\\\\\"chainQueue\\\\\\\";N;s:19:\\\\\\\"chainCatchCallbacks\\\\\\\";N;s:5:\\\\\\\"delay\\\\\\\";N;s:11:\\\\\\\"afterCommit\\\\\\\";N;s:10:\\\\\\\"middleware\\\\\\\";a:0:{}s:7:\\\\\\\"chained\\\\\\\";a:0:{}}\\\"}}', '2025-08-12 09:10:31', '2025-08-12 09:10:31')\\n-- \",\n    \"Time:\": 0.69\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.625521, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `job`.`id` = '1649' limit 1\\n-- \",\n    \"Time:\": 0.96\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.741018, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_seo` (`job_id`, `title_vi`, `title_en`, `keyword_vi`, `keyword_en`, `updated_at`, `created_at`) values ('1649', 'L\\u1eadp tr\\u00ecnh PHP', 'L\\u1eadp tr\\u00ecnh PHP', 'M\\u00f4i tr\\u01b0\\u1eddng\\/ X\\u1eed l\\u00fd ch\\u1ea5t th\\u1ea3i', 'M\\u00f4i tr\\u01b0\\u1eddng\\/ X\\u1eed l\\u00fd ch\\u1ea5t th\\u1ea3i', '2025-08-12 09:10:31', '2025-08-12 09:10:31')\\n-- \",\n    \"Time:\": 0.29\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.781745, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_meta` (`job_id`, `priority`, `updated_at`, `created_at`) values ('1649', 'New', '2025-08-12 09:10:31', '2025-08-12 09:10:31')\\n-- \",\n    \"Time:\": 0.26\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.788923, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Notifications\\\\RegisterJob', 'sync', '{\\\"uuid\\\":\\\"b39e0f74-8fb5-4efe-b311-f23bd0e0c9ac\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\RegisterJob\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:1723;}s:9:\\\\\\\"relations\\\\\\\";a:3:{i:0;s:16:\\\\\\\"userEmployerType\\\\\\\";i:1;s:29:\\\\\\\"userEmployerType.employeeRole\\\\\\\";i:2;s:6:\\\\\\\"wallet\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:29:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\RegisterJob\\\\\\\":2:{s:7:\\\\\\\"\\\\u0000*\\\\u0000user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1723;s:9:\\\\\\\"relations\\\\\\\";a:3:{i:0;s:16:\\\\\\\"userEmployerType\\\\\\\";i:1;s:29:\\\\\\\"userEmployerType.employeeRole\\\\\\\";i:2;s:6:\\\\\\\"wallet\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:4:\\\\\\\"mail\\\\\\\";}}\\\"}}', '2025-08-12 09:10:31', '2025-08-12 09:10:31')\\n-- \",\n    \"Time:\": 1.02\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.936897, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.81\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.940721, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.94392, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.24\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.94524, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 3.89\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.950136, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:31] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` in (1723)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.951649, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:33] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\\\"<EMAIL>\\\"]', '[\\\"<EMAIL>\\\"]', '[]', '[Recland] [T\\u1ea0O JOB M\\u1edaI TH\\u00c0NH C\\u00d4NG]', '<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/assets_v2\\/images\\/graphics\\/logo-250.png?v=689aa298e8c36\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689aa298e8c48);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Xin ch\\u00e0o, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland th\\u00f4ng b\\u00e1o job c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c t\\u1ea1o m\\u1edbi th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Hello, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland announces that your job has been successfully created.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689aa298e8c76\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689aa298e8c81\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689aa298e8c8c\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n', '12d82a386e28a0a0ed26b170edfef643', '0', '2025-08-12 09:10:33', '2025-08-12 09:10:33')\\n-- \",\n    \"Time:\": 0.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": 1754964633.023901, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:34] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Jobs\\\\UpdateEmailLogStatus', 'sync', '{\\\"uuid\\\":\\\"eacdcac8-606e-4bcb-85d6-cdc0d252daca\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\",\\\"command\\\":\\\"O:29:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\UpdateEmailLogStatus\\\\\\\":1:{s:5:\\\\\\\"email\\\\\\\";O:28:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Email\\\\\\\":6:{i:0;N;i:1;N;i:2;s:5635:\\\\\\\"<!DOCTYPE html>\\\\r\\\\n<html>\\\\r\\\\n\\\\r\\\\n<body width=\\\\\\\"100%\\\\\\\"\\\\r\\\\n    style=\\\\\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\\\\\">\\\\r\\\\n    <center style=\\\\\\\"width: 100%; background-color: #f1f1f1;\\\\\\\">\\\\r\\\\n        <div style=\\\\\\\"max-width: 600px; margin: 0 auto;\\\\\\\">\\\\r\\\\n            <table align=\\\\\\\"center\\\\\\\" role=\\\\\\\"presentation\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\" cellpadding=\\\\\\\"0\\\\\\\" border=\\\\\\\"0\\\\\\\" width=\\\\\\\"100%\\\\\\\"\\\\r\\\\n                style=\\\\\\\"margin: auto;\\\\\\\">\\\\r\\\\n                <tr>\\\\r\\\\n                    <td>\\\\r\\\\n                        <div style=\\\\\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\\\\\">\\\\r\\\\n                            <img style=\\\\\\\"max-width: 100%\\\\\\\"\\\\r\\\\n                                src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/assets_v2\\\\\\/images\\\\\\/graphics\\\\\\/logo-250.png?v=689aa298e8c36\\\\\\\">\\\\r\\\\n                        <\\\\\\/div>\\\\r\\\\n                    <\\\\\\/td>\\\\r\\\\n                <\\\\\\/tr>\\\\r\\\\n            <\\\\\\/table>\\\\r\\\\n            <div style=\\\\\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\\\\\">\\\\r\\\\n                <div\\\\r\\\\n                    style=\\\\\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\\\r\\\\n                background-image: url(http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/background.png?v=689aa298e8c48);\\\\r\\\\n                background-repeat: no-repeat;background-size: 100%;\\\\\\\">\\\\r\\\\n                    <table>\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                <div\\\\r\\\\n                                    style=\\\\\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\\\\\">\\\\r\\\\n                                    <div style=\\\\\\\"margin-bottom: 14px\\\\\\\">\\\\r\\\\n                                                                            <\\\\\\/div>\\\\r\\\\n                                    <div>\\\\r\\\\n                                                                            <\\\\\\/div>\\\\r\\\\n\\\\r\\\\n                                <\\\\\\/div>\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                    <\\\\\\/table>\\\\r\\\\n                    <table style=\\\\\\\"width: 100%\\\\\\\" role=\\\\\\\"presentation\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\" cellpadding=\\\\\\\"0\\\\\\\" border=\\\\\\\"0\\\\\\\"\\\\r\\\\n                        width=\\\\\\\"100%\\\\\\\">\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                    <div\\\\r\\\\n        style=\\\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\\\">\\\\r\\\\n        Xin ch\\\\u00e0o, H\\\\u1eadu Test\\\\r\\\\n        <br>\\\\r\\\\n        Recland th\\\\u00f4ng b\\\\u00e1o job c\\\\u1ee7a b\\\\u1ea1n \\\\u0111\\\\u00e3 \\\\u0111\\\\u01b0\\\\u1ee3c t\\\\u1ea1o m\\\\u1edbi th\\\\u00e0nh c\\\\u00f4ng.\\\\r\\\\n        <p><b>Tr\\\\u00e2n tr\\\\u1ecdng,<\\\\\\/b><\\\\\\/p>\\\\r\\\\n        <p><i>\\\\u0110\\\\u1ed9i ng\\\\u0169 Recland.<\\\\\\/i><\\\\\\/p>\\\\r\\\\n    <\\\\\\/div>\\\\r\\\\n    <div style=\\\\\\\"border: 5px solid #F7F7F7;\\\\\\\"><\\\\\\/div>\\\\r\\\\n    <div\\\\r\\\\n        style=\\\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\\\">\\\\r\\\\n        Hello, H\\\\u1eadu Test\\\\r\\\\n        <br>\\\\r\\\\n        Recland announces that your job has been successfully created.\\\\r\\\\n        <p><b>Best regards,<\\\\\\/b><\\\\\\/p>\\\\r\\\\n        <p><i>Recland team.<\\\\\\/i><\\\\\\/p>\\\\r\\\\n    <\\\\\\/div>\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                        <tr>\\\\r\\\\n                            <td>\\\\r\\\\n                                <div style=\\\\\\\"padding:12px 0\\\\\\\">\\\\r\\\\n                                    <div\\\\r\\\\n                                        style=\\\\\\\"background-image: url(http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\\\\\">\\\\r\\\\n                                        <div style=\\\\\\\"margin-bottom: 12px;text-align: center\\\\\\\">\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-twitter.png?v=689aa298e8c76\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-facebook.png?v=689aa298e8c81\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                            <a style=\\\\\\\"padding-left: 16px;padding-right: 16px\\\\\\\" href=\\\\\\\"#\\\\\\\"><img\\\\r\\\\n                                                    src=\\\\\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/frontend\\\\\\/asset\\\\\\/images\\\\\\/template-email\\\\\\/icon-instagram.png?v=689aa298e8c8c\\\\\\\"><\\\\\\/a>\\\\r\\\\n                                        <\\\\\\/div>\\\\r\\\\n                                        <p\\\\r\\\\n                                            style=\\\\\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\\\\\">\\\\r\\\\n                                            N\\\\u1ec1n t\\\\u1ea3ng t\\\\u1ea1o ra c\\\\u01a1 h\\\\u1ed9i ki\\\\u1ebfm ti\\\\u1ec1n d\\\\u00e0nh cho HR Freelancer<\\\\\\/p>\\\\r\\\\n                                        <p\\\\r\\\\n                                            style=\\\\\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\\\\\">\\\\r\\\\n                                            \\\\u00a9 2022 Recland.co<\\\\\\/p>\\\\r\\\\n                                    <\\\\\\/div>\\\\r\\\\n                                <\\\\\\/div>\\\\r\\\\n\\\\r\\\\n                            <\\\\\\/td>\\\\r\\\\n                        <\\\\\\/tr>\\\\r\\\\n                    <\\\\\\/table>\\\\r\\\\n                <\\\\\\/div>\\\\r\\\\n            <\\\\\\/div>\\\\r\\\\n        <\\\\\\/div>\\\\r\\\\n    <\\\\\\/center>\\\\r\\\\n<\\\\\\/body>\\\\r\\\\n\\\\r\\\\n<\\\\\\/html>\\\\r\\\\n\\\\\\\";i:3;s:5:\\\\\\\"utf-8\\\\\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\\\\":2:{s:46:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\u0000headers\\\\\\\";a:4:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;O:47:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:4:\\\\\\\"From\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:58:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\u0000addresses\\\\\\\";a:1:{i:0;O:30:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\\\\":2:{s:39:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000address\\\\\\\";s:18:\\\\\\\"<EMAIL>\\\\\\\";s:36:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000name\\\\\\\";s:7:\\\\\\\"RECLAND\\\\\\\";}}}}s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;O:47:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:2:\\\\\\\"To\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:58:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\MailboxListHeader\\\\u0000addresses\\\\\\\";a:1:{i:0;O:30:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\\\\":2:{s:39:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000address\\\\\\\";s:21:\\\\\\\"<EMAIL>\\\\\\\";s:36:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Address\\\\u0000name\\\\\\\";s:0:\\\\\\\"\\\\\\\";}}}}s:7:\\\\\\\"subject\\\\\\\";a:1:{i:0;O:48:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:7:\\\\\\\"Subject\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:55:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\u0000value\\\\\\\";s:40:\\\\\\\"[Recland] [T\\\\u1ea0O JOB M\\\\u1edaI TH\\\\u00c0NH C\\\\u00d4NG]\\\\\\\";}}s:17:\\\\\\\"x-original-emails\\\\\\\";a:1:{i:0;O:48:\\\\\\\"Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\\\\":5:{s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000name\\\\\\\";s:17:\\\\\\\"X-Original-Emails\\\\\\\";s:56:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lineLength\\\\\\\";i:76;s:50:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000lang\\\\\\\";N;s:53:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\AbstractHeader\\\\u0000charset\\\\\\\";s:5:\\\\\\\"utf-8\\\\\\\";s:55:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\UnstructuredHeader\\\\u0000value\\\\\\\";s:2:\\\\\\\"[]\\\\\\\";}}}s:49:\\\\\\\"\\\\u0000Symfony\\\\\\\\Component\\\\\\\\Mime\\\\\\\\Header\\\\\\\\Headers\\\\u0000lineLength\\\\\\\";i:76;}i:1;N;}}}\\\"}}', '2025-08-12 09:10:34', '2025-08-12 09:10:34')\\n-- \",\n    \"Time:\": 0.74\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.846656, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `email_logs` where `hash` = '1f256ac9dcb7bc2317c51047224616c5' and `created_at` >= '2025-08-12 09:05:34'\\n-- \",\n    \"Time:\": 275.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.124963, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\\\Notifications\\\\RegisterJob', 'sync', '{\\\"uuid\\\":\\\"e44d6cc0-a36f-423c-b2d9-56726f73f54d\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\RegisterJob\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:1723;}s:9:\\\\\\\"relations\\\\\\\";a:3:{i:0;s:16:\\\\\\\"userEmployerType\\\\\\\";i:1;s:29:\\\\\\\"userEmployerType.employeeRole\\\\\\\";i:2;s:6:\\\\\\\"wallet\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:29:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\RegisterJob\\\\\\\":2:{s:7:\\\\\\\"\\\\u0000*\\\\u0000user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1723;s:9:\\\\\\\"relations\\\\\\\";a:3:{i:0;s:16:\\\\\\\"userEmployerType\\\\\\\";i:1;s:29:\\\\\\\"userEmployerType.employeeRole\\\\\\\";i:2;s:6:\\\\\\\"wallet\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:8:\\\\\\\"database\\\\\\\";}}\\\"}}', '2025-08-12 09:10:35', '2025-08-12 09:10:35')\\n-- \",\n    \"Time:\": 0.55\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.167204, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.65\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.169058, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.77\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.171105, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.29\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.172492, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 6.08\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.179632, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` in (1723)\\n-- \",\n    \"Time:\": 0.5\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.181348, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `value`, `key` from `settings` where `key` like '%settings.vi.notification%'\\n-- \",\n    \"Time:\": 6.16\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.213862, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `value`, `key` from `settings` where `key` like '%settings.en.notification%'\\n-- \",\n    \"Time:\": 4.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.221705, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:35] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d', 'App\\\\Notifications\\\\RegisterJob', '{\\\"content_vi\\\":\\\"Nh\\\\u00e0 tuy\\\\u1ec3n d\\\\u1ee5ng \\\\u0111\\\\u00e3 t\\\\u1ea1o job m\\\\u1edbi th\\\\u00e0nh c\\\\u00f4ng.\\\",\\\"content_en\\\":\\\"The employer has successfully created a new job.\\\"}', '', '1723', 'App\\\\Models\\\\User', '2025-08-12 09:10:35', '2025-08-12 09:10:35')\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.225342, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.036603, "end": **********.272704, "duration": 6.236100912094116, "duration_str": "6.24s", "measures": [{"label": "Booting", "start": **********.036603, "relative_start": 0, "end": **********.386108, "relative_end": **********.386108, "duration": 0.****************, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.386118, "relative_start": 0.****************, "end": **********.272706, "relative_end": 2.1457672119140625e-06, "duration": 5.***************, "duration_str": "5.89s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.397039, "relative_start": 0.*****************, "end": **********.400424, "relative_end": **********.400424, "duration": 0.0033850669860839844, "duration_str": "3.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x email.RegisterJob", "param_count": null, "params": [], "start": **********.952479, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/email/RegisterJob.blade.phpemail.RegisterJob", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Femail%2FRegisterJob.blade.php&line=1", "ajax": false, "filename": "RegisterJob.blade.php", "line": "?"}, "render_count": 1, "name_original": "email.RegisterJob"}, {"name": "1x email.master", "param_count": null, "params": [], "start": **********.953027, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/email/master.blade.phpemail.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Femail%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "email.master"}]}, "route": {"uri": "POST employer/job/store", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@storeJob<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=335\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-store", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=335\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:335-354</a>"}, "queries": {"count": 37, "nb_statements": 35, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.37915999999999994, "accumulated_duration_str": "379ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-store' limit 1", "type": "query", "params": [], "bindings": ["employer-store"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.4357219, "duration": 0.02148, "duration_str": "21.48ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "974756f57658f5b062a6b1ed4e78dcc7e253b6227c82f453e468dca59d66dfea"}, "start_percent": 0, "width_percent": 5.665}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\",\\\"coupon_code\\\":null,\\\"collaborator_cost\\\":null,\\\"name\\\":\\\"L\\u1eadp tr\\u00ecnh PHP\\\",\\\"expire_at\\\":\\\"18\\/08\\/2025\\\",\\\"vacancies\\\":\\\"5\\\",\\\"career\\\":\\\"1\\\",\\\"skill\\\":\\\"2\\\",\\\"rank\\\":\\\"3\\\",\\\"salary_currency\\\":\\\"VND\\\",\\\"salary_min\\\":\\\"10000000\\\",\\\"salary_currency_max\\\":\\\"VND\\\",\\\"salary_max\\\":\\\"20000000\\\",\\\"bonus_type\\\":\\\"cv\\\",\\\"bonus\\\":\\\"2000000\\\",\\\"type\\\":\\\"part-time\\\",\\\"address\\\":[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\\\"},null],\\\"jd_description\\\":\\\"<p>NEWBIE2025<\\/p>\\\",\\\"jd_request\\\":\\\"<p>NEWBIE2025<\\/p>\\\",\\\"jd_welfare\\\":\\\"<p>NEWBIE2025<\\/p>\\\"}', 'http://recland.local/employer/job/store', 'http://recland.local/employer/job/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"2399\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\/form-data; boundary=----WebKitFormBoundaryMAevSAZOKefzwIaK\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/job\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6IjFRTjdDNFdHSDJVQng3eTUyRWhjWHc9PSIsInZhbHVlIjoieUIva0o0c1dJTmN1M1EyandOMll0OXcrNGlwcFZYaEJWcjg0UU5leTZhemtmSjhBeXJVNk5kRFFORG00Wkp1bVd2Rk9BMVhqZ3huSlR2Y01KWXZ3dnVkMVRZbEx0bVNHNTBOTGUvbjRUeE51QWh4bjgwaUdZQmpyS1JmVWowQnciLCJtYWMiOiI3MTFiMDMzNjU2NjVjOGQ2NTgwYjg2ZjNmNjhlN2NiZTIxY2M0MDUyZDY0NDJkMzIzMjcyOWZlY2ZlZDhlY2FhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InhQZHBXYzgyN0JIN2EwYVVzaDhFNGc9PSIsInZhbHVlIjoibm5PVDJ2dWE3QldlN2tuRVhEcHhEQy9FV2NjbFc1SWZkQ01vUlF6aSs3Wkg5V1V5UFNha3RZeXV0cWtzSGRmR2RWLzZOUE1xYzN3VTgxN0ozMlpWZ0Y2bHFxRVpmOVV6Q2F3b0MrTE9ZQm4ydWhtUmEwMWkvVVdXQ0ZqRmZLVUwiLCJtYWMiOiJhMjIyMjFmZWYxMTIyZjIwZmVkZDYyY2QwMjQ4MjgyMWZiZDY2ODIyYThjZDlhYWZiN2NiNmMxN2NhYjgyNTllIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j59$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:29', '2025-08-12 09:10:29')", "type": "query", "params": [], "bindings": ["POST", "{\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\",\"coupon_code\":null,\"collaborator_cost\":null,\"name\":\"L\\u1eadp tr\\u00ecnh PHP\",\"expire_at\":\"18\\/08\\/2025\",\"vacancies\":\"5\",\"career\":\"1\",\"skill\":\"2\",\"rank\":\"3\",\"salary_currency\":\"VND\",\"salary_min\":\"10000000\",\"salary_currency_max\":\"VND\",\"salary_max\":\"20000000\",\"bonus_type\":\"cv\",\"bonus\":\"2000000\",\"type\":\"part-time\",\"address\":[{\"area\":\"ha-noi\",\"address\":\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\"},null],\"jd_description\":\"<p>NEWBIE2025<\\/p>\",\"jd_request\":\"<p>NEWBIE2025<\\/p>\",\"jd_welfare\":\"<p>NEWBIE2025<\\/p>\"}", "http://recland.local/employer/job/store", "http://recland.local/employer/job/create", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"2399\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundaryMAevSAZOKefzwIaK\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6IjFRTjdDNFdHSDJVQng3eTUyRWhjWHc9PSIsInZhbHVlIjoieUIva0o0c1dJTmN1M1EyandOMll0OXcrNGlwcFZYaEJWcjg0UU5leTZhemtmSjhBeXJVNk5kRFFORG00Wkp1bVd2Rk9BMVhqZ3huSlR2Y01KWXZ3dnVkMVRZbEx0bVNHNTBOTGUvbjRUeE51QWh4bjgwaUdZQmpyS1JmVWowQnciLCJtYWMiOiI3MTFiMDMzNjU2NjVjOGQ2NTgwYjg2ZjNmNjhlN2NiZTIxY2M0MDUyZDY0NDJkMzIzMjcyOWZlY2ZlZDhlY2FhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InhQZHBXYzgyN0JIN2EwYVVzaDhFNGc9PSIsInZhbHVlIjoibm5PVDJ2dWE3QldlN2tuRVhEcHhEQy9FV2NjbFc1SWZkQ01vUlF6aSs3Wkg5V1V5UFNha3RZeXV0cWtzSGRmR2RWLzZOUE1xYzN3VTgxN0ozMlpWZ0Y2bHFxRVpmOVV6Q2F3b0MrTE9ZQm4ydWhtUmEwMWkvVVdXQ0ZqRmZLVUwiLCJtYWMiOiJhMjIyMjFmZWYxMTIyZjIwZmVkZDYyY2QwMjQ4MjgyMWZiZDY2ODIyYThjZDlhYWZiN2NiNmMxN2NhYjgyNTllIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j59$l0$h0\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 09:10:29", "2025-08-12 09:10:29"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.474611, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 5.665, "width_percent": 0.148}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.479042, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 5.813, "width_percent": 0.148}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.485895, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 5.961, "width_percent": 0.198}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.488647, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 6.158, "width_percent": 0.084}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 1723 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.49057, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ef913f8afc9f4d48f551727e02040c91d14047cbc5f6bb1c2d8490bb794fe689"}, "start_percent": 6.243, "width_percent": 1.102}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 338}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.576725, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EmployerController.php:338", "source": {"index": 8, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 338}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=338", "ajax": false, "filename": "EmployerController.php", "line": "338"}, "connection": "hri_recland_product", "explain": null, "start_percent": 7.345, "width_percent": 0}, {"sql": "select * from `job_tops` where `id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 339}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.578272, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "JobService.php:126", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FJobService.php&line=126", "ajax": false, "filename": "JobService.php", "line": "126"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops` where `id` = ? limit 1", "hash": "90aec72b1f0521ed5e7093b709c7ebbf740b2350f93c281845150b5d0d5f99dd"}, "start_percent": 7.345, "width_percent": 0.14}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'job'", "type": "query", "params": [], "bindings": ["hri_recland_product", "job"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, {"index": 18, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 168}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 339}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.589844, "duration": 0.018359999999999998, "duration_str": "18.36ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:124", "source": {"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=124", "ajax": false, "filename": "BaseRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "990a09a5fd80a4157b96a1522ddde828b43b0f6c71c90db92e7d7f75e92a356e"}, "start_percent": 7.485, "width_percent": 4.842}, {"sql": "insert into `job` (`name`, `slug`, `company_id`, `employer_id`, `expire_at`, `vacancies`, `type`, `urgent`, `remote`, `career`, `rank`, `skill_id`, `bonus`, `bonus_type`, `skills`, `address`, `jd_description`, `jd_request`, `jd_welfare`, `salary_min`, `salary_max`, `salary_currency`, `coupon_code`, `is_active`, `updated_at`, `created_at`) values ('Lập trình PHP', 'lap-trinh-php-fC4uZ2Oz', 84, 1723, '2025-08-18', '5', 'part-time', 0, 0, '1', '3', '2', '2000000', 'cv', 'Môi trường/ Xử lý chất thải', '[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\\\"},null]', '<p>NEWBIE2025</p>', '<p>NEWBIE2025</p>', '<p>NEWBIE2025</p>', '10000000', '20000000', 'VND', '', 0, '2025-08-12 09:10:29', '2025-08-12 09:10:29')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "lap-trinh-php-fC4uZ2Oz", 84, 1723, "2025-08-18", "5", "part-time", 0, 0, "1", "3", "2", "2000000", "cv", "<PERSON><PERSON><PERSON> trườ<PERSON>/ <PERSON><PERSON> lý chất thải", "[{\"area\":\"ha-noi\",\"address\":\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\"},null]", "<p>NEWBIE2025</p>", "<p>NEWBIE2025</p>", "<p>NEWBIE2025</p>", "10000000", "20000000", "VND", null, 0, "2025-08-12 09:10:29", "2025-08-12 09:10:29"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 168}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 339}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.610358, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:124", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=124", "ajax": false, "filename": "BaseRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": null, "start_percent": 12.327, "width_percent": 0.253}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Resolvers/UserResolver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Resolvers\\UserResolver.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditable.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditable.php", "line": 409}], "start": **********.614126, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 12.58, "width_percent": 0.15}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('[]', '{\\\"name\\\":\\\"L\\u1eadp tr\\u00ecnh PHP\\\",\\\"slug\\\":\\\"lap-trinh-php-fC4uZ2Oz\\\",\\\"company_id\\\":84,\\\"employer_id\\\":1723,\\\"expire_at\\\":\\\"2025-08-18\\\",\\\"vacancies\\\":\\\"5\\\",\\\"type\\\":\\\"part-time\\\",\\\"urgent\\\":0,\\\"remote\\\":0,\\\"career\\\":\\\"1\\\",\\\"rank\\\":\\\"3\\\",\\\"skill_id\\\":\\\"2\\\",\\\"bonus\\\":\\\"2000000\\\",\\\"bonus_type\\\":\\\"cv\\\",\\\"skills\\\":\\\"M\\u00f4i tr\\u01b0\\u1eddng\\/ X\\u1eed l\\u00fd ch\\u1ea5t th\\u1ea3i\\\",\\\"address\\\":\\\"[{\\\\\"area\\\\\":\\\\\"ha-noi\\\\\",\\\\\"address\\\\\":\\\\\"To\\\\u00e0 AC, ng\\\\u00f5 78 Duy T\\\\u00e2n - C\\\\u1ea7u Gi\\\\u1ea5y - H\\\\u00e0 N\\\\u1ed9i\\\\\"},null]\\\",\\\"jd_description\\\":\\\"<p>NEWBIE2025<\\/p>\\\",\\\"jd_request\\\":\\\"<p>NEWBIE2025<\\/p>\\\",\\\"jd_welfare\\\":\\\"<p>NEWBIE2025<\\/p>\\\",\\\"salary_min\\\":\\\"10000000\\\",\\\"salary_max\\\":\\\"20000000\\\",\\\"salary_currency\\\":\\\"VND\\\",\\\"coupon_code\\\":null,\\\"is_active\\\":0,\\\"id\\\":1649}', 'created', 1649, 'App\\Models\\Job', 1, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://recland.local/employer/job/store', '2025-08-12 09:10:29', '2025-08-12 09:10:29')", "type": "query", "params": [], "bindings": ["[]", "{\"name\":\"L\\u1eadp tr\\u00ecnh PHP\",\"slug\":\"lap-trinh-php-fC4uZ2Oz\",\"company_id\":84,\"employer_id\":1723,\"expire_at\":\"2025-08-18\",\"vacancies\":\"5\",\"type\":\"part-time\",\"urgent\":0,\"remote\":0,\"career\":\"1\",\"rank\":\"3\",\"skill_id\":\"2\",\"bonus\":\"2000000\",\"bonus_type\":\"cv\",\"skills\":\"M\\u00f4i tr\\u01b0\\u1eddng\\/ X\\u1eed l\\u00fd ch\\u1ea5t th\\u1ea3i\",\"address\":\"[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\\\u00e0 AC, ng\\\\u00f5 78 Duy T\\\\u00e2n - C\\\\u1ea7u Gi\\\\u1ea5y - H\\\\u00e0 N\\\\u1ed9i\\\"},null]\",\"jd_description\":\"<p>NEWBIE2025<\\/p>\",\"jd_request\":\"<p>NEWBIE2025<\\/p>\",\"jd_welfare\":\"<p>NEWBIE2025<\\/p>\",\"salary_min\":\"10000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"VND\",\"coupon_code\":null,\"is_active\":0,\"id\":1649}", "created", 1649, "App\\Models\\Job", 1, "App\\Models\\User", null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "http://recland.local/employer/job/store", "2025-08-12 09:10:29", "2025-08-12 09:10:29"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 37}, {"index": 35, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, {"index": 36, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 168}], "start": **********.618745, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Auditor.php:83", "source": {"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FAuditor.php&line=83", "ajax": false, "filename": "Auditor.php", "line": "83"}, "connection": "hri_recland_product", "explain": null, "start_percent": 12.731, "width_percent": 0.264}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Listeners\\NotifyGoogleIndexingApi', 'sync', '{\\\"uuid\\\":\\\"77dc9355-bda3-46fd-855e-2ab962ef2977\\\",\\\"displayName\\\":\\\"App\\\\Listeners\\\\NotifyGoogleIndexingApi\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Events\\\\CallQueuedListener\\\",\\\"command\\\":\\\"O:36:\\\\\"Illuminate\\\\Events\\\\CallQueuedListener\\\\\":19:{s:5:\\\\\"class\\\\\";s:37:\\\\\"App\\\\Listeners\\\\NotifyGoogleIndexingApi\\\\\";s:6:\\\\\"method\\\\\";s:6:\\\\\"handle\\\\\";s:4:\\\\\"data\\\\\";a:1:{i:0;O:21:\\\\\"App\\\\Events\\\\JobUpdated\\\\\":2:{s:3:\\\\\"job\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:14:\\\\\"App\\\\Models\\\\Job\\\\\";s:2:\\\\\"id\\\\\";i:1649;s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:6:\\\\\"action\\\\\";s:7:\\\\\"created\\\\\";}}s:5:\\\\\"tries\\\\\";N;s:13:\\\\\"maxExceptions\\\\\";N;s:7:\\\\\"backoff\\\\\";N;s:10:\\\\\"retryUntil\\\\\";N;s:7:\\\\\"timeout\\\\\";N;s:17:\\\\\"shouldBeEncrypted\\\\\";b:0;s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}', '2025-08-12 09:10:31', '2025-08-12 09:10:31')", "type": "query", "params": [], "bindings": ["", "App\\Listeners\\NotifyGoogleIndexingApi", "sync", "{\"uuid\":\"77dc9355-bda3-46fd-855e-2ab962ef2977\",\"displayName\":\"App\\\\Listeners\\\\NotifyGoogleIndexingApi\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Events\\\\CallQueuedListener\",\"command\":\"O:36:\\\"Illuminate\\\\Events\\\\CallQueuedListener\\\":19:{s:5:\\\"class\\\";s:37:\\\"App\\\\Listeners\\\\NotifyGoogleIndexingApi\\\";s:6:\\\"method\\\";s:6:\\\"handle\\\";s:4:\\\"data\\\";a:1:{i:0;O:21:\\\"App\\\\Events\\\\JobUpdated\\\":2:{s:3:\\\"job\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:14:\\\"App\\\\Models\\\\Job\\\";s:2:\\\"id\\\";i:1649;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:6:\\\"action\\\";s:7:\\\"created\\\";}}s:5:\\\"tries\\\";N;s:13:\\\"maxExceptions\\\";N;s:7:\\\"backoff\\\";N;s:10:\\\"retryUntil\\\";N;s:7:\\\"timeout\\\";N;s:17:\\\"shouldBeEncrypted\\\";b:0;s:3:\\\"job\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}}\"}}", "2025-08-12 09:10:31", "2025-08-12 09:10:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 57}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 451}], "start": **********.625024, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:74", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=74", "ajax": false, "filename": "AppServiceProvider.php", "line": "74"}, "connection": "hri_recland_product", "explain": null, "start_percent": 12.995, "width_percent": 0.182}, {"sql": "select * from `job` where `job`.`id` = 1649 limit 1", "type": "query", "params": [], "bindings": [1649], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.740229, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `job`.`id` = ? limit 1", "hash": "b2803155bd2c7a4b71e55d6d37340bfb4cede636dd687a0275fb20238dc268d3"}, "start_percent": 13.176, "width_percent": 0.253}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'job_seo'", "type": "query", "params": [], "bindings": ["hri_recland_product", "job_seo"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, {"index": 18, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 178}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 339}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7602131, "duration": 0.019780000000000002, "duration_str": "19.78ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:124", "source": {"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=124", "ajax": false, "filename": "BaseRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "e8493345a0d887b602c5d268a0aba46c51856efe7122372e8aac402f1f8c1d7f"}, "start_percent": 13.43, "width_percent": 5.217}, {"sql": "insert into `job_seo` (`job_id`, `title_vi`, `title_en`, `keyword_vi`, `keyword_en`, `updated_at`, `created_at`) values (1649, '<PERSON><PERSON><PERSON> trình PHP', '<PERSON><PERSON><PERSON> trình <PERSON>', '<PERSON><PERSON><PERSON> trường/ <PERSON><PERSON> lý chất thải', '<PERSON><PERSON><PERSON> trường/ <PERSON><PERSON> lý chất thải', '2025-08-12 09:10:31', '2025-08-12 09:10:31')", "type": "query", "params": [], "bindings": [1649, "<PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "<PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "<PERSON><PERSON><PERSON> trườ<PERSON>/ <PERSON><PERSON> lý chất thải", "<PERSON><PERSON><PERSON> trườ<PERSON>/ <PERSON><PERSON> lý chất thải", "2025-08-12 09:10:31", "2025-08-12 09:10:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 178}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 339}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.781528, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:124", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=124", "ajax": false, "filename": "BaseRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": null, "start_percent": 18.646, "width_percent": 0.076}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'job_meta'", "type": "query", "params": [], "bindings": ["hri_recland_product", "job_meta"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, {"index": 18, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 185}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 339}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.783127, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:124", "source": {"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=124", "ajax": false, "filename": "BaseRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "37b4fac14c4d6b854c431bb80b1e21779506625812a4e48d3ade231768c842d7"}, "start_percent": 18.723, "width_percent": 1.147}, {"sql": "insert into `job_meta` (`job_id`, `priority`, `updated_at`, `created_at`) values (1649, 'New', '2025-08-12 09:10:31', '2025-08-12 09:10:31')", "type": "query", "params": [], "bindings": [1649, "New", "2025-08-12 09:10:31", "2025-08-12 09:10:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 185}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 339}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7887328, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:124", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=124", "ajax": false, "filename": "BaseRepository.php", "line": "124"}, "connection": "hri_recland_product", "explain": null, "start_percent": 19.87, "width_percent": 0.069}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\RegisterJob', 'sync', '{\\\"uuid\\\":\\\"b39e0f74-8fb5-4efe-b311-f23bd0e0c9ac\\\",\\\"displayName\\\":\\\"App\\\\Notifications\\\\RegisterJob\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\\\":3:{s:11:\\\\\"notifiables\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";a:1:{i:0;i:1723;}s:9:\\\\\"relations\\\\\";a:3:{i:0;s:16:\\\\\"userEmployerType\\\\\";i:1;s:29:\\\\\"userEmployerType.employeeRole\\\\\";i:2;s:6:\\\\\"wallet\\\\\";}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:12:\\\\\"notification\\\\\";O:29:\\\\\"App\\\\Notifications\\\\RegisterJob\\\\\":2:{s:7:\\\\\"\\u0000*\\u0000user\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";i:1723;s:9:\\\\\"relations\\\\\";a:3:{i:0;s:16:\\\\\"userEmployerType\\\\\";i:1;s:29:\\\\\"userEmployerType.employeeRole\\\\\";i:2;s:6:\\\\\"wallet\\\\\";}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:2:\\\\\"id\\\\\";s:36:\\\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\\\";}s:8:\\\\\"channels\\\\\";a:1:{i:0;s:4:\\\\\"mail\\\\\";}}\\\"}}', '2025-08-12 09:10:31', '2025-08-12 09:10:31')", "type": "query", "params": [], "bindings": ["", "App\\Notifications\\RegisterJob", "sync", "{\"uuid\":\"b39e0f74-8fb5-4efe-b311-f23bd0e0c9ac\",\"displayName\":\"App\\\\Notifications\\\\RegisterJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1723;}s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:29:\\\"App\\\\Notifications\\\\RegisterJob\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1723;s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"}}", "2025-08-12 09:10:31", "2025-08-12 09:10:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.9360402, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:74", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=74", "ajax": false, "filename": "AppServiceProvider.php", "line": "74"}, "connection": "hri_recland_product", "explain": null, "start_percent": 19.939, "width_percent": 0.269}, {"sql": "select * from `users` where `users`.`id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.940057, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "63437c517f2707deabb03f652ca40eb52e17c2b648a20bb088cf0d4128d32897"}, "start_percent": 20.208, "width_percent": 0.214}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.943451, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 20.421, "width_percent": 0.142}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.945066, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 20.564, "width_percent": 0.063}, {"sql": "select * from `wallets` where `wallets`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.946312, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` in (1723)", "hash": "57f6bc3070f1e11f38e8fd8b803f21cdf8a8cd6739d55c7b03d1fa2e8385e760"}, "start_percent": 20.627, "width_percent": 1.026}, {"sql": "select * from `users` where `users`.`id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.951315, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:80", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=80", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "80"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` in (1723)", "hash": "9eaf2ffee63eb4897a31941b87f7bd688dcf5d29a7179ad3514838fd0c4118ea"}, "start_percent": 21.653, "width_percent": 0.105}, {"sql": "insert into `email_logs` (`from`, `to`, `cc`, `subject`, `html_content`, `hash`, `status`, `updated_at`, `created_at`) values ('[\\\"<EMAIL>\\\"]', '[\\\"<EMAIL>\\\"]', '[]', '[Re<PERSON>land] [TẠO JOB MỚI THÀNH CÔNG]', '<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, \\'Helvetica Neue\\', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http://recland.local/frontend/assets_v2/images/graphics/logo-250.png?v=689aa298e8c36\\\">\\r\\n                        </div>\\r\\n                    </td>\\r\\n                </tr>\\r\\n            </table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=689aa298e8c48);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            </div>\\r\\n                                    <div>\\r\\n                                                                            </div>\\r\\n\\r\\n                                </div>\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                    </table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Xin chào, Hậu Test\\r\\n        <br>\\r\\n        Recland thông báo job của bạn đã được tạo mới thành công.\\r\\n        <p><b>Trân trọng,</b></p>\\r\\n        <p><i>Đội ngũ Recland.</i></p>\\r\\n    </div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"></div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Hello, Hậu Test\\r\\n        <br>\\r\\n        Recland announces that your job has been successfully created.\\r\\n        <p><b>Best regards,</b></p>\\r\\n        <p><i>Recland team.</i></p>\\r\\n    </div>\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=689aa298e8c76\\\"></a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=689aa298e8c81\\\"></a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=689aa298e8c8c\\\"></a>\\r\\n                                        </div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            © 2022 Recland.co</p>\\r\\n                                    </div>\\r\\n                                </div>\\r\\n\\r\\n                            </td>\\r\\n                        </tr>\\r\\n                    </table>\\r\\n                </div>\\r\\n            </div>\\r\\n        </div>\\r\\n    </center>\\r\\n</body>\\r\\n\\r\\n</html>\\r\\n', '12d82a386e28a0a0ed26b170edfef643', 0, '2025-08-12 09:10:33', '2025-08-12 09:10:33')", "type": "query", "params": [], "bindings": ["[\"<EMAIL>\"]", "[\"<EMAIL>\"]", "[]", "[Re<PERSON>land] [TẠO JOB MỚI THÀNH CÔNG]", "<!DOCTYPE html>\r\n<html>\r\n\r\n<body width=\"100%\"\r\n    style=\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\">\r\n    <center style=\"width: 100%; background-color: #f1f1f1;\">\r\n        <div style=\"max-width: 600px; margin: 0 auto;\">\r\n            <table align=\"center\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\"\r\n                style=\"margin: auto;\">\r\n                <tr>\r\n                    <td>\r\n                        <div style=\"text-align: center;padding: 25px 0;background: #FCFCFE;\">\r\n                            <img style=\"max-width: 100%\"\r\n                                src=\"http://recland.local/frontend/assets_v2/images/graphics/logo-250.png?v=689aa298e8c36\">\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </table>\r\n            <div style=\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\">\r\n                <div\r\n                    style=\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\r\n                background-image: url(http://recland.local/frontend/asset/images/template-email/background.png?v=689aa298e8c48);\r\n                background-repeat: no-repeat;background-size: 100%;\">\r\n                    <table>\r\n                        <tr>\r\n                            <td>\r\n                                <div\r\n                                    style=\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\">\r\n                                    <div style=\"margin-bottom: 14px\">\r\n                                                                            </div>\r\n                                    <div>\r\n                                                                            </div>\r\n\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                    </table>\r\n                    <table style=\"width: 100%\" role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"\r\n                        width=\"100%\">\r\n                        <tr>\r\n                            <td>\r\n                                    <div\r\n        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">\r\n        Xin chào, Hậu Test\r\n        <br>\r\n        Recland thông báo job của bạn đã được tạo mới thành công.\r\n        <p><b>Trân trọng,</b></p>\r\n        <p><i>Đội ngũ Recland.</i></p>\r\n    </div>\r\n    <div style=\"border: 5px solid #F7F7F7;\"></div>\r\n    <div\r\n        style=\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\">\r\n        Hello, Hậu Test\r\n        <br>\r\n        Recland announces that your job has been successfully created.\r\n        <p><b>Best regards,</b></p>\r\n        <p><i>Recland team.</i></p>\r\n    </div>\r\n                            </td>\r\n                        </tr>\r\n                        <tr>\r\n                            <td>\r\n                                <div style=\"padding:12px 0\">\r\n                                    <div\r\n                                        style=\"background-image: url(http://recland.local/frontend/asset/images/template-email/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\">\r\n                                        <div style=\"margin-bottom: 12px;text-align: center\">\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-twitter.png?v=689aa298e8c76\"></a>\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-facebook.png?v=689aa298e8c81\"></a>\r\n                                            <a style=\"padding-left: 16px;padding-right: 16px\" href=\"#\"><img\r\n                                                    src=\"http://recland.local/frontend/asset/images/template-email/icon-instagram.png?v=689aa298e8c8c\"></a>\r\n                                        </div>\r\n                                        <p\r\n                                            style=\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\">\r\n                                            Nền tảng tạo ra cơ hội kiếm tiền dành cho HR Freelancer</p>\r\n                                        <p\r\n                                            style=\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\">\r\n                                            © 2022 Recland.co</p>\r\n                                    </div>\r\n                                </div>\r\n\r\n                            </td>\r\n                        </tr>\r\n                    </table>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </center>\r\n</body>\r\n\r\n</html>\r\n", "12d82a386e28a0a0ed26b170edfef643", 0, "2025-08-12 09:10:33", "2025-08-12 09:10:33"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Jobs/CreateEmailLog.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\CreateEmailLog.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1754964633.0232038, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "CreateEmailLog.php:68", "source": {"index": 21, "namespace": null, "name": "app/Jobs/CreateEmailLog.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\CreateEmailLog.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FJobs%2FCreateEmailLog.php&line=68", "ajax": false, "filename": "CreateEmailLog.php", "line": "68"}, "connection": "hri_recland_product", "explain": null, "start_percent": 21.759, "width_percent": 0.206}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Jobs\\UpdateEmailLogStatus', 'sync', '{\\\"uuid\\\":\\\"eacdcac8-606e-4bcb-85d6-cdc0d252daca\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\",\\\"command\\\":\\\"O:29:\\\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\\\":1:{s:5:\\\\\"email\\\\\";O:28:\\\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\\\":6:{i:0;N;i:1;N;i:2;s:5635:\\\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\\\"100%\\\\\"\\r\\n    style=\\\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, \\'Helvetica Neue\\', Helvetica, sans-serif\\\\\">\\r\\n    <center style=\\\\\"width: 100%; background-color: #f1f1f1;\\\\\">\\r\\n        <div style=\\\\\"max-width: 600px; margin: 0 auto;\\\\\">\\r\\n            <table align=\\\\\"center\\\\\" role=\\\\\"presentation\\\\\" cellspacing=\\\\\"0\\\\\" cellpadding=\\\\\"0\\\\\" border=\\\\\"0\\\\\" width=\\\\\"100%\\\\\"\\r\\n                style=\\\\\"margin: auto;\\\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\\\">\\r\\n                            <img style=\\\\\"max-width: 100%\\\\\"\\r\\n                                src=\\\\\"http:\\/\\/recland.local\\/frontend\\/assets_v2\\/images\\/graphics\\/logo-250.png?v=689aa298e8c36\\\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\\\">\\r\\n                <div\\r\\n                    style=\\\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689aa298e8c48);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\\\">\\r\\n                                    <div style=\\\\\"margin-bottom: 14px\\\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\\\"width: 100%\\\\\" role=\\\\\"presentation\\\\\" cellspacing=\\\\\"0\\\\\" cellpadding=\\\\\"0\\\\\" border=\\\\\"0\\\\\"\\r\\n                        width=\\\\\"100%\\\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\">\\r\\n        Xin ch\\u00e0o, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland th\\u00f4ng b\\u00e1o job c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c t\\u1ea1o m\\u1edbi th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\\\"border: 5px solid #F7F7F7;\\\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\\\">\\r\\n        Hello, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland announces that your job has been successfully created.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\\\"padding:12px 0\\\\\">\\r\\n                                    <div\\r\\n                                        style=\\\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\\\">\\r\\n                                        <div style=\\\\\"margin-bottom: 12px;text-align: center\\\\\">\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689aa298e8c76\\\\\"><\\/a>\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689aa298e8c81\\\\\"><\\/a>\\r\\n                                            <a style=\\\\\"padding-left: 16px;padding-right: 16px\\\\\" href=\\\\\"#\\\\\"><img\\r\\n                                                    src=\\\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689aa298e8c8c\\\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\\\";i:3;s:5:\\\\\"utf-8\\\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\\\":2:{s:46:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\\\";a:4:{s:4:\\\\\"from\\\\\";a:1:{i:0;O:47:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:4:\\\\\"From\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:58:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\\\";a:1:{i:0;O:30:\\\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\\\":2:{s:39:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\\\";s:18:\\\\\"<EMAIL>\\\\\";s:36:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\\\";s:7:\\\\\"RECLAND\\\\\";}}}}s:2:\\\\\"to\\\\\";a:1:{i:0;O:47:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:2:\\\\\"To\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:58:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\\\";a:1:{i:0;O:30:\\\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\\\":2:{s:39:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\\\";s:21:\\\\\"<EMAIL>\\\\\";s:36:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\\\";s:0:\\\\\"\\\\\";}}}}s:7:\\\\\"subject\\\\\";a:1:{i:0;O:48:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:7:\\\\\"Subject\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:55:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\\\";s:40:\\\\\"[Recland] [T\\u1ea0O JOB M\\u1edaI TH\\u00c0NH C\\u00d4NG]\\\\\";}}s:17:\\\\\"x-original-emails\\\\\";a:1:{i:0;O:48:\\\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\\\":5:{s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\\\";s:17:\\\\\"X-Original-Emails\\\\\";s:56:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\\\";i:76;s:50:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\\\";N;s:53:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\\\";s:5:\\\\\"utf-8\\\\\";s:55:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\\\";s:2:\\\\\"[]\\\\\";}}}s:49:\\\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\\\";i:76;}i:1;N;}}}\\\"}}', '2025-08-12 09:10:34', '2025-08-12 09:10:34')", "type": "query", "params": [], "bindings": ["", "App\\Jobs\\UpdateEmailLogStatus", "sync", "{\"uuid\":\"eacdcac8-606e-4bcb-85d6-cdc0d252daca\",\"displayName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateEmailLogStatus\",\"command\":\"O:29:\\\"App\\\\Jobs\\\\UpdateEmailLogStatus\\\":1:{s:5:\\\"email\\\";O:28:\\\"Symfony\\\\Component\\\\Mime\\\\Email\\\":6:{i:0;N;i:1;N;i:2;s:5635:\\\"<!DOCTYPE html>\\r\\n<html>\\r\\n\\r\\n<body width=\\\"100%\\\"\\r\\n    style=\\\"margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif\\\">\\r\\n    <center style=\\\"width: 100%; background-color: #f1f1f1;\\\">\\r\\n        <div style=\\\"max-width: 600px; margin: 0 auto;\\\">\\r\\n            <table align=\\\"center\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\" width=\\\"100%\\\"\\r\\n                style=\\\"margin: auto;\\\">\\r\\n                <tr>\\r\\n                    <td>\\r\\n                        <div style=\\\"text-align: center;padding: 25px 0;background: #FCFCFE;\\\">\\r\\n                            <img style=\\\"max-width: 100%\\\"\\r\\n                                src=\\\"http:\\/\\/recland.local\\/frontend\\/assets_v2\\/images\\/graphics\\/logo-250.png?v=689aa298e8c36\\\">\\r\\n                        <\\/div>\\r\\n                    <\\/td>\\r\\n                <\\/tr>\\r\\n            <\\/table>\\r\\n            <div style=\\\"background-image: linear-gradient(192.45deg, #155969 18.03%, #DAE6E9 93.73%);\\\">\\r\\n                <div\\r\\n                    style=\\\"padding-top: 0;padding-left: 40px;padding-right: 40px;padding-bottom: 32px;\\r\\n                background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background.png?v=689aa298e8c48);\\r\\n                background-repeat: no-repeat;background-size: 100%;\\\">\\r\\n                    <table>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div\\r\\n                                    style=\\\"padding-top: 80px;height: 194px;text-align: center;font-style: normal;font-weight: 600;font-size: 26px;line-height: 31px;color: #FCFCFE;\\\">\\r\\n                                    <div style=\\\"margin-bottom: 14px\\\">\\r\\n                                                                            <\\/div>\\r\\n                                    <div>\\r\\n                                                                            <\\/div>\\r\\n\\r\\n                                <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                    <table style=\\\"width: 100%\\\" role=\\\"presentation\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" border=\\\"0\\\"\\r\\n                        width=\\\"100%\\\">\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Xin ch\\u00e0o, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland th\\u00f4ng b\\u00e1o job c\\u1ee7a b\\u1ea1n \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c t\\u1ea1o m\\u1edbi th\\u00e0nh c\\u00f4ng.\\r\\n        <p><b>Tr\\u00e2n tr\\u1ecdng,<\\/b><\\/p>\\r\\n        <p><i>\\u0110\\u1ed9i ng\\u0169 Recland.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n    <div style=\\\"border: 5px solid #F7F7F7;\\\"><\\/div>\\r\\n    <div\\r\\n        style=\\\"border-bottom: 1px solid #DEE4F5;background: #FCFCFE;min-height: 251px;padding: 40px 40px 18px;font-style: normal;font-weight: 400;font-size: 14px;line-height: 19px;color: #394860\\\">\\r\\n        Hello, H\\u1eadu Test\\r\\n        <br>\\r\\n        Recland announces that your job has been successfully created.\\r\\n        <p><b>Best regards,<\\/b><\\/p>\\r\\n        <p><i>Recland team.<\\/i><\\/p>\\r\\n    <\\/div>\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                        <tr>\\r\\n                            <td>\\r\\n                                <div style=\\\"padding:12px 0\\\">\\r\\n                                    <div\\r\\n                                        style=\\\"background-image: url(http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/background-footer.png?v=689aa298e8c6a);background-position: bottom left;background-repeat: no-repeat;text-align: center;background-color: #FCFCFE;padding-top: 22px;padding-bottom: 12px\\\">\\r\\n                                        <div style=\\\"margin-bottom: 12px;text-align: center\\\">\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-twitter.png?v=689aa298e8c76\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-facebook.png?v=689aa298e8c81\\\"><\\/a>\\r\\n                                            <a style=\\\"padding-left: 16px;padding-right: 16px\\\" href=\\\"#\\\"><img\\r\\n                                                    src=\\\"http:\\/\\/recland.local\\/frontend\\/asset\\/images\\/template-email\\/icon-instagram.png?v=689aa298e8c8c\\\"><\\/a>\\r\\n                                        <\\/div>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 8px;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #17677B;\\\">\\r\\n                                            N\\u1ec1n t\\u1ea3ng t\\u1ea1o ra c\\u01a1 h\\u1ed9i ki\\u1ebfm ti\\u1ec1n d\\u00e0nh cho HR Freelancer<\\/p>\\r\\n                                        <p\\r\\n                                            style=\\\"margin-top: 0;margin-bottom: 0;font-style: normal;font-weight: 600;font-size: 10px;line-height: 16px;text-align: center;color: #3C4455;\\\">\\r\\n                                            \\u00a9 2022 Recland.co<\\/p>\\r\\n                                    <\\/div>\\r\\n                                <\\/div>\\r\\n\\r\\n                            <\\/td>\\r\\n                        <\\/tr>\\r\\n                    <\\/table>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/center>\\r\\n<\\/body>\\r\\n\\r\\n<\\/html>\\r\\n\\\";i:3;s:5:\\\"utf-8\\\";i:4;a:0:{}i:5;a:2:{i:0;O:37:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\\":2:{s:46:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000headers\\\";a:4:{s:4:\\\"from\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:4:\\\"From\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:18:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:7:\\\"RECLAND\\\";}}}}s:2:\\\"to\\\";a:1:{i:0;O:47:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:2:\\\"To\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:58:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\MailboxListHeader\\u0000addresses\\\";a:1:{i:0;O:30:\\\"Symfony\\\\Component\\\\Mime\\\\Address\\\":2:{s:39:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000address\\\";s:21:\\\"<EMAIL>\\\";s:36:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Address\\u0000name\\\";s:0:\\\"\\\";}}}}s:7:\\\"subject\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:7:\\\"Subject\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:40:\\\"[Recland] [T\\u1ea0O JOB M\\u1edaI TH\\u00c0NH C\\u00d4NG]\\\";}}s:17:\\\"x-original-emails\\\";a:1:{i:0;O:48:\\\"Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\\":5:{s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000name\\\";s:17:\\\"X-Original-Emails\\\";s:56:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lineLength\\\";i:76;s:50:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000lang\\\";N;s:53:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\AbstractHeader\\u0000charset\\\";s:5:\\\"utf-8\\\";s:55:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\UnstructuredHeader\\u0000value\\\";s:2:\\\"[]\\\";}}}s:49:\\\"\\u0000Symfony\\\\Component\\\\Mime\\\\Header\\\\Headers\\u0000lineLength\\\";i:76;}i:1;N;}}}\"}}", "2025-08-12 09:10:34", "2025-08-12 09:10:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.846086, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:74", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=74", "ajax": false, "filename": "AppServiceProvider.php", "line": "74"}, "connection": "hri_recland_product", "explain": null, "start_percent": 21.964, "width_percent": 0.195}, {"sql": "select * from `email_logs` where `hash` = '1f256ac9dcb7bc2317c51047224616c5' and `created_at` >= '2025-08-12 09:05:34'", "type": "query", "params": [], "bindings": ["1f256ac9dcb7bc2317c51047224616c5", "2025-08-12 09:05:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Jobs/UpdateEmailLogStatus.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\UpdateEmailLogStatus.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.849752, "duration": 0.27532999999999996, "duration_str": "275ms", "memory": 0, "memory_str": null, "filename": "UpdateEmailLogStatus.php:59", "source": {"index": 14, "namespace": null, "name": "app/Jobs/UpdateEmailLogStatus.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Jobs\\UpdateEmailLogStatus.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FJobs%2FUpdateEmailLogStatus.php&line=59", "ajax": false, "filename": "UpdateEmailLogStatus.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `email_logs` where `hash` = ? and `created_at` >= ?", "hash": "5fcd9ab8c1f6899f94c237b8761515a907fe3390dc4c3f684138e39cf25a220e"}, "start_percent": 22.16, "width_percent": 72.616}, {"sql": "insert into `job_logs` (`job_id`, `name`, `queue`, `payload`, `updated_at`, `created_at`) values ('', 'App\\Notifications\\RegisterJob', 'sync', '{\\\"uuid\\\":\\\"e44d6cc0-a36f-423c-b2d9-56726f73f54d\\\",\\\"displayName\\\":\\\"App\\\\Notifications\\\\RegisterJob\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\\\":3:{s:11:\\\\\"notifiables\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";a:1:{i:0;i:1723;}s:9:\\\\\"relations\\\\\";a:3:{i:0;s:16:\\\\\"userEmployerType\\\\\";i:1;s:29:\\\\\"userEmployerType.employeeRole\\\\\";i:2;s:6:\\\\\"wallet\\\\\";}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:12:\\\\\"notification\\\\\";O:29:\\\\\"App\\\\Notifications\\\\RegisterJob\\\\\":2:{s:7:\\\\\"\\u0000*\\u0000user\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":5:{s:5:\\\\\"class\\\\\";s:15:\\\\\"App\\\\Models\\\\User\\\\\";s:2:\\\\\"id\\\\\";i:1723;s:9:\\\\\"relations\\\\\";a:3:{i:0;s:16:\\\\\"userEmployerType\\\\\";i:1;s:29:\\\\\"userEmployerType.employeeRole\\\\\";i:2;s:6:\\\\\"wallet\\\\\";}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";s:15:\\\\\"collectionClass\\\\\";N;}s:2:\\\\\"id\\\\\";s:36:\\\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\\\";}s:8:\\\\\"channels\\\\\";a:1:{i:0;s:8:\\\\\"database\\\\\";}}\\\"}}', '2025-08-12 09:10:35', '2025-08-12 09:10:35')", "type": "query", "params": [], "bindings": ["", "App\\Notifications\\RegisterJob", "sync", "{\"uuid\":\"e44d6cc0-a36f-423c-b2d9-56726f73f54d\",\"displayName\":\"App\\\\Notifications\\\\RegisterJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1723;}s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:29:\\\"App\\\\Notifications\\\\RegisterJob\\\":2:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1723;s:9:\\\"relations\\\";a:3:{i:0;s:16:\\\"userEmployerType\\\";i:1;s:29:\\\"userEmployerType.employeeRole\\\";i:2;s:6:\\\"wallet\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"}}", "2025-08-12 09:10:35", "2025-08-12 09:10:35"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SyncQueue.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 229}], "start": **********.16676, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:74", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Providers\\AppServiceProvider.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FProviders%2FAppServiceProvider.php&line=74", "ajax": false, "filename": "AppServiceProvider.php", "line": "74"}, "connection": "hri_recland_product", "explain": null, "start_percent": 94.775, "width_percent": 0.145}, {"sql": "select * from `users` where `users`.`id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.168478, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` = ? limit 1", "hash": "63437c517f2707deabb03f652ca40eb52e17c2b648a20bb088cf0d4128d32897"}, "start_percent": 94.92, "width_percent": 0.171}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.170405, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 95.092, "width_percent": 0.203}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.1722682, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 95.295, "width_percent": 0.076}, {"sql": "select * from `wallets` where `wallets`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.1736362, "duration": 0.00608, "duration_str": "6.08ms", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:108", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=108", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "108"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` in (1723)", "hash": "57f6bc3070f1e11f38e8fd8b803f21cdf8a8cd6739d55c7b03d1fa2e8385e760"}, "start_percent": 95.371, "width_percent": 1.604}, {"sql": "select * from `users` where `users`.`id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 98}], "start": **********.180914, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:80", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=80", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "80"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` in (1723)", "hash": "9eaf2ffee63eb4897a31941b87f7bd688dcf5d29a7179ad3514838fd0c4118ea"}, "start_percent": 96.975, "width_percent": 0.132}, {"sql": "select `value`, `key` from `settings` where `key` like '%settings.vi.notification%'", "type": "query", "params": [], "bindings": ["%settings.vi.notification%"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/SettingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SettingService.php", "line": 49}, {"index": 15, "namespace": null, "name": "app/Notifications/RegisterJob.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Notifications\\RegisterJob.php", "line": 68}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 38}], "start": **********.2078679, "duration": 0.0061600000000000005, "duration_str": "6.16ms", "memory": 0, "memory_str": null, "filename": "SettingRepository.php:23", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSettingRepository.php&line=23", "ajax": false, "filename": "SettingRepository.php", "line": "23"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `value`, `key` from `settings` where `key` like ?", "hash": "03db4599941fbfcc5d5ce90af08233c929fd5fe673d9c15cc07ef3d9b7c55995"}, "start_percent": 97.107, "width_percent": 1.625}, {"sql": "select `value`, `key` from `settings` where `key` like '%settings.en.notification%'", "type": "query", "params": [], "bindings": ["%settings.en.notification%"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, {"index": 14, "namespace": null, "name": "app/Services/Frontend/SettingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SettingService.php", "line": 49}, {"index": 15, "namespace": null, "name": "app/Notifications/RegisterJob.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Notifications\\RegisterJob.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 38}], "start": **********.217419, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "SettingRepository.php:23", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SettingRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SettingRepository.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSettingRepository.php&line=23", "ajax": false, "filename": "SettingRepository.php", "line": "23"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `value`, `key` from `settings` where `key` like ?", "hash": "0013672069abff7e5ead156adf6ed0e3cdad8f11cd08bce35f44b435e246d43a"}, "start_percent": 98.731, "width_percent": 1.168}, {"sql": "insert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d', 'App\\Notifications\\RegisterJob', '{\\\"content_vi\\\":\\\"Nh\\u00e0 tuy\\u1ec3n d\\u1ee5ng \\u0111\\u00e3 t\\u1ea1o job m\\u1edbi th\\u00e0nh c\\u00f4ng.\\\",\\\"content_en\\\":\\\"The employer has successfully created a new job.\\\"}', '', 1723, 'App\\Models\\User', '2025-08-12 09:10:35', '2025-08-12 09:10:35')", "type": "query", "params": [], "bindings": ["88d4be9a-a6a0-4d04-bb31-5a8b6fa7e30d", "App\\Notifications\\RegisterJob", "{\"content_vi\":\"Nh\\u00e0 tuy\\u1ec3n d\\u1ee5ng \\u0111\\u00e3 t\\u1ea1o job m\\u1edbi th\\u00e0nh c\\u00f4ng.\",\"content_en\":\"The employer has successfully created a new job.\"}", null, 1723, "App\\Models\\User", "2025-08-12 09:10:35", "2025-08-12 09:10:35"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 106}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 101}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/ChannelManager.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php", "line": 54}], "start": **********.2251139, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseChannel.php:19", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FChannels%2FDatabaseChannel.php&line=19", "ajax": false, "filename": "DatabaseChannel.php", "line": "19"}, "connection": "hri_recland_product", "explain": null, "start_percent": 99.9, "width_percent": 0.1}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 340}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.26608, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EmployerController.php:340", "source": {"index": 8, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=340", "ajax": false, "filename": "EmployerController.php", "line": "340"}, "connection": "hri_recland_product", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\JobLog": {"created": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobLog.php&line=1", "ajax": false, "filename": "JobLog.php", "line": "?"}}, "App\\Models\\EmployerType": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\Job": {"retrieved": 1, "created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\JobTop": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobTop.php&line=1", "ajax": false, "filename": "JobTop.php", "line": "?"}}, "OwenIt\\Auditing\\Models\\Audit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FModels%2FAudit.php&line=1", "ajax": false, "filename": "Audit.php", "line": "?"}}, "App\\Models\\JobSeo": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobSeo.php&line=1", "ajax": false, "filename": "JobSeo.php", "line": "?"}}, "App\\Models\\JobMeta": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobMeta.php&line=1", "ajax": false, "filename": "JobMeta.php", "line": "?"}}, "App\\Models\\EmailLog": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmailLog.php&line=1", "ajax": false, "filename": "EmailLog.php", "line": "?"}}, "Illuminate\\Notifications\\DatabaseNotification": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}}, "count": 25, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 11, "retrieved": 14}}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "[Re<PERSON>land] [TẠO JOB MỚI THÀNH CÔNG]", "headers": "From: RECLAND <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: [Recland] =?utf-8?Q?=5BT=E1=BA=A0O?= JOB\r\n =?utf-8?Q?M=E1=BB=9AI_TH=C3=80NH_C=C3=94NG=5D?=\r\nX-Original-Emails: []\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/job/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:1 [\n    0 => \"toastr::messages\"\n  ]\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "PHPDEBUGBAR_STACK_DATA": "[]", "toastr::messages": "array:1 [\n  0 => array:4 [\n    \"type\" => \"success\"\n    \"title\" => \"Success\"\n    \"message\" => \"<PERSON><PERSON> lưu thành công bản ghi\"\n    \"options\" => []\n  ]\n]"}, "request": {"data": {"status": "302 Found", "full_url": "http://recland.local/employer/job/store", "action_name": "employer-store", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@storeJob", "uri": "POST employer/job/store", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@storeJob<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=335\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=335\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:335-354</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "6.24s", "peak_memory": "52MB", "response": "Redirect to http://recland.local/employer/job", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-302103589 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-302103589\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2145282419 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>coupon_code</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>collaborator_cost</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">L&#7853;p tr&#236;nh PHP</span>\"\n  \"<span class=sf-dump-key>expire_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">18/08/2025</span>\"\n  \"<span class=sf-dump-key>vacancies</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>career</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>skill</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>rank</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>salary_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">VND</span>\"\n  \"<span class=sf-dump-key>salary_min</span>\" => \"<span class=sf-dump-str title=\"8 characters\">10000000</span>\"\n  \"<span class=sf-dump-key>salary_currency_max</span>\" => \"<span class=sf-dump-str title=\"3 characters\">VND</span>\"\n  \"<span class=sf-dump-key>salary_max</span>\" => \"<span class=sf-dump-str title=\"8 characters\">20000000</span>\"\n  \"<span class=sf-dump-key>bonus_type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">cv</span>\"\n  \"<span class=sf-dump-key>bonus</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2000000</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">part-time</span>\"\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>area</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ha-noi</span>\"\n      \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"42 characters\">To&#224; AC, ng&#245; 78 Duy T&#226;n - C&#7847;u Gi&#7845;y - H&#224; N&#7897;i</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>jd_description</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&lt;p&gt;NEWBIE2025&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>jd_request</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&lt;p&gt;NEWBIE2025&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>jd_welfare</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&lt;p&gt;NEWBIE2025&lt;/p&gt;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145282419\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1516940778 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2399</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryMAevSAZOKefzwIaK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6IjFRTjdDNFdHSDJVQng3eTUyRWhjWHc9PSIsInZhbHVlIjoieUIva0o0c1dJTmN1M1EyandOMll0OXcrNGlwcFZYaEJWcjg0UU5leTZhemtmSjhBeXJVNk5kRFFORG00Wkp1bVd2Rk9BMVhqZ3huSlR2Y01KWXZ3dnVkMVRZbEx0bVNHNTBOTGUvbjRUeE51QWh4bjgwaUdZQmpyS1JmVWowQnciLCJtYWMiOiI3MTFiMDMzNjU2NjVjOGQ2NTgwYjg2ZjNmNjhlN2NiZTIxY2M0MDUyZDY0NDJkMzIzMjcyOWZlY2ZlZDhlY2FhIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InhQZHBXYzgyN0JIN2EwYVVzaDhFNGc9PSIsInZhbHVlIjoibm5PVDJ2dWE3QldlN2tuRVhEcHhEQy9FV2NjbFc1SWZkQ01vUlF6aSs3Wkg5V1V5UFNha3RZeXV0cWtzSGRmR2RWLzZOUE1xYzN3VTgxN0ozMlpWZ0Y2bHFxRVpmOVV6Q2F3b0MrTE9ZQm4ydWhtUmEwMWkvVVdXQ0ZqRmZLVUwiLCJtYWMiOiJhMjIyMjFmZWYxMTIyZjIwZmVkZDYyY2QwMjQ4MjgyMWZiZDY2ODIyYThjZDlhYWZiN2NiNmMxN2NhYjgyNTllIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j59$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516940778\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1172840310 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172840310\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-82935915 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 02:10:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://recland.local/employer/job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82935915\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1484191357 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">toastr::messages</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>toastr::messages</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#272;&#227; l&#432;u th&#224;nh c&#244;ng b&#7843;n ghi</span>\"\n      \"<span class=sf-dump-key>options</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484191357\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://recland.local/employer/job/store", "action_name": "employer-store", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@storeJob"}, "badge": "302 Found"}}