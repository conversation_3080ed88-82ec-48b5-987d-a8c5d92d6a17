{"__meta": {"id": "01K2DYCTZF3FYMD4FFAFYGJVMK", "datetime": "2025-08-12 08:30:26", "utime": **********.16038, "method": "GET", "uri": "/admin/job/1648/edit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 17, "messages": [{"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 21.94\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.06386, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)\\n-- \",\n    \"Time:\": 0.87\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.072367, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `job`.`id` = '1648' limit 1\\n-- \",\n    \"Time:\": 0.81\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.076224, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops`\\n-- \",\n    \"Time:\": 0.8\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.080644, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops` where `id` = '2' limit 1\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.08335, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_tops` where `id` = '2' limit 1\\n-- \",\n    \"Time:\": 0.27\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.087666, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `level_by_job_tops` where `group` = '1'\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.089491, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `job`.`id` = '1648' limit 1\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.09117, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_meta` where `job_id` = '1648' limit 1\\n-- \",\n    \"Time:\": 1.7\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.094316, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_seo` where `job_id` = '1648' limit 1\\n-- \",\n    \"Time:\": 2.64\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.098451, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `name` from `skills` order by `id` desc limit 50\\n-- \",\n    \"Time:\": 0.48\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.102646, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '84' limit 1\\n-- \",\n    \"Time:\": 0.37\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.104775, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `type` = 'employer' and `company_id` = '84'\\n-- \",\n    \"Time:\": 14.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.120232, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.121961, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `user_role` where `user_role`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.124566, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` where `job`.`id` = '1648' limit 1\\n-- \",\n    \"Time:\": 0.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.145098, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '84' limit 1\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.148271, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.606199, "end": **********.160418, "duration": 0.5542190074920654, "duration_str": "554ms", "measures": [{"label": "Booting", "start": **********.606199, "relative_start": 0, "end": **********.964565, "relative_end": **********.964565, "duration": 0.****************, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.964575, "relative_start": 0.*****************, "end": **********.16042, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.975986, "relative_start": 0.*****************, "end": **********.988663, "relative_end": **********.988663, "duration": 0.*****************, "duration_str": "12.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x admin.pages.job.edit", "param_count": null, "params": [], "start": **********.133618, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/job/edit.blade.phpadmin.pages.job.edit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Fpages%2Fjob%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.job.edit"}, {"name": "1x admin.inc_layouts.datatable.table", "param_count": null, "params": [], "start": **********.149965, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/datatable/table.blade.phpadmin.inc_layouts.datatable.table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fdatatable%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.datatable.table"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.153045, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x admin.inc_layouts.side_bar", "param_count": null, "params": [], "start": **********.154224, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/side_bar.blade.phpadmin.inc_layouts.side_bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fside_bar.blade.php&line=1", "ajax": false, "filename": "side_bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.side_bar"}, {"name": "1x admin.inc_layouts.header", "param_count": null, "params": [], "start": **********.155652, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/header.blade.phpadmin.inc_layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.header"}, {"name": "1x admin.inc_layouts.footer", "param_count": null, "params": [], "start": **********.156339, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/footer.blade.phpadmin.inc_layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.footer"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.157771, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET admin/job/{job}/edit", "middleware": "web, check-admin, check-role", "as": "job.edit", "controller": "App\\Http\\Controllers\\Admin\\JobController@edit<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/JobController.php:84-133</a>"}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04677, "accumulated_duration_str": "46.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.042166, "duration": 0.02194, "duration_str": "21.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 0, "width_percent": 46.91}, {"sql": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, {"index": 19, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 20, "namespace": "middleware", "name": "check-role", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRole.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 22, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 21}], "start": **********.071561, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "PermissionService.php:19", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FAdmin%2FPermissionService.php&line=19", "ajax": false, "filename": "PermissionService.php", "line": "19"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "hash": "09b63fe1be07508dfb18b73a990c5ee0249dd740fb6501945ccdb8043c3b971c"}, "start_percent": 46.91, "width_percent": 1.86}, {"sql": "select * from `job` where `job`.`id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 156}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 87}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0755801, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `job`.`id` = ? limit 1", "hash": "f88a85fef5dc70adb996a33ffa653271c0e40e6e958eebc918f2a3cfdbaba770"}, "start_percent": 48.771, "width_percent": 1.732}, {"sql": "select * from `job_tops`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 93}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.079909, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "JobController.php:93", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=93", "ajax": false, "filename": "JobController.php", "line": "93"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops`", "hash": "e483b570db4a4d95eef34975f1de4502d417ef682ca65f1939c31565ac3f693d"}, "start_percent": 50.502, "width_percent": 1.71}, {"sql": "select * from `job_tops` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 94}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0830219, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "JobController.php:94", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=94", "ajax": false, "filename": "JobController.php", "line": "94"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops` where `id` = ? limit 1", "hash": "1bfd90076618dd93defc42058a65a53d6e3761f1201d91e16fa7f1f695c75442"}, "start_percent": 52.213, "width_percent": 0.834}, {"sql": "select * from `job_tops` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 170}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.08746, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingService.php:170", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 170}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FWareHouseCvSellingService.php&line=170", "ajax": false, "filename": "WareHouseCvSellingService.php", "line": "170"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_tops` where `id` = ? limit 1", "hash": "1bfd90076618dd93defc42058a65a53d6e3761f1201d91e16fa7f1f695c75442"}, "start_percent": 53.047, "width_percent": 0.577}, {"sql": "select * from `level_by_job_tops` where `group` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/LevelByJobTopRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\LevelByJobTopRepository.php", "line": 21}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseCvSellingService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseCvSellingService.php", "line": 172}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.089225, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "LevelByJobTopRepository.php:21", "source": {"index": 14, "namespace": null, "name": "app/Repositories/LevelByJobTopRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\LevelByJobTopRepository.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FLevelByJobTopRepository.php&line=21", "ajax": false, "filename": "LevelByJobTopRepository.php", "line": "21"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `level_by_job_tops` where `group` = ?", "hash": "f995fef5746d1b596de4a0412d67262f529d43944a2f29a7712d18bf19c14e20"}, "start_percent": 53.624, "width_percent": 0.706}, {"sql": "select * from `job` where `job`.`id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 156}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 100}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0907922, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `job`.`id` = ? limit 1", "hash": "f88a85fef5dc70adb996a33ffa653271c0e40e6e958eebc918f2a3cfdbaba770"}, "start_percent": 54.33, "width_percent": 0.941}, {"sql": "select * from `job_meta` where `job_id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobMetaRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobMetaRepository.php", "line": 13}, {"index": 16, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 304}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 101}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.092678, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "JobMetaRepository.php:13", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobMetaRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobMetaRepository.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobMetaRepository.php&line=13", "ajax": false, "filename": "JobMetaRepository.php", "line": "13"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_meta` where `job_id` = ? limit 1", "hash": "09032322ddf5999d1ad23948334282f93608772864355fcc886b4ea5b140dd64"}, "start_percent": 55.27, "width_percent": 3.635}, {"sql": "select * from `job_seo` where `job_id` = '1648' limit 1", "type": "query", "params": [], "bindings": ["1648"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobSeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobSeoRepository.php", "line": 13}, {"index": 16, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 309}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.095874, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "JobSeoRepository.php:13", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobSeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobSeoRepository.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobSeoRepository.php&line=13", "ajax": false, "filename": "JobSeoRepository.php", "line": "13"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_seo` where `job_id` = ? limit 1", "hash": "8f1dae58f00721492063c9782b980990ec969f0f23c92e92ca2b4119e6222f03"}, "start_percent": 58.905, "width_percent": 5.645}, {"sql": "select `name` from `skills` order by `id` desc limit 50", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/SkillRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SkillRepository.php", "line": 64}, {"index": 14, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 527}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 172}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.102229, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "SkillRepository.php:64", "source": {"index": 13, "namespace": null, "name": "app/Repositories/SkillRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SkillRepository.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSkillRepository.php&line=64", "ajax": false, "filename": "SkillRepository.php", "line": "64"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `name` from `skills` order by `id` desc limit 50", "hash": "39c8ce3e3ccb0f0611e4e603d54866b828dad39a2aac32d490aca8a5b82cb28f"}, "start_percent": 64.55, "width_percent": 1.026}, {"sql": "select * from `companies` where `companies`.`id` = 84 limit 1", "type": "query", "params": [], "bindings": [84], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Admin/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\CompanyService.php", "line": 163}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.104469, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? limit 1", "hash": "cc1297ee2bc563ebfe1a5606d32a0266e1723b55aa2725a781741e51d0fb88ab"}, "start_percent": 65.576, "width_percent": 0.791}, {"sql": "select * from `users` where `type` = 'employer' and `company_id` = 84", "type": "query", "params": [], "bindings": ["employer", 84], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 177}, {"index": 15, "namespace": null, "name": "app/Services/Admin/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\CompanyService.php", "line": 164}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.106047, "duration": 0.01425, "duration_str": "14.25ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:177", "source": {"index": 14, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=177", "ajax": false, "filename": "UserRepository.php", "line": "177"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `type` = ? and `company_id` = ?", "hash": "50e2651ef65fee1e6bfdfa38a3165d3eef3b4a87f8f89f45d596bfd1bbd1c2f5"}, "start_percent": 66.367, "width_percent": 30.468}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, {"index": 17, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 156}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 117}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1216931, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:96", "source": {"index": 16, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=96", "ajax": false, "filename": "UserRepository.php", "line": "96"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 96.836, "width_percent": 0.727}, {"sql": "select * from `user_role` where `user_role`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, {"index": 22, "namespace": null, "name": "app/Services/Admin/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\UserService.php", "line": 156}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 117}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1243799, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "UserRepository.php:96", "source": {"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=96", "ajax": false, "filename": "UserRepository.php", "line": "96"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `user_role` where `user_role`.`user_id` in (1723)", "hash": "9fca1e5a30e20c1de59afd1eeeb503483febe4f0420eb57dd58fb1bd405df992"}, "start_percent": 97.563, "width_percent": 0.535}, {"sql": "select * from `job` where `job`.`id` = 1648 limit 1", "type": "query", "params": [], "bindings": [1648], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/JobSeo.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\JobSeo.php", "line": 35}, {"index": 26, "namespace": "view", "name": "admin.pages.job.edit", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/job/edit.blade.php", "line": 725}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.1446838, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "JobSeo.php:35", "source": {"index": 20, "namespace": null, "name": "app/Models/JobSeo.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\JobSeo.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobSeo.php&line=35", "ajax": false, "filename": "JobSeo.php", "line": "35"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` where `job`.`id` = ? limit 1", "hash": "47559d9dc6d9934ac24ab049bef05e7c7b62e130d8bb931e50b6730076cdfb30"}, "start_percent": 98.097, "width_percent": 1.197}, {"sql": "select * from `companies` where `companies`.`id` = 84 limit 1", "type": "query", "params": [], "bindings": [84], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Models/JobSeo.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\JobSeo.php", "line": 35}, {"index": 27, "namespace": "view", "name": "admin.pages.job.edit", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/admin/pages/job/edit.blade.php", "line": 725}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.148008, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? limit 1", "hash": "cc1297ee2bc563ebfe1a5606d32a0266e1723b55aa2725a781741e51d0fb88ab"}, "start_percent": 99.294, "width_percent": 0.706}]}, "models": {"data": {"App\\Models\\JobTop": {"retrieved": 59, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobTop.php&line=1", "ajax": false, "filename": "JobTop.php", "line": "?"}}, "App\\Models\\LevelByJobTop": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FLevelByJobTop.php&line=1", "ajax": false, "filename": "LevelByJobTop.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Job": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\JobMeta": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobMeta.php&line=1", "ajax": false, "filename": "JobMeta.php", "line": "?"}}, "App\\Models\\JobSeo": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobSeo.php&line=1", "ajax": false, "filename": "JobSeo.php", "line": "?"}}}, "count": 80, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 80}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/jobs-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/job/1648/edit", "action_name": "job.edit", "controller_action": "App\\Http\\Controllers\\Admin\\JobController@edit", "uri": "GET admin/job/{job}/edit", "controller": "App\\Http\\Controllers\\Admin\\JobController@edit<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=84\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/JobController.php:84-133</a>", "middleware": "web, check-admin, check-role", "duration": "557ms", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-35084413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-35084413\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1788832738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1788832738\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://recland.local/admin/jobs-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVoTVBHSTFuQzdvcHRrWC9FbnIrRmc9PSIsInZhbHVlIjoiWm9URklLZCt0SGhGeTYrcEI4dXB3NHA3OXJ2eDA0L0tHeVN5d0ZOc3pKNVdOYXR3bWcyc3ZiUG43UDBQeWswRXoydS80c0lDOUV3aW9nMU1OclJFSUlMaTBKdzE4STM3RzZkRjB3MDhFaEtXRVpqMFZHM0Z3Vkl1Sk1reisya1kiLCJtYWMiOiIwZmNiMGU5Nzk1M2E3NzU0ZjVmMzhlNDlmMjg5NmY4ODMyOGIzYTU3MzFiOGE3M2M0Y2NkYmYzZWZmYTg5YTVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjZucEw5TEtsdUZjY1NFaWpTYWNqaGc9PSIsInZhbHVlIjoiTjZ4N01kK3I5RWx0ZE9WZU5aN3BZbURWMkpyang1bGZBcFNGaUVsbklnZStkZGVKVHlHcDh1TUF3a1VQeFUyeklBQ0hzS1E3dHRJamxjOVJUMXBsZnEzSVIzS05kYUc4T2o4UEVCK3FOYlJrTXVtdEsxeVY0SStpRUJMOForK0ciLCJtYWMiOiI2NzgzZmZmMGZkMGNlNjA3ZjMwYTI3N2FlOTBiZDE4NDJkM2QzMGI2Nzc4MTQ4OWEzODk5NjNhNjk1YWU4MDJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-*********8 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6n7BHowcS6we7JFkDGDZRBjg4hsvpTWKVgnVcqpE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********8\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2014502042 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:30:26 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014502042\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1035287172 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://recland.local/admin/jobs-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035287172\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/job/1648/edit", "action_name": "job.edit", "controller_action": "App\\Http\\Controllers\\Admin\\JobController@edit"}, "badge": null}}