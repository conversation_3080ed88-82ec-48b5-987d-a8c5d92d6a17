{"__meta": {"id": "01K2DY8B02P8Z1TQ96Y6FKKN53", "datetime": "2025-08-12 08:27:58", "utime": **********.723135, "method": "GET", "uri": "/employer/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 51, "messages": [{"message": "[08:27:58] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.34022, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.340564, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-manager-dashboard' limit 1\\n-- \",\n    \"Time:\": 16.62\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.388032, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/employer\\/dashboard', 'http:\\/\\/recland.local\\/employer', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962076$j18$l0$h0; XSRF-TOKEN=eyJpdiI6InZwdDNVeG1jdzQxc25BSHBsT0NDQVE9PSIsInZhbHVlIjoibDM1ZkNKZmEyc0lVVG9uRUhrQnc2YWd3SFJpSGJTdWg4K1hkQ3dFdGJOWVZIWEYxbUlDakJTSEhNY3VOeDF1VDVpaW9PZEVRSjJadC9SRENpU3F4azVWOUM3czZnS1lmbDNMU1ZLT2p3dnZCMlJKOG5McFN1bkl2QTdBdXZGNjIiLCJtYWMiOiIyZGM0OTFmZTBiYThiMWU0OTUyODEyMjllZTUwNDI2ZjM1ZmRkNmQ4NDFmNzBmMjAwMDYxY2QyYjcxNGE2YjYzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJhd1duMWU0b2JDWWhvdG1KTW0zQXc9PSIsInZhbHVlIjoiMWQwUHZwNmZ3bnJLeWJqMFdMTiszYm9tV1N3ZDJHNWFFQXgyUXlkcjZZMm41cGNMWFRjUkJscHVuVDd2UC9VYkR4UzB5VmZMOHhFMDdaUncyRktrL21QcHczemJMcTRxRkdIODhMaG81UkxUbHlYc1ZUaDdFY2tXdFlWNVErdWQiLCJtYWMiOiIwNjRhZTI4NmU2OWJlOTk3OGQ1MmFhMzMyMjVlNTIzNTIwYTQzNjhkMThkY2RiZmQwMzljOTNkNjg3OWNkMDZkIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:58', '2025-08-12 08:27:58')\\n-- \",\n    \"Time:\": 0.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.426219, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.59\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.429945, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.435337, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.43761, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 4.28\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.443566, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `is_active` = '1' and `employer_id` = '1723'\\n-- \",\n    \"Time:\": 7.7\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.453489, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `status` = '5' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1'\\n-- \",\n    \"Time:\": 3.81\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.458805, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `status` != '7' and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1'\\n-- \",\n    \"Time:\": 26.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.486993, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '0' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.83\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.490565, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '1' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.88\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.493929, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '2' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 2.04\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.497871, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '3' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.79\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.501167, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '4' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.8\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.504445, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '8' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.507702, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '5' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.510964, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1' and `status` = '6' and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc\\n-- \",\n    \"Time:\": 1.81\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.514256, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-manager-dashboard' limit 1\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.515916, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = '1723') and `is_active` = '1'\\n-- \",\n    \"Time:\": 21.23\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.538701, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `user_id` = '1723' limit 1\\n-- \",\n    \"Time:\": 1.83\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.541821, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `deposits` where `user_id` = '1723'\\n-- \",\n    \"Time:\": 0.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.543769, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `deposits` where `user_id` = '1723' order by `id` desc limit 10 offset 0\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.545254, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`amount`) as aggregate from `deposits` where `user_id` = '1723' and `created_at` between '2025-08-01 00:00:00' and '2025-08-31 23:59:59'\\n-- \",\n    \"Time:\": 0.5\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.595273, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`amount`) as aggregate from `deposits` where `user_id` = '1723' and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59'\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.598382, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '1723'\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.602414, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `warehouse_cv_selling_history_buys` where `user_id` = '1723' order by `id` desc limit 10 offset 0\\n-- \",\n    \"Time:\": 0.48\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.60567, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`id` in (18, 25) and `warehouse_cv_selling_buys`.`deleted_at` is null\\n-- \",\n    \"Time:\": 0.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.61249, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`id` in (1372, 50624) and `warehouse_cv_sellings`.`deleted_at` is null\\n-- \",\n    \"Time:\": 1.28\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.617924, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `warehouse_cvs` where `warehouse_cvs`.`id` in (2084, 57585)\\n-- \",\n    \"Time:\": 1.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.622018, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '1723' and `type` = '0' and year(`created_at`) = '2025' and month(`created_at`) = '08'\\n-- \",\n    \"Time:\": 0.34\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.623786, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = '1723' and `type` = '0' and year(`created_at`) = '2025'\\n-- \",\n    \"Time:\": 0.3\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.625516, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.645538, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.647755, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.649569, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.651099, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.652629, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.654186, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.655767, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `meta_data` where `meta_data`.`object_type` = 'App\\\\Models\\\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.658764, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.42\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.663626, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.688867, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.41\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.703351, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.706709, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.70824, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.70982, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.711335, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.712845, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.714359, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:58] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.38\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.715887, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754962077.971487, "end": **********.723219, "duration": 0.7517318725585938, "duration_str": "752ms", "measures": [{"label": "Booting", "start": 1754962077.971487, "relative_start": 0, "end": **********.318192, "relative_end": **********.318192, "duration": 0.*****************, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.318203, "relative_start": 0.*****************, "end": **********.723222, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "405ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.329448, "relative_start": 0.*****************, "end": **********.336673, "relative_end": **********.336673, "duration": 0.*****************, "duration_str": "7.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x frontend.pages.employer.dashboard", "param_count": null, "params": [], "start": **********.637881, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/dashboard.blade.phpfrontend.pages.employer.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.dashboard"}, {"name": "1x frontend.layouts.employer.app", "param_count": null, "params": [], "start": **********.642637, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/app.blade.phpfrontend.layouts.employer.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.employer.app"}, {"name": "2x frontend.layouts.user.avatar", "param_count": null, "params": [], "start": **********.643425, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/user/avatar.blade.phpfrontend.layouts.user.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fuser%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.user.avatar"}, {"name": "2x frontend.layouts.employer.menu", "param_count": null, "params": [], "start": **********.644005, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.phpfrontend.layouts.employer.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.employer.menu"}, {"name": "1x frontend.layouts.modal.employer_confirm", "param_count": null, "params": [], "start": **********.657022, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.phpfrontend.layouts.modal.employer_confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmodal%2Femployer_confirm.blade.php&line=1", "ajax": false, "filename": "employer_confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.modal.employer_confirm"}, {"name": "1x frontend.layouts.login.app", "param_count": null, "params": [], "start": **********.659919, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/login/app.blade.phpfrontend.layouts.login.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Flogin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.login.app"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.660771, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.login.header", "param_count": null, "params": [], "start": **********.661312, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.phpfrontend.inc_layouts.login.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.header"}, {"name": "2x frontend.inc_layouts.notification.drop-notification", "param_count": null, "params": [], "start": **********.690133, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/notification/drop-notification.blade.phpfrontend.inc_layouts.notification.drop-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fnotification%2Fdrop-notification.blade.php&line=1", "ajax": false, "filename": "drop-notification.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.inc_layouts.notification.drop-notification"}, {"name": "1x frontend.inc_layouts.login.footer_v2", "param_count": null, "params": [], "start": **********.717299, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/footer_v2.blade.phpfrontend.inc_layouts.login.footer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Ffooter_v2.blade.php&line=1", "ajax": false, "filename": "footer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.footer_v2"}, {"name": "1x frontend.inc_layouts.login.modal_report", "param_count": null, "params": [], "start": **********.718252, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/modal_report.blade.phpfrontend.inc_layouts.login.modal_report", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fmodal_report.blade.php&line=1", "ajax": false, "filename": "modal_report.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.modal_report"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.719046, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.720535, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET employer/dashboard", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-manager-dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:217-278</a>"}, "queries": {"count": 49, "nb_statements": 49, "nb_visible_statements": 49, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11281000000000002, "accumulated_duration_str": "113ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-manager-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-manager-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.371655, "duration": 0.01662, "duration_str": "16.62ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "b5743e64e3255a2e102720b7b23123c44f032301e0af811d68647bb6b25f9e64"}, "start_percent": 0, "width_percent": 14.733}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/dashboard', 'http://recland.local/employer', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962076$j18$l0$h0; XSRF-TOKEN=eyJpdiI6InZwdDNVeG1jdzQxc25BSHBsT0NDQVE9PSIsInZhbHVlIjoibDM1ZkNKZmEyc0lVVG9uRUhrQnc2YWd3SFJpSGJTdWg4K1hkQ3dFdGJOWVZIWEYxbUlDakJTSEhNY3VOeDF1VDVpaW9PZEVRSjJadC9SRENpU3F4azVWOUM3czZnS1lmbDNMU1ZLT2p3dnZCMlJKOG5McFN1bkl2QTdBdXZGNjIiLCJtYWMiOiIyZGM0OTFmZTBiYThiMWU0OTUyODEyMjllZTUwNDI2ZjM1ZmRkNmQ4NDFmNzBmMjAwMDYxY2QyYjcxNGE2YjYzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJhd1duMWU0b2JDWWhvdG1KTW0zQXc9PSIsInZhbHVlIjoiMWQwUHZwNmZ3bnJLeWJqMFdMTiszYm9tV1N3ZDJHNWFFQXgyUXlkcjZZMm41cGNMWFRjUkJscHVuVDd2UC9VYkR4UzB5VmZMOHhFMDdaUncyRktrL21QcHczemJMcTRxRkdIODhMaG81UkxUbHlYc1ZUaDdFY2tXdFlWNVErdWQiLCJtYWMiOiIwNjRhZTI4NmU2OWJlOTk3OGQ1MmFhMzMyMjVlNTIzNTIwYTQzNjhkMThkY2RiZmQwMzljOTNkNjg3OWNkMDZkIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:58', '2025-08-12 08:27:58')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/employer/dashboard", "http://recland.local/employer", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"cache-control\":[\"max-age=0\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962076$j18$l0$h0; XSRF-TOKEN=eyJpdiI6InZwdDNVeG1jdzQxc25BSHBsT0NDQVE9PSIsInZhbHVlIjoibDM1ZkNKZmEyc0lVVG9uRUhrQnc2YWd3SFJpSGJTdWg4K1hkQ3dFdGJOWVZIWEYxbUlDakJTSEhNY3VOeDF1VDVpaW9PZEVRSjJadC9SRENpU3F4azVWOUM3czZnS1lmbDNMU1ZLT2p3dnZCMlJKOG5McFN1bkl2QTdBdXZGNjIiLCJtYWMiOiIyZGM0OTFmZTBiYThiMWU0OTUyODEyMjllZTUwNDI2ZjM1ZmRkNmQ4NDFmNzBmMjAwMDYxY2QyYjcxNGE2YjYzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJhd1duMWU0b2JDWWhvdG1KTW0zQXc9PSIsInZhbHVlIjoiMWQwUHZwNmZ3bnJLeWJqMFdMTiszYm9tV1N3ZDJHNWFFQXgyUXlkcjZZMm41cGNMWFRjUkJscHVuVDd2UC9VYkR4UzB5VmZMOHhFMDdaUncyRktrL21QcHczemJMcTRxRkdIODhMaG81UkxUbHlYc1ZUaDdFY2tXdFlWNVErdWQiLCJtYWMiOiIwNjRhZTI4NmU2OWJlOTk3OGQ1MmFhMzMyMjVlNTIzNTIwYTQzNjhkMThkY2RiZmQwMzljOTNkNjg3OWNkMDZkIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:27:58", "2025-08-12 08:27:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.425734, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 14.733, "width_percent": 0.496}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.429425, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 15.229, "width_percent": 0.523}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.4346468, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 15.752, "width_percent": 0.674}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.43736, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 16.426, "width_percent": 0.284}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 1723 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.439355, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ef913f8afc9f4d48f551727e02040c91d14047cbc5f6bb1c2d8490bb794fe689"}, "start_percent": 16.71, "width_percent": 3.794}, {"sql": "select count(*) as aggregate from `job` where `is_active` = 1 and `employer_id` = 1723", "type": "query", "params": [], "bindings": [1, 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 113}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 222}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.445858, "duration": 0.0077, "duration_str": "7.7ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:389", "source": {"index": 15, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 389}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=389", "ajax": false, "filename": "JobRepository.php", "line": "389"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `is_active` = ? and `employer_id` = ?", "hash": "45aced023ff853ff47c57e636a155498b907d7716397a9e1e2d6d81b5d3ffb1e"}, "start_percent": 20.504, "width_percent": 6.826}, {"sql": "select count(*) as aggregate from `submit_cvs` where `status` = 5 and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1", "type": "query", "params": [], "bindings": [5, 1723, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 627}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 227}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.45507, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `status` = ? and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ?", "hash": "bc61b6a81d2453db3f151008c5424e78fac62ebee622042a44eee8f85cfa5824"}, "start_percent": 27.329, "width_percent": 3.377}, {"sql": "select count(*) as aggregate from `submit_cvs` where `status` != 7 and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1", "type": "query", "params": [], "bindings": [7, 1723, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 627}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.460309, "duration": 0.02675, "duration_str": "26.75ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `status` != ? and exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ?", "hash": "153a9c6b56006da543065934c22e6f0341b002cd84fa9f89356dca3e8e002411"}, "start_percent": 30.706, "width_percent": 23.712}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 0 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 0, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.488803, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "fbc6e1750b663757c6141198862eb5c62e519df4bb0c1666f3d73606a3905650"}, "start_percent": 54.419, "width_percent": 1.622}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 1 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 1, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4921172, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "c27a3a5e98ae32f8f5e2da340adf0b8753e6ea6710fe5b9926ba589051561244"}, "start_percent": 56.041, "width_percent": 1.667}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 2 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 2, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.495901, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "b332bc6ed320848cae2b1802aed019e1ee3c623c81a590ff637eda5b205651fc"}, "start_percent": 57.708, "width_percent": 1.808}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 3 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 3, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.499445, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "397739bbdafecab68067dac96e6f2ab8ac4a58f388b76623d5124b4feb2186c8"}, "start_percent": 59.516, "width_percent": 1.587}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 4 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 4, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.502713, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "5a381a121610eff4c834d924b272fd780661f6b51e19c20fe578f3bf137aa9d6"}, "start_percent": 61.103, "width_percent": 1.596}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 8 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 8, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.505989, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "4f4fe38097b7f3faa72e8aa09de1d076a87747fbc5dbe853fb8f56d1a36ad98e"}, "start_percent": 62.698, "width_percent": 1.578}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 5 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 5, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.509252, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "2beebc8c46b4322de772f733eaf059bd14bed0034b3dadcf46e0a5a4089afddc"}, "start_percent": 64.276, "width_percent": 1.578}, {"sql": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1 and `status` = 6 and `created_at` >= '2025-08-01 00:00:00' and `created_at` <= '2025-08-31 23:59:59' group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "type": "query", "params": [], "bindings": [1723, 1, 6, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/WareHouseSubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WareHouseSubmitCvService.php", "line": 697}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.512516, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:117", "source": {"index": 14, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=117", "ajax": false, "filename": "SubmitCvRepository.php", "line": "117"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select (count(id)) as total, (DATE_FORMAT(created_at, '%d')) as day from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ? and `status` = ? and `created_at` >= ? and `created_at` <= ? group by DATE_FORMAT(created_at, '%d') order by DATE_FORMAT(created_at, '%d') asc", "hash": "de80fca9d37dd0402adc9645b24c032530c43a86d646056c1921f7ad591309b5"}, "start_percent": 65.854, "width_percent": 1.604}, {"sql": "select * from `seos` where `key` = 'employer-manager-dashboard' limit 1", "type": "query", "params": [], "bindings": ["employer-manager-dashboard"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 240}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.515594, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "b5743e64e3255a2e102720b7b23123c44f032301e0af811d68647bb6b25f9e64"}, "start_percent": 67.459, "width_percent": 0.346}, {"sql": "select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = 1723) and `is_active` = 1", "type": "query", "params": [], "bindings": [1723, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Admin/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\SubmitCvService.php", "line": 1153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 244}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.517541, "duration": 0.02123, "duration_str": "21.23ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=75", "ajax": false, "filename": "SubmitCvRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where exists (select * from `job` where `submit_cvs`.`job_id` = `job`.`id` and `employer_id` = ?) and `is_active` = ?", "hash": "a0a5f452a2a756d84f8798c96685ec598a76e231ccca3251a4b55c2f627ec6c3"}, "start_percent": 67.804, "width_percent": 18.819}, {"sql": "select * from `wallets` where `user_id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WalletRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WalletRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 248}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.540057, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "WalletRepository.php:28", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WalletRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WalletRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWalletRepository.php&line=28", "ajax": false, "filename": "WalletRepository.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `user_id` = ? limit 1", "hash": "ff0a5f7307a2744e387be663dd0573b2297be5a1753cdd5620f68e6716f8f8b8"}, "start_percent": 86.624, "width_percent": 1.622}, {"sql": "select count(*) as aggregate from `deposits` where `user_id` = 1723", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 250}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5435889, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DepositRepository.php:41", "source": {"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FDepositRepository.php&line=41", "ajax": false, "filename": "DepositRepository.php", "line": "41"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `deposits` where `user_id` = ?", "hash": "2c44155cea5a9a372bf5c9e8752eb75eaa22e87ac83ecc250c5a6260ea0f4503"}, "start_percent": 88.246, "width_percent": 0.222}, {"sql": "select * from `deposits` where `user_id` = 1723 order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 250}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5450091, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DepositRepository.php:41", "source": {"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FDepositRepository.php&line=41", "ajax": false, "filename": "DepositRepository.php", "line": "41"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `deposits` where `user_id` = ? order by `id` desc limit 10 offset 0", "hash": "16da3876bc6652f4ae3b9307df93c9d14b2e351e40917646d2dd242c20c9c4ea"}, "start_percent": 88.467, "width_percent": 0.275}, {"sql": "select sum(`amount`) as aggregate from `deposits` where `user_id` = 1723 and `created_at` between '2025-08-01 00:00:00' and '2025-08-31 23:59:59'", "type": "query", "params": [], "bindings": [1723, "2025-08-01 00:00:00", "2025-08-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 729}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.594935, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DepositRepository.php:46", "source": {"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FDepositRepository.php&line=46", "ajax": false, "filename": "DepositRepository.php", "line": "46"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`amount`) as aggregate from `deposits` where `user_id` = ? and `created_at` between ? and ?", "hash": "8f841252d3d776948fdb880640a8a8c6e28e302c51a532652f98e86c8fb49a46"}, "start_percent": 88.742, "width_percent": 0.443}, {"sql": "select sum(`amount`) as aggregate from `deposits` where `user_id` = 1723 and `created_at` between '2025-01-01 00:00:00' and '2025-12-31 23:59:59'", "type": "query", "params": [], "bindings": [1723, "2025-01-01 00:00:00", "2025-12-31 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 730}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.598127, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DepositRepository.php:46", "source": {"index": 15, "namespace": null, "name": "app/Repositories/DepositRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\DepositRepository.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FDepositRepository.php&line=46", "ajax": false, "filename": "DepositRepository.php", "line": "46"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`amount`) as aggregate from `deposits` where `user_id` = ? and `created_at` between ? and ?", "hash": "6237c4ae13b313d7d72b78bf4cb0ebfbf416844bed7f72dd05bac52f42eea7c3"}, "start_percent": 89.185, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = 1723", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 26}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 253}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.602177, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:44", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=44", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "44"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = ?", "hash": "9dc513d03387e718918cb2160e956797b09642634bf98ada68e088ad23fe6a6f"}, "start_percent": 89.54, "width_percent": 0.346}, {"sql": "select * from `warehouse_cv_selling_history_buys` where `user_id` = 1723 order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 26}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 253}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6053622, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:44", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=44", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "44"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `warehouse_cv_selling_history_buys` where `user_id` = ? order by `id` desc limit 10 offset 0", "hash": "367e40c58c362e8245b90215dc6da4d70df7695c10f096a2caf41481b34f9f4b"}, "start_percent": 89.886, "width_percent": 0.425}, {"sql": "select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`id` in (18, 25) and `warehouse_cv_selling_buys`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, {"index": 21, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 26}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 253}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.612122, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:44", "source": {"index": 20, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=44", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "44"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `warehouse_cv_selling_buys` where `warehouse_cv_selling_buys`.`id` in (18, 25) and `warehouse_cv_selling_buys`.`deleted_at` is null", "hash": "99e5b5ef57354dd3bd404e25f7b072a7e94a23b30d1111f89112a2d14cd3a72e"}, "start_percent": 90.311, "width_percent": 0.461}, {"sql": "select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`id` in (1372, 50624) and `warehouse_cv_sellings`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, {"index": 26, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 26}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 253}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.616797, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:44", "source": {"index": 25, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=44", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "44"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `warehouse_cv_sellings` where `warehouse_cv_sellings`.`id` in (1372, 50624) and `warehouse_cv_sellings`.`deleted_at` is null", "hash": "16af8627aa42a9aafb37c29dcb97d1c7a5f489e7855dff43afa99c17ae5893f3"}, "start_percent": 90.772, "width_percent": 1.135}, {"sql": "select * from `warehouse_cvs` where `warehouse_cvs`.`id` in (2084, 57585)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 30, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, {"index": 31, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 26}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 253}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.62069, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:44", "source": {"index": 30, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=44", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "44"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `warehouse_cvs` where `warehouse_cvs`.`id` in (2084, 57585)", "hash": "e22f1e0c5cb73017fe037addd57430a2bbe30d1339f9db0fbc58f7ba32341b9f"}, "start_percent": 91.907, "width_percent": 1.25}, {"sql": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = 1723 and `type` = 0 and year(`created_at`) = 2025 and month(`created_at`) = '08'", "type": "query", "params": [], "bindings": [1723, 0, 2025, "08"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 254}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.623526, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:56", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=56", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "56"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = ? and `type` = ? and year(`created_at`) = ? and month(`created_at`) = ?", "hash": "c60278864155c5306f84f2ca99477ee64705973df9887bfd6ff2a54e5c3d5eff"}, "start_percent": 93.157, "width_percent": 0.301}, {"sql": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = 1723 and `type` = 0 and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": [1723, 0, 2025], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/WarehouseCvSellingHistoryBuyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\WarehouseCvSellingHistoryBuyService.php", "line": 36}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 255}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.625292, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "WareHouseCvSellingHistoryBuyRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/WareHouseCvSellingHistoryBuyRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\WareHouseCvSellingHistoryBuyRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FWareHouseCvSellingHistoryBuyRepository.php&line=75", "ajax": false, "filename": "WareHouseCvSellingHistoryBuyRepository.php", "line": "75"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select sum(`point`) as aggregate from `warehouse_cv_selling_history_buys` where `user_id` = ? and `type` = ? and year(`created_at`) = ?", "hash": "737763a8b1385a140668fc59394ea571cb94d36846814f9398dd2739acc3f38c"}, "start_percent": 93.458, "width_percent": 0.266}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.645178, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 93.724, "width_percent": 0.39}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.647425, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 94.114, "width_percent": 0.363}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6492438, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 94.477, "width_percent": 0.346}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.650785, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 94.823, "width_percent": 0.337}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.652304, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 95.16, "width_percent": 0.346}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.653842, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 95.506, "width_percent": 0.363}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.655433, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 95.869, "width_percent": 0.355}, {"sql": "select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = 1723 and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723, "employer_confirmed_at"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, {"index": 19, "namespace": "view", "name": "frontend.layouts.modal.employer_confirm", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.php", "line": 5}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.658505, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "User.php:197", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=197", "ajax": false, "filename": "User.php", "line": "197"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `meta_data` where `meta_data`.`object_type` = ? and `meta_data`.`object_id` = ? and `meta_data`.`object_id` is not null and `key` = ? limit 1", "hash": "1e919bf895733b158fa816fe17ad059167697afb952d846aaeabf40228f5fa12"}, "start_percent": 96.224, "width_percent": 0.293}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 1723 and `notifications`.`notifiable_id` is not null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.663277, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:98", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=98", "ajax": false, "filename": "header.blade.php", "line": "98"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null order by `created_at` desc", "hash": "2c9622ba988b61fa8c1f7fbcd422804726d734b2ef6b91781d7446b85a6e43d8"}, "start_percent": 96.516, "width_percent": 0.372}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 1723 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6885679, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:99", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=99", "ajax": false, "filename": "header.blade.php", "line": "99"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "hash": "f6ba9bdd77fc193fe60a0e5b92c8d6d03185ee2af0d5baf1deec78db97c629a0"}, "start_percent": 96.889, "width_percent": 0.346}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.703017, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:243", "source": {"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=243", "ajax": false, "filename": "header.blade.php", "line": "243"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 97.234, "width_percent": 0.363}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.706379, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 97.598, "width_percent": 0.355}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.707917, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 97.952, "width_percent": 0.346}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.709508, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 98.298, "width_percent": 0.337}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.711011, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 98.635, "width_percent": 0.346}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.712524, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 98.981, "width_percent": 0.346}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.714047, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 99.326, "width_percent": 0.337}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.7155719, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 99.663, "width_percent": 0.337}]}, "models": {"data": {"App\\Models\\EmployerType": {"retrieved": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Deposit": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FDeposit.php&line=1", "ajax": false, "filename": "Deposit.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\WareHouseCvSellingHistoryBuy": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWareHouseCvSellingHistoryBuy.php&line=1", "ajax": false, "filename": "WareHouseCvSellingHistoryBuy.php", "line": "?"}}, "App\\Models\\WareHouseCvSellingBuy": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWareHouseCvSellingBuy.php&line=1", "ajax": false, "filename": "WareHouseCvSellingBuy.php", "line": "?"}}, "App\\Models\\WareHouseCvSelling": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWareHouseCvSelling.php&line=1", "ajax": false, "filename": "WareHouseCvSelling.php", "line": "?"}}, "App\\Models\\WareHouseCv": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWareHouseCv.php&line=1", "ajax": false, "filename": "WareHouseCv.php", "line": "?"}}, "Illuminate\\Notifications\\DatabaseNotification": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\SubmitCv": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSubmitCv.php&line=1", "ajax": false, "filename": "SubmitCv.php", "line": "?"}}, "App\\Models\\MetaData": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FMetaData.php&line=1", "ajax": false, "filename": "MetaData.php", "line": "?"}}}, "count": 37, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 36, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01K2DY8A83KRB7QSPKRKK904AH\" => null\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer/dashboard", "action_name": "employer-manager-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard", "uri": "GET employer/dashboard", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=217\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:217-278</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "758ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1051861013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1051861013\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-558944942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-558944942\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1669614282 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://recland.local/employer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754962076$j18$l0$h0; XSRF-TOKEN=eyJpdiI6InZwdDNVeG1jdzQxc25BSHBsT0NDQVE9PSIsInZhbHVlIjoibDM1ZkNKZmEyc0lVVG9uRUhrQnc2YWd3SFJpSGJTdWg4K1hkQ3dFdGJOWVZIWEYxbUlDakJTSEhNY3VOeDF1VDVpaW9PZEVRSjJadC9SRENpU3F4azVWOUM3czZnS1lmbDNMU1ZLT2p3dnZCMlJKOG5McFN1bkl2QTdBdXZGNjIiLCJtYWMiOiIyZGM0OTFmZTBiYThiMWU0OTUyODEyMjllZTUwNDI2ZjM1ZmRkNmQ4NDFmNzBmMjAwMDYxY2QyYjcxNGE2YjYzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkJhd1duMWU0b2JDWWhvdG1KTW0zQXc9PSIsInZhbHVlIjoiMWQwUHZwNmZ3bnJLeWJqMFdMTiszYm9tV1N3ZDJHNWFFQXgyUXlkcjZZMm41cGNMWFRjUkJscHVuVDd2UC9VYkR4UzB5VmZMOHhFMDdaUncyRktrL21QcHczemJMcTRxRkdIODhMaG81UkxUbHlYc1ZUaDdFY2tXdFlWNVErdWQiLCJtYWMiOiIwNjRhZTI4NmU2OWJlOTk3OGQ1MmFhMzMyMjVlNTIzNTIwYTQzNjhkMThkY2RiZmQwMzljOTNkNjg3OWNkMDZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669614282\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1372881749 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372881749\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1744252866 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:27:58 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744252866\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-837019213 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://recland.local/employer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K2DY8A83KRB7QSPKRKK904AH</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837019213\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer/dashboard", "action_name": "employer-manager-dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@dashboard"}, "badge": null}}