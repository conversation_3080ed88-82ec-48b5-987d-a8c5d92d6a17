{"__meta": {"id": "01K2DYCWQ5CC5PHY653W6TKGNG", "datetime": "2025-08-12 08:30:27", "utime": **********.942696, "method": "GET", "uri": "/admin/ref/datatable/1648?draw=1&columns%5B0%5D%5Bdata%5D=id&columns%5B0%5D%5Bname%5D=&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=false&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=id&columns%5B1%5D%5Bname%5D=&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=false&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=submit_cv_meta_candidate_name&columns%5B2%5D%5Bname%5D=&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=false&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=submit_cv_meta_candidate_email&columns%5B3%5D%5Bname%5D=&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=false&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=submit_cv_meta_candidate_mobile&columns%5B4%5D%5Bname%5D=&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=false&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=rec_name&columns%5B5%5D%5Bname%5D=&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=false&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=rec_email&columns%5B6%5D%5Bname%5D=&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=false&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B7%5D%5Bdata%5D=rec_mobile&columns%5B7%5D%5Bname%5D=&columns%5B7%5D%5Bsearchable%5D=true&columns%5B7%5D%5Borderable%5D=false&columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B8%5D%5Bdata%5D=submit_cv_meta_url_cv_public&columns%5B8%5D%5Bname%5D=&columns%5B8%5D%5Bsearchable%5D=true&columns%5B8%5D%5Borderable%5D=false&columns%5B8%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B8%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B9%5D%5Bdata%5D=submit_cv_meta_url_cv_private&columns%5B9%5D%5Bname%5D=&columns%5B9%5D%5Bsearchable%5D=true&columns%5B9%5D%5Borderable%5D=false&columns%5B9%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B9%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B10%5D%5Bdata%5D=job_name&columns%5B10%5D%5Bname%5D=&columns%5B10%5D%5Bsearchable%5D=true&columns%5B10%5D%5Borderable%5D=false&columns%5B10%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B10%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B11%5D%5Bdata%5D=company_name&columns%5B11%5D%5Bname%5D=&columns%5B11%5D%5Bsearchable%5D=true&columns%5B11%5D%5Borderable%5D=false&columns%5B11%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B11%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B12%5D%5Bdata%5D=employer_email&columns%5B12%5D%5Bname%5D=&columns%5B12%5D%5Bsearchable%5D=true&columns%5B12%5D%5Borderable%5D=false&columns%5B12%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B12%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B13%5D%5Bdata%5D=status_value&columns%5B13%5D%5Bname%5D=&columns%5B13%5D%5Bsearchable%5D=true&columns%5B13%5D%5Borderable%5D=false&columns%5B13%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B13%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B14%5D%5Bdata%5D=expected_date_value&columns%5B14%5D%5Bname%5D=&columns%5B14%5D%5Bsearchable%5D=true&columns%5B14%5D%5Borderable%5D=false&columns%5B14%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B14%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B15%5D%5Bdata%5D=id&columns%5B15%5D%5Bname%5D=&columns%5B15%5D%5Bsearchable%5D=true&columns%5B15%5D%5Borderable%5D=false&columns%5B15%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B15%5D%5Bsearch%5D%5Bregex%5D=false&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1754962226673", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 16, "messages": [{"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 3.48\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.749369, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.759931, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1'))\\n-- \",\n    \"Time:\": 1.6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.764546, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `submit_cvs` where `job_id` = '1648' and (`authorize` = '0' or (`authorize` = '1' and `authorize_status` = '1')) order by `id` desc limit 10 offset 0\\n-- \",\n    \"Time:\": 5.56\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.771692, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `id`, `name` from `job` where `job`.`id` in (1648)\\n-- \",\n    \"Time:\": 0.3\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.776323, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `id`, `name` from `companies` where `companies`.`id` in (84)\\n-- \",\n    \"Time:\": 0.29\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.778689, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `users`.`email`, `job`.`id` as `laravel_through_key` from `users` inner join `job` on `job`.`employer_id` = `users`.`id` where `users`.`type` = 'employer' and `job`.`id` in ('1648')\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.782109, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `email`, `name`, `mobile`, `id` from `users` where `users`.`id` in ('512')\\n-- \",\n    \"Time:\": 0.25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.785173, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `id`, `candidate_name`, `candidate_email`, `candidate_mobile`, `cv_public`, `cv_private`, `submit_cv_id` from `submit_cv_metas` where `submit_cv_metas`.`submit_cv_id` in (3165, 3167)\\n-- \",\n    \"Time:\": 1.19\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.788151, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 356", "message_html": null, "is_string": false, "label": "warning", "time": **********.790895, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 351", "message_html": null, "is_string": false, "label": "warning", "time": **********.791028, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.4\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.791881, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\Company.php on line 174", "message_html": null, "is_string": false, "label": "warning", "time": **********.917139, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 356", "message_html": null, "is_string": false, "label": "warning", "time": **********.920209, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php on line 351", "message_html": null, "is_string": false, "label": "warning", "time": **********.920335, "xdebug_link": null, "collector": "log"}, {"message": "[08:30:27] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\Company.php on line 174", "message_html": null, "is_string": false, "label": "warning", "time": **********.925053, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.271784, "end": **********.94273, "duration": 0.6709458827972412, "duration_str": "671ms", "measures": [{"label": "Booting", "start": **********.271784, "relative_start": 0, "end": **********.683194, "relative_end": **********.683194, "duration": 0.***************, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.683208, "relative_start": 0.***************, "end": **********.942732, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.697167, "relative_start": 0.****************, "end": **********.71127, "relative_end": **********.71127, "duration": 0.014103174209594727, "duration_str": "14.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "59MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/ref/datatable/{job}", "middleware": "web, check-admin, check-role", "controller": "App\\Http\\Controllers\\Admin\\JobController@refDatatable<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=148\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/ref", "as": "ref-datatable", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=148\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/JobController.php:148-155</a>"}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01378, "accumulated_duration_str": "13.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.746217, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 0, "width_percent": 25.254}, {"sql": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, {"index": 19, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 31}, {"index": 20, "namespace": "middleware", "name": "check-role", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckRole.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 22, "namespace": "middleware", "name": "check-admin", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckLoginAdmin.php", "line": 21}], "start": **********.759611, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PermissionService.php:19", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/PermissionService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\PermissionService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FAdmin%2FPermissionService.php&line=19", "ajax": false, "filename": "PermissionService.php", "line": "19"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `roles`.`permission`, `user_role`.`user_id` as `pivot_user_id`, `user_role`.`role_id` as `pivot_role_id` from `roles` inner join `user_role` on `roles`.`id` = `user_role`.`role_id` where `user_role`.`user_id` in (1)", "hash": "09b63fe1be07508dfb18b73a990c5ee0249dd740fb6501945ccdb8043c3b971c"}, "start_percent": 25.254, "width_percent": 2.903}, {"sql": "select count(*) as aggregate from `submit_cvs` where `job_id` = '1648' and (`authorize` = 0 or (`authorize` = 1 and `authorize_status` = 1))", "type": "query", "params": [], "bindings": ["1648", 0, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 33}, {"index": 16, "namespace": null, "name": "app/Services/Admin/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\SubmitCvService.php", "line": 67}, {"index": 17, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 368}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.763015, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:33", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=33", "ajax": false, "filename": "SubmitCvRepository.php", "line": "33"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `submit_cvs` where `job_id` = ? and (`authorize` = ? or (`authorize` = ? and `authorize_status` = ?))", "hash": "826fe939c3cae6dfac4fe36fcf82db0bd1a0b3bbf2642257e98ce6ab6dc8fff0"}, "start_percent": 28.157, "width_percent": 11.611}, {"sql": "select * from `submit_cvs` where `job_id` = '1648' and (`authorize` = 0 or (`authorize` = 1 and `authorize_status` = 1)) order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["1648", 0, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 33}, {"index": 16, "namespace": null, "name": "app/Services/Admin/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\SubmitCvService.php", "line": 67}, {"index": 17, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 368}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.766205, "duration": 0.00556, "duration_str": "5.56ms", "memory": 0, "memory_str": null, "filename": "SubmitCvRepository.php:33", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SubmitCvRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SubmitCvRepository.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSubmitCvRepository.php&line=33", "ajax": false, "filename": "SubmitCvRepository.php", "line": "33"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `submit_cvs` where `job_id` = ? and (`authorize` = ? or (`authorize` = ? and `authorize_status` = ?)) order by `id` desc limit 10 offset 0", "hash": "bc7c41ce10744faaf6f785b6a1c09615cc104517e5ada20649bbb23830523a90"}, "start_percent": 39.768, "width_percent": 40.348}, {"sql": "select `id`, `name` from `job` where `job`.`id` in (1648)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, {"index": 21, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 370}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7760952, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:785", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=785", "ajax": false, "filename": "AbstractPaginator.php", "line": "785"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `id`, `name` from `job` where `job`.`id` in (1648)", "hash": "3cdc6535185e22c5120ecf42b0ea85be7d2f4dceee713a79d66fda49a4349639"}, "start_percent": 80.116, "width_percent": 2.177}, {"sql": "select `id`, `name` from `companies` where `companies`.`id` in (84)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, {"index": 21, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 370}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.77849, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:785", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=785", "ajax": false, "filename": "AbstractPaginator.php", "line": "785"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `id`, `name` from `companies` where `companies`.`id` in (84)", "hash": "53ccf93308e3c092efc7b30d1f7192a8f63cf5aee1eff337f80cdc72fdd64236"}, "start_percent": 82.293, "width_percent": 2.104}, {"sql": "select `users`.`email`, `job`.`id` as `laravel_through_key` from `users` inner join `job` on `job`.`employer_id` = `users`.`id` where `users`.`type` = 'employer' and `job`.`id` in (1648)", "type": "query", "params": [], "bindings": ["employer", 1648], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, {"index": 20, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 370}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.781866, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:785", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=785", "ajax": false, "filename": "AbstractPaginator.php", "line": "785"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `users`.`email`, `job`.`id` as `laravel_through_key` from `users` inner join `job` on `job`.`employer_id` = `users`.`id` where `users`.`type` = ? and `job`.`id` in (?)", "hash": "ee12a162f0343283a463adf892ca20043503cb7c1ccfbc30c67e8e8e82ae489d"}, "start_percent": 84.398, "width_percent": 2.25}, {"sql": "select `email`, `name`, `mobile`, `id` from `users` where `users`.`id` in (512)", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, {"index": 21, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 370}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7849889, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:785", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=785", "ajax": false, "filename": "AbstractPaginator.php", "line": "785"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `email`, `name`, `mobile`, `id` from `users` where `users`.`id` in (?)", "hash": "8b8bf08e874ad44cd6986ae7f1d8813b5ab49bcc95f5d6c244839594d469bd02"}, "start_percent": 86.647, "width_percent": 1.814}, {"sql": "select `id`, `candidate_name`, `candidate_email`, `candidate_mobile`, `cv_public`, `cv_private`, `submit_cv_id` from `submit_cv_metas` where `submit_cv_metas`.`submit_cv_id` in (3165, 3167)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, {"index": 21, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 370}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7870271, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:785", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=785", "ajax": false, "filename": "AbstractPaginator.php", "line": "785"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `id`, `candidate_name`, `candidate_email`, `candidate_mobile`, `cv_public`, `cv_private`, `submit_cv_id` from `submit_cv_metas` where `submit_cv_metas`.`submit_cv_id` in (3165, 3167)", "hash": "06e5de700fa84155c53c8112489795fae47ff87d87df40d745700df7c5515390"}, "start_percent": 88.462, "width_percent": 8.636}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "app/Models/SubmitCv.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\SubmitCv.php", "line": 339}, {"index": 28, "namespace": null, "name": "app/Services/Admin/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Admin\\JobService.php", "line": 402}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Admin/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobController.php", "line": 153}], "start": **********.791544, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 97.097, "width_percent": 2.903}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\SubmitCv": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSubmitCv.php&line=1", "ajax": false, "filename": "SubmitCv.php", "line": "?"}}, "App\\Models\\SubmitCvMeta": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSubmitCvMeta.php&line=1", "ajax": false, "filename": "SubmitCvMeta.php", "line": "?"}}, "App\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Job": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}}, "count": 11, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 11}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/job-comments/1648\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/ref/datatable/1648?_=1754962226673&columns%5B0%5D%5Bdata%5D=id&columns%5B...", "action_name": "ref-datatable", "controller_action": "App\\Http\\Controllers\\Admin\\JobController@refDatatable", "uri": "GET admin/ref/datatable/{job}", "controller": "App\\Http\\Controllers\\Admin\\JobController@refDatatable<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=148\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/ref", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobController.php&line=148\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/JobController.php:148-155</a>", "middleware": "web, check-admin, check-role", "duration": "675ms", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-906075882 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:16</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"29 characters\">submit_cv_meta_candidate_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"30 characters\">submit_cv_meta_candidate_email</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"31 characters\">submit_cv_meta_candidate_mobile</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">rec_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"9 characters\">rec_email</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">rec_mobile</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"28 characters\">submit_cv_meta_url_cv_public</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"29 characters\">submit_cv_meta_url_cv_private</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">job_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"14 characters\">employer_email</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">status_value</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"19 characters\">expected_date_value</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1754962226673</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906075882\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-666114468 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-666114468\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1482830448 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/admin/job/1648/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlhFVHd1azlab1AwaU9BUHhSWXU0N2c9PSIsInZhbHVlIjoiR2ZWOS8xZkR5UTUzeXVkZHVmeU5UUzZKRWI1NTRnTGZXeXBtQzZSdjg3aitzS1ZybDE4SGtJbldSbEIrUjlWejl3Mi9QM0gvNG41YUZXSU9jVkptR3d6NCthbDE4a0ZXVTBYVHgremVJUXBzMEVodFc3T1ZRN0Z6QlIxQzFLeWUiLCJtYWMiOiJmYWNjZmI4OTAzMTdmYzJjYzhkMDFmODA0ZjBjYjAxMTM3ZDdjMGZmNTQ4MDQxNTNmZWU1NTUyNGQxZGVhNjY5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IkM1cU1BY3piOElNK0xqbGFSWko3L0E9PSIsInZhbHVlIjoieHFWaHA3ZGd2YmFVWS9EQk01TXV5N1RMU1ZWR1c4WUlQeGthbWMvZ1FSa0VCcVRpVEtkY3Y4ZTErNXRpZDlzS1VyQlhlaTcwNy9zOENaN2tpY2k2S3BmeUxHdTN3UjFnV2VSM0Z2NjVjbVlnR0tzMkVRYWJYT0lOZzdRcXJaK3giLCJtYWMiOiIwYmRhOTY2YTljY2Q4NzhjMWE1YmZlZWUxYWI3YjJiOGRkMGQ5ZGMxYmNkOGExNTViZGFmYmI1OTkwNDQ0ODBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482830448\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1861301447 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6n7BHowcS6we7JFkDGDZRBjg4hsvpTWKVgnVcqpE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861301447\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-195561535 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:30:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195561535\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-163183312 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://recland.local/admin/job-comments/1648</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163183312\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/ref/datatable/1648?_=1754962226673&columns%5B0%5D%5Bdata%5D=id&columns%5B...", "action_name": "ref-datatable", "controller_action": "App\\Http\\Controllers\\Admin\\JobController@refDatatable"}, "badge": null}}