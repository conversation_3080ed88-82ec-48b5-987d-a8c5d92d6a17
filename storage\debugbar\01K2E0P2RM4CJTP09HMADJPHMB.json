{"__meta": {"id": "01K2E0P2RM4CJTP09HMADJPHMB", "datetime": "2025-08-12 09:10:26", "utime": **********.196928, "method": "GET", "uri": "/ajax/get-min-submit-price?group=&is_it=&bonus_type=cv&level=3&career=1&salary_min=10000000&salary_max=20000000&salary_currency=VND&skill_id=2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[09:10:26] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.131036, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:26] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.131374, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\\\"group\\\":null,\\\"is_it\\\":null,\\\"bonus_type\\\":\\\"cv\\\",\\\"level\\\":\\\"3\\\",\\\"career\\\":\\\"1\\\",\\\"salary_min\\\":\\\"10000000\\\",\\\"salary_max\\\":\\\"20000000\\\",\\\"salary_currency\\\":\\\"VND\\\",\\\"skill_id\\\":\\\"2\\\"}', 'http:\\/\\/recland.local\\/ajax\\/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_currency=VND&salary_max=20000000&salary_min=10000000&skill_id=2', 'http:\\/\\/recland.local\\/employer\\/job\\/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"x-csrf-token\\\":[\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"application\\\\\\/json, text\\\\\\/javascript, *\\\\\\/*; q=0.01\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/job\\\\\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6Ii9ZaElEZ1BoZENVL2tMZndzN3FaWGc9PSIsInZhbHVlIjoiYU5QUUxuOGFPT3ovME5ITHBhS3Q5UGIxaHpRcDhFMEo0UHFZZ011Z2MwT0U3a3VVTGdFaTBVVHdsUi9tQ2NFR1VrVERRWmt0clgwUmx1RzRjc3pIRVFJUmN4M21yN0xoamVhOWpuNFJYL0FaSWJpb0ZFckpZRElKeWRTQnArUmwiLCJtYWMiOiJhYzU2Nzg2ODA2NDIyOWUwNmRhNWNhYmM0YWE2NmRmNzVhMmM2YTIxOTJlYzYzOGEwMWU3NGE3MmQwYWRjMTQ5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImVFZmpRc1h3aVZXYVpRVFk3OUw4Rmc9PSIsInZhbHVlIjoidUFSZGNiZWYvditZQmhFV3lhZlJObjIvbmJTY2t5U1dLcW5ZNmwvZzdmZTZ3Vm5qc0d1b0Z0VWFFZVF3a21wQllOUmZmMko4cUhCbzBVdkF5ZkpLUkpGYmh2SENqeTRIL0wvNlNGYTRtVVN2bVVDL0JVWjNmV0o1TDJlRGlYQmUiLCJtYWMiOiJlMDUyZTFiOTRkMWVkZGI5YTIxNzQ0MjA4ZDAyMjc3YTlkYTQ5MTIzYmU4OWM3ZWE3NmE2YjAzYWY4NWRhMjI3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:26', '2025-08-12 09:10:26')\\n-- \",\n    \"Time:\": 2.98\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.186327, "xdebug_link": null, "collector": "log"}, {"message": "[09:10:26] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `level_by_job_tops` where `level_by_job_tops`.`id` = '3' limit 1\\n-- \",\n    \"Time:\": 0.67\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.188868, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754964625.760684, "end": **********.196951, "duration": 0.4362668991088867, "duration_str": "436ms", "measures": [{"label": "Booting", "start": 1754964625.760684, "relative_start": 0, "end": **********.108985, "relative_end": **********.108985, "duration": 0.****************, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.108995, "relative_start": 0.****************, "end": **********.196953, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "87.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.119896, "relative_start": 0.*****************, "end": **********.125724, "relative_end": **********.125724, "duration": 0.005828142166137695, "duration_str": "5.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET ajax/get-min-submit-price", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Frontend\\AjaxController@getMinSubmitPrice<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=461\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/ajax", "as": "ajax-get-min-submit-price", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=461\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/AjaxController.php:461-466</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00365, "accumulated_duration_str": "3.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '{\\\"group\\\":null,\\\"is_it\\\":null,\\\"bonus_type\\\":\\\"cv\\\",\\\"level\\\":\\\"3\\\",\\\"career\\\":\\\"1\\\",\\\"salary_min\\\":\\\"10000000\\\",\\\"salary_max\\\":\\\"20000000\\\",\\\"salary_currency\\\":\\\"VND\\\",\\\"skill_id\\\":\\\"2\\\"}', 'http://recland.local/ajax/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_currency=VND&salary_max=20000000&salary_min=10000000&skill_id=2', 'http://recland.local/employer/job/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"x-csrf-token\\\":[\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"application\\/json, text\\/javascript, *\\/*; q=0.01\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/job\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6Ii9ZaElEZ1BoZENVL2tMZndzN3FaWGc9PSIsInZhbHVlIjoiYU5QUUxuOGFPT3ovME5ITHBhS3Q5UGIxaHpRcDhFMEo0UHFZZ011Z2MwT0U3a3VVTGdFaTBVVHdsUi9tQ2NFR1VrVERRWmt0clgwUmx1RzRjc3pIRVFJUmN4M21yN0xoamVhOWpuNFJYL0FaSWJpb0ZFckpZRElKeWRTQnArUmwiLCJtYWMiOiJhYzU2Nzg2ODA2NDIyOWUwNmRhNWNhYmM0YWE2NmRmNzVhMmM2YTIxOTJlYzYzOGEwMWU3NGE3MmQwYWRjMTQ5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImVFZmpRc1h3aVZXYVpRVFk3OUw4Rmc9PSIsInZhbHVlIjoidUFSZGNiZWYvditZQmhFV3lhZlJObjIvbmJTY2t5U1dLcW5ZNmwvZzdmZTZ3Vm5qc0d1b0Z0VWFFZVF3a21wQllOUmZmMko4cUhCbzBVdkF5ZkpLUkpGYmh2SENqeTRIL0wvNlNGYTRtVVN2bVVDL0JVWjNmV0o1TDJlRGlYQmUiLCJtYWMiOiJlMDUyZTFiOTRkMWVkZGI5YTIxNzQ0MjA4ZDAyMjc3YTlkYTQ5MTIzYmU4OWM3ZWE3NmE2YjAzYWY4NWRhMjI3IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 09:10:26', '2025-08-12 09:10:26')", "type": "query", "params": [], "bindings": ["GET", "{\"group\":null,\"is_it\":null,\"bonus_type\":\"cv\",\"level\":\"3\",\"career\":\"1\",\"salary_min\":\"10000000\",\"salary_max\":\"20000000\",\"salary_currency\":\"VND\",\"skill_id\":\"2\"}", "http://recland.local/ajax/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_currency=VND&salary_max=20000000&salary_min=10000000&skill_id=2", "http://recland.local/employer/job/create", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"x-csrf-token\":[\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/javascript, *\\/*; q=0.01\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6Ii9ZaElEZ1BoZENVL2tMZndzN3FaWGc9PSIsInZhbHVlIjoiYU5QUUxuOGFPT3ovME5ITHBhS3Q5UGIxaHpRcDhFMEo0UHFZZ011Z2MwT0U3a3VVTGdFaTBVVHdsUi9tQ2NFR1VrVERRWmt0clgwUmx1RzRjc3pIRVFJUmN4M21yN0xoamVhOWpuNFJYL0FaSWJpb0ZFckpZRElKeWRTQnArUmwiLCJtYWMiOiJhYzU2Nzg2ODA2NDIyOWUwNmRhNWNhYmM0YWE2NmRmNzVhMmM2YTIxOTJlYzYzOGEwMWU3NGE3MmQwYWRjMTQ5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImVFZmpRc1h3aVZXYVpRVFk3OUw4Rmc9PSIsInZhbHVlIjoidUFSZGNiZWYvditZQmhFV3lhZlJObjIvbmJTY2t5U1dLcW5ZNmwvZzdmZTZ3Vm5qc0d1b0Z0VWFFZVF3a21wQllOUmZmMko4cUhCbzBVdkF5ZkpLUkpGYmh2SENqeTRIL0wvNlNGYTRtVVN2bVVDL0JVWjNmV0o1TDJlRGlYQmUiLCJtYWMiOiJlMDUyZTFiOTRkMWVkZGI5YTIxNzQ0MjA4ZDAyMjc3YTlkYTQ5MTIzYmU4OWM3ZWE3NmE2YjAzYWY4NWRhMjI3IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 09:10:26", "2025-08-12 09:10:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.183597, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 81.644}, {"sql": "select * from `level_by_job_tops` where `level_by_job_tops`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/SubmitCvService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SubmitCvService.php", "line": 1289}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/AjaxController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\AjaxController.php", "line": 464}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.188267, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `level_by_job_tops` where `level_by_job_tops`.`id` = ? limit 1", "hash": "b204d8c2622258e5f4355685b1b6a546ac8ff4d2d35c5034548dd69346383db0"}, "start_percent": 81.644, "width_percent": 18.356}]}, "models": {"data": {"Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\LevelByJobTop": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FLevelByJobTop.php&line=1", "ajax": false, "filename": "LevelByJobTop.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/job/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/ajax/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_c...", "action_name": "ajax-get-min-submit-price", "controller_action": "App\\Http\\Controllers\\Frontend\\AjaxController@getMinSubmitPrice", "uri": "GET ajax/get-min-submit-price", "controller": "App\\Http\\Controllers\\Frontend\\AjaxController@getMinSubmitPrice<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=461\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/ajax", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FAjaxController.php&line=461\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/AjaxController.php:461-466</a>", "middleware": "web, localization, visit-website", "duration": "437ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-212688568 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>group</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_it</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>bonus_type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">cv</span>\"\n  \"<span class=sf-dump-key>level</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>career</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>salary_min</span>\" => \"<span class=sf-dump-str title=\"8 characters\">10000000</span>\"\n  \"<span class=sf-dump-key>salary_max</span>\" => \"<span class=sf-dump-str title=\"8 characters\">20000000</span>\"\n  \"<span class=sf-dump-key>salary_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">VND</span>\"\n  \"<span class=sf-dump-key>skill_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212688568\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1189592777 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1189592777\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2053624054 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754964549$j56$l0$h0; XSRF-TOKEN=eyJpdiI6Ii9ZaElEZ1BoZENVL2tMZndzN3FaWGc9PSIsInZhbHVlIjoiYU5QUUxuOGFPT3ovME5ITHBhS3Q5UGIxaHpRcDhFMEo0UHFZZ011Z2MwT0U3a3VVTGdFaTBVVHdsUi9tQ2NFR1VrVERRWmt0clgwUmx1RzRjc3pIRVFJUmN4M21yN0xoamVhOWpuNFJYL0FaSWJpb0ZFckpZRElKeWRTQnArUmwiLCJtYWMiOiJhYzU2Nzg2ODA2NDIyOWUwNmRhNWNhYmM0YWE2NmRmNzVhMmM2YTIxOTJlYzYzOGEwMWU3NGE3MmQwYWRjMTQ5IiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6ImVFZmpRc1h3aVZXYVpRVFk3OUw4Rmc9PSIsInZhbHVlIjoidUFSZGNiZWYvditZQmhFV3lhZlJObjIvbmJTY2t5U1dLcW5ZNmwvZzdmZTZ3Vm5qc0d1b0Z0VWFFZVF3a21wQllOUmZmMko4cUhCbzBVdkF5ZkpLUkpGYmh2SENqeTRIL0wvNlNGYTRtVVN2bVVDL0JVWjNmV0o1TDJlRGlYQmUiLCJtYWMiOiJlMDUyZTFiOTRkMWVkZGI5YTIxNzQ0MjA4ZDAyMjc3YTlkYTQ5MTIzYmU4OWM3ZWE3NmE2YjAzYWY4NWRhMjI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053624054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-193304126 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193304126\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-748336115 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 02:10:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748336115\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1947741508 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947741508\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/ajax/get-min-submit-price?bonus_type=cv&career=1&group=&is_it=&level=3&salary_c...", "action_name": "ajax-get-min-submit-price", "controller_action": "App\\Http\\Controllers\\Frontend\\AjaxController@getMinSubmitPrice"}, "badge": null}}