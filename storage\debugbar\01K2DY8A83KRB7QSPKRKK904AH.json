{"__meta": {"id": "01K2DY8A83KRB7QSPKRKK904AH", "datetime": "2025-08-12 08:27:57", "utime": **********.95626, "method": "POST", "uri": "/employer/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[08:27:56] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.916424, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:56] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.916805, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"password\\\":\\\"02042000\\\"}', 'http:\\/\\/recland.local\\/employer\\/login', 'http:\\/\\/recland.local\\/employer', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"98\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"content-type\\\":[\\\"application\\\\\\/x-www-form-urlencoded\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImZValJBTmQ2ZW1HNHlZcCt0WW5TbFE9PSIsInZhbHVlIjoiYVBra05HekhUOVhsbURxMnkwRFBnMW1ZRDE1VnUvdkpWeWVtUkhQZnUxd3pSWDdvSng2ektZM0luKzJQTlB1UTd3UERObURmNTA5ZGtGTjRpaXZ1WFNUNVhZTnQwVDl5RTZ5WUVNZXdXWktRTTVVMGZUV25wU0RKNng0TU1USnoiLCJtYWMiOiI5YTQ3ZWMwYWI3YTJlN2IxYjFkNmI0NWNkYTg1NDM5NzhiMjc4Mzc1N2Y0NWU1OGNhZDNlMzM0YjljYWUyODUzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InRudWFPNmtiVitBQy9vbm1qRnFreFE9PSIsInZhbHVlIjoiN25Yb3VKSlJrSVdUUW55ZHhiUGpVVzk3NlJnQ3ZaL2xHT1VMT1N6c29zNkJxTzFNSFVQbS95S1l0VklXK3k5amRPMlJIYXFvdkR4dGM5MVFyRnMrV3FsVy8zZFVycTFRZSs0enN6MHhOL3FXMUtkTGdnanpxMnRQMGVhR0E0VEQiLCJtYWMiOiIzOTI3NjA4ZWE3MDc4YzRjNWI1Y2E1Y2ZiMWEwODliZDhhN2I3ZTYzN2UyNjk5ODg0YzBlNmVhNjMyMmU3M2Q4IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j18$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:57', '2025-08-12 08:27:57')\\n-- \",\n    \"Time:\": 26.05\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.065924, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1\\n-- \",\n    \"Time:\": 1.86\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.585883, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `email` = '<EMAIL>' and `type` = 'employer' and `is_active` = '1' limit 1\\n-- \",\n    \"Time:\": 0.69\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.755224, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nupdate `users` set `last_login_at` = '2025-08-12 08:27:57', `users`.`updated_at` = '2025-08-12 08:27:57' where `id` = '1723'\\n-- \",\n    \"Time:\": 0.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.90562, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.57\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.909024, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"last_login_at\\\":\\\"2025-08-11 15:15:49\\\"}', '{\\\"last_login_at\\\":\\\"2025-08-12 08:27:57\\\"}', 'updated', '1723', 'App\\\\Models\\\\User', '1', 'App\\\\Models\\\\User', '', '127.0.0.1', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', 'http:\\/\\/recland.local\\/employer\\/login', '2025-08-12 08:27:57', '2025-08-12 08:27:57')\\n-- \",\n    \"Time:\": 0.5\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.91505, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ndelete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\\\Models\\\\User' and `personal_access_tokens`.`tokenable_id` = '1723' and `personal_access_tokens`.`tokenable_id` is not null\\n-- \",\n    \"Time:\": 1.85\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.918843, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:57] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('Personal Access Token', '98d6f485f8a7f500d6d114f5734f1d14c5332f7245c0dc5799ef1cda0cec1273', '[\\\"*\\\"]', '1723', 'App\\\\Models\\\\User', '2025-08-12 08:27:57', '2025-08-12 08:27:57')\\n-- \",\n    \"Time:\": 0.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.921031, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.489823, "end": **********.956287, "duration": 1.4664638042449951, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": **********.489823, "relative_start": 0, "end": **********.889637, "relative_end": **********.889637, "duration": 0.****************, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.889647, "relative_start": 0.****************, "end": **********.95629, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.903004, "relative_start": 0.****************, "end": **********.906947, "relative_end": **********.906947, "duration": 0.003942966461181641, "duration_str": "3.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST employer/login", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "employer-post-login", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserController.php:201-214</a>"}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05226999999999999, "accumulated_duration_str": "52.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"password\\\":\\\"02042000\\\"}', 'http://recland.local/employer/login', 'http://recland.local/employer', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"98\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"content-type\\\":[\\\"application\\/x-www-form-urlencoded\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImZValJBTmQ2ZW1HNHlZcCt0WW5TbFE9PSIsInZhbHVlIjoiYVBra05HekhUOVhsbURxMnkwRFBnMW1ZRDE1VnUvdkpWeWVtUkhQZnUxd3pSWDdvSng2ektZM0luKzJQTlB1UTd3UERObURmNTA5ZGtGTjRpaXZ1WFNUNVhZTnQwVDl5RTZ5WUVNZXdXWktRTTVVMGZUV25wU0RKNng0TU1USnoiLCJtYWMiOiI5YTQ3ZWMwYWI3YTJlN2IxYjFkNmI0NWNkYTg1NDM5NzhiMjc4Mzc1N2Y0NWU1OGNhZDNlMzM0YjljYWUyODUzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InRudWFPNmtiVitBQy9vbm1qRnFreFE9PSIsInZhbHVlIjoiN25Yb3VKSlJrSVdUUW55ZHhiUGpVVzk3NlJnQ3ZaL2xHT1VMT1N6c29zNkJxTzFNSFVQbS95S1l0VklXK3k5amRPMlJIYXFvdkR4dGM5MVFyRnMrV3FsVy8zZFVycTFRZSs0enN6MHhOL3FXMUtkTGdnanpxMnRQMGVhR0E0VEQiLCJtYWMiOiIzOTI3NjA4ZWE3MDc4YzRjNWI1Y2E1Y2ZiMWEwODliZDhhN2I3ZTYzN2UyNjk5ODg0YzBlNmVhNjMyMmU3M2Q4IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j18$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:57', '2025-08-12 08:27:57')", "type": "query", "params": [], "bindings": ["POST", "{\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\",\"email\":\"<EMAIL>\",\"password\":\"02042000\"}", "http://recland.local/employer/login", "http://recland.local/employer", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"98\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImZValJBTmQ2ZW1HNHlZcCt0WW5TbFE9PSIsInZhbHVlIjoiYVBra05HekhUOVhsbURxMnkwRFBnMW1ZRDE1VnUvdkpWeWVtUkhQZnUxd3pSWDdvSng2ektZM0luKzJQTlB1UTd3UERObURmNTA5ZGtGTjRpaXZ1WFNUNVhZTnQwVDl5RTZ5WUVNZXdXWktRTTVVMGZUV25wU0RKNng0TU1USnoiLCJtYWMiOiI5YTQ3ZWMwYWI3YTJlN2IxYjFkNmI0NWNkYTg1NDM5NzhiMjc4Mzc1N2Y0NWU1OGNhZDNlMzM0YjljYWUyODUzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InRudWFPNmtiVitBQy9vbm1qRnFreFE9PSIsInZhbHVlIjoiN25Yb3VKSlJrSVdUUW55ZHhiUGpVVzk3NlJnQ3ZaL2xHT1VMT1N6c29zNkJxTzFNSFVQbS95S1l0VklXK3k5amRPMlJIYXFvdkR4dGM5MVFyRnMrV3FsVy8zZFVycTFRZSs0enN6MHhOL3FXMUtkTGdnanpxMnRQMGVhR0E0VEQiLCJtYWMiOiIzOTI3NjA4ZWE3MDc4YzRjNWI1Y2E1Y2ZiMWEwODliZDhhN2I3ZTYzN2UyNjk5ODg0YzBlNmVhNjMyMmU3M2Q4IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j18$l0$h0\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:27:57", "2025-08-12 08:27:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.0401728, "duration": 0.02605, "duration_str": "26.05ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 49.837}, {"sql": "select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>", "employer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5841851, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:79", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserRepository.php&line=79", "ajax": false, "filename": "UserRepository.php", "line": "79"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `email` = ? and `type` = ? limit 1", "hash": "a54d756b464a0159e5245a94d6230becdbef534626794266d1bd01ea42412f21"}, "start_percent": 49.837, "width_percent": 3.558}, {"sql": "select * from `users` where `email` = '<EMAIL>' and `type` = 'employer' and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>", "employer", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 112}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.754617, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `email` = ? and `type` = ? and `is_active` = ? limit 1", "hash": "d9ff23a8fd6f153b18a036af43bc0eace1433d18806b93f2a0503437e6913180"}, "start_percent": 53.396, "width_percent": 1.32}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'users'", "type": "query", "params": [], "bindings": ["hri_recland_product", "users"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.88386, "duration": 0.01945, "duration_str": "19.45ms", "memory": 0, "memory_str": null, "filename": "UserService.php:119", "source": {"index": 15, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FUserService.php&line=119", "ajax": false, "filename": "UserService.php", "line": "119"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "2f932a8ab5559dc2e718e2169d292b7c251235f387a3834f8b6fb4b996f87912"}, "start_percent": 54.716, "width_percent": 37.211}, {"sql": "update `users` set `last_login_at` = '2025-08-12 08:27:57', `users`.`updated_at` = '2025-08-12 08:27:57' where `id` = 1723", "type": "query", "params": [], "bindings": ["2025-08-12 08:27:57", "2025-08-12 08:27:57", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.905113, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "UserService.php:119", "source": {"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FUserService.php&line=119", "ajax": false, "filename": "UserService.php", "line": "119"}, "connection": "hri_recland_product", "explain": null, "start_percent": 91.927, "width_percent": 1.11}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Resolvers/UserResolver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Resolvers\\UserResolver.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditable.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditable.php", "line": 409}], "start": **********.9085228, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 93.036, "width_percent": 1.09}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"last_login_at\\\":\\\"2025-08-11 15:15:49\\\"}', '{\\\"last_login_at\\\":\\\"2025-08-12 08:27:57\\\"}', 'updated', 1723, 'App\\Models\\User', 1, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://recland.local/employer/login', '2025-08-12 08:27:57', '2025-08-12 08:27:57')", "type": "query", "params": [], "bindings": ["{\"last_login_at\":\"2025-08-11 15:15:49\"}", "{\"last_login_at\":\"2025-08-12 08:27:57\"}", "updated", 1723, "App\\Models\\User", 1, "App\\Models\\User", null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "http://recland.local/employer/login", "2025-08-12 08:27:57", "2025-08-12 08:27:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 33, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 119}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}], "start": **********.91462, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Auditor.php:83", "source": {"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FAuditor.php&line=83", "ajax": false, "filename": "Auditor.php", "line": "83"}, "connection": "hri_recland_product", "explain": null, "start_percent": 94.127, "width_percent": 0.957}, {"sql": "delete from `personal_access_tokens` where `personal_access_tokens`.`tokenable_type` = 'App\\Models\\User' and `personal_access_tokens`.`tokenable_id` = 1723 and `personal_access_tokens`.`tokenable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 121}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9170642, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "UserService.php:121", "source": {"index": 14, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FServices%2FFrontend%2FUserService.php&line=121", "ajax": false, "filename": "UserService.php", "line": "121"}, "connection": "hri_recland_product", "explain": null, "start_percent": 95.083, "width_percent": 3.539}, {"sql": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('Personal Access Token', '98d6f485f8a7f500d6d114f5734f1d14c5332f7245c0dc5799ef1cda0cec1273', '[\\\"*\\\"]', 1723, 'App\\Models\\User', '2025-08-12 08:27:57', '2025-08-12 08:27:57')", "type": "query", "params": [], "bindings": ["Personal Access Token", "98d6f485f8a7f500d6d114f5734f1d14c5332f7245c0dc5799ef1cda0cec1273", "[\"*\"]", 1723, "App\\Models\\User", "2025-08-12 08:27:57", "2025-08-12 08:27:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 46}, {"index": 19, "namespace": null, "name": "app/Services/Frontend/UserService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserService.php", "line": 122}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.920381, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasApiTokens.php:46", "source": {"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FHasApiTokens.php&line=46", "ajax": false, "filename": "HasApiTokens.php", "line": "46"}, "connection": "hri_recland_product", "explain": null, "start_percent": 98.623, "width_percent": 1.377}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 3, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "OwenIt\\Auditing\\Models\\Audit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FModels%2FAudit.php&line=1", "ajax": false, "filename": "Audit.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}}, "count": 7, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 3, "retrieved": 3, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723"}, "request": {"data": {"status": "302 Found", "full_url": "http://recland.local/employer/login", "action_name": "employer-post-login", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin", "uri": "POST employer/login", "controller": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=201\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserController.php:201-214</a>", "middleware": "web, localization, visit-website", "duration": "1.47s", "peak_memory": "44MB", "response": "Redirect to http://recland.local/employer/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1389743076 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1389743076\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1722037203 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722037203\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1832466348 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">98</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://recland.local/employer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6ImZValJBTmQ2ZW1HNHlZcCt0WW5TbFE9PSIsInZhbHVlIjoiYVBra05HekhUOVhsbURxMnkwRFBnMW1ZRDE1VnUvdkpWeWVtUkhQZnUxd3pSWDdvSng2ektZM0luKzJQTlB1UTd3UERObURmNTA5ZGtGTjRpaXZ1WFNUNVhZTnQwVDl5RTZ5WUVNZXdXWktRTTVVMGZUV25wU0RKNng0TU1USnoiLCJtYWMiOiI5YTQ3ZWMwYWI3YTJlN2IxYjFkNmI0NWNkYTg1NDM5NzhiMjc4Mzc1N2Y0NWU1OGNhZDNlMzM0YjljYWUyODUzIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6InRudWFPNmtiVitBQy9vbm1qRnFreFE9PSIsInZhbHVlIjoiN25Yb3VKSlJrSVdUUW55ZHhiUGpVVzk3NlJnQ3ZaL2xHT1VMT1N6c29zNkJxTzFNSFVQbS95S1l0VklXK3k5amRPMlJIYXFvdkR4dGM5MVFyRnMrV3FsVy8zZFVycTFRZSs0enN6MHhOL3FXMUtkTGdnanpxMnRQMGVhR0E0VEQiLCJtYWMiOiIzOTI3NjA4ZWE3MDc4YzRjNWI1Y2E1Y2ZiMWEwODliZDhhN2I3ZTYzN2UyNjk5ODg0YzBlNmVhNjMyMmU3M2Q4IiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t**********$j18$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832466348\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1219537597 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OLDKqoGpbczGmQ3iONEKK3dsTzLGGTm8QWy0Jh3e</span>\"\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219537597\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1743982275 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:27:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://recland.local/employer/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743982275\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-591920585 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://recland.local/employer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591920585\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://recland.local/employer/login", "action_name": "employer-post-login", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@employerPostLogin"}, "badge": "302 Found"}}