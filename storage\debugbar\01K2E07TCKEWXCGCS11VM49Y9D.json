{"__meta": {"id": "01K2E07TCKEWXCGCS11VM49Y9D", "datetime": "2025-08-12 09:02:38", "utime": **********.868613, "method": "POST", "uri": "/admin/jobs-management/search?", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 7, "messages": [{"message": "[09:02:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job`\\n-- \",\n    \"Time:\": 12.65\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.697456, "xdebug_link": null, "collector": "log"}, {"message": "[09:02:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where `is_active` = '1'\\n-- \",\n    \"Time:\": 7.89\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.708239, "xdebug_link": null, "collector": "log"}, {"message": "[09:02:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = '2025'\\n-- \",\n    \"Time:\": 5.78\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.715316, "xdebug_link": null, "collector": "log"}, {"message": "[09:02:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job` order by `id` desc limit 25\\n-- \",\n    \"Time:\": 1.2\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.748186, "xdebug_link": null, "collector": "log"}, {"message": "[09:02:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` in (84, 217, 286, 487, 491, 693, 694, 695, 696)\\n-- \",\n    \"Time:\": 0.61\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.756478, "xdebug_link": null, "collector": "log"}, {"message": "[09:02:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `users`.`id` in (1723, 4629, 5421, 6139, 6156, 7372, 7412, 7415, 7456)\\n-- \",\n    \"Time:\": 0.59\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.761632, "xdebug_link": null, "collector": "log"}, {"message": "[09:02:38] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as total_rows from (select `job`.`id` from `job`) as `job_aggregator`\\n-- \",\n    \"Time:\": 0.27\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.765443, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.199813, "end": **********.868641, "duration": 0.668828010559082, "duration_str": "669ms", "measures": [{"label": "Booting", "start": **********.199813, "relative_start": 0, "end": **********.627619, "relative_end": **********.627619, "duration": 0.*****************, "duration_str": "428ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.62763, "relative_start": 0.*****************, "end": **********.868644, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "241ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.641223, "relative_start": 0.****************, "end": **********.645271, "relative_end": **********.645271, "duration": 0.*****************, "duration_str": "4.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 250, "nb_templates": 250, "templates": [{"name": "50x crud::columns.number", "param_count": null, "params": [], "start": **********.774016, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/number.blade.phpcrud::columns.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 50, "name_original": "crud::columns.number"}, {"name": "125x crud::columns.custom_html", "param_count": null, "params": [], "start": **********.77465, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/custom_html.blade.phpcrud::columns.custom_html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Fcustom_html.blade.php&line=1", "ajax": false, "filename": "custom_html.blade.php", "line": "?"}, "render_count": 125, "name_original": "crud::columns.custom_html"}, {"name": "25x crud::columns.closure", "param_count": null, "params": [], "start": **********.776894, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/columns/closure.blade.phpcrud::columns.closure", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fcolumns%2Fclosure.blade.php&line=1", "ajax": false, "filename": "closure.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::columns.closure"}, {"name": "25x crud::inc.button_stack", "param_count": null, "params": [], "start": **********.777613, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/inc/button_stack.blade.phpcrud::inc.button_stack", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Finc%2Fbutton_stack.blade.php&line=1", "ajax": false, "filename": "button_stack.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::inc.button_stack"}, {"name": "25x crud::buttons.job.custom_edit", "param_count": null, "params": [], "start": **********.778573, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/vendor/backpack/crud/buttons/job/custom_edit.blade.phpcrud::buttons.job.custom_edit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fvendor%2Fbackpack%2Fcrud%2Fbuttons%2Fjob%2Fcustom_edit.blade.php&line=1", "ajax": false, "filename": "custom_edit.blade.php", "line": "?"}, "render_count": 25, "name_original": "crud::buttons.job.custom_edit"}]}, "route": {"uri": "POST admin/jobs-management/search", "middleware": "web, admin, Closure", "as": "jobs-management.search", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\JobCrudController@search<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:70-111</a>"}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05142, "accumulated_duration_str": "51.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `job`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 400}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 87}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.685133, "duration": 0.01265, "duration_str": "12.65ms", "memory": 0, "memory_str": null, "filename": "JobCrudController.php:400", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 400}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobCrudController.php&line=400", "ajax": false, "filename": "JobCrudController.php", "line": "400"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job`", "hash": "69ce9914dd8df1c6e446718b922b4f3947f2c065f466a1e566a877e12e6c2618"}, "start_percent": 0, "width_percent": 24.601}, {"sql": "select count(*) as aggregate from `job` where `is_active` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 405}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.700414, "duration": 0.00789, "duration_str": "7.89ms", "memory": 0, "memory_str": null, "filename": "JobCrudController.php:405", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobCrudController.php&line=405", "ajax": false, "filename": "JobCrudController.php", "line": "405"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where `is_active` = ?", "hash": "5a65b1fd7af6e5bb70cc9d18537216394af83833e0cee12b8e9d1dcd5a7285e6"}, "start_percent": 24.601, "width_percent": 15.344}, {"sql": "select count(*) as aggregate from `job` where month(`created_at`) = '08' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["08", 2025], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 412}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/CrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\CrudController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}], "start": **********.709598, "duration": 0.00578, "duration_str": "5.78ms", "memory": 0, "memory_str": null, "filename": "JobCrudController.php:412", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 412}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FAdmin%2FJobCrudController.php&line=412", "ajax": false, "filename": "JobCrudController.php", "line": "412"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `job` where month(`created_at`) = ? and year(`created_at`) = ?", "hash": "4df7cf857fe8dc883e4b10353b6aeb8eea03ae042dd6975a45f85d2e9969e3ff"}, "start_percent": 39.946, "width_percent": 11.241}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'hri_recland_product' and table_name = 'job'", "type": "query", "params": [], "bindings": ["hri_recland_product", "job"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 382}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/CrudColumn.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\CrudColumn.php", "line": 53}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Columns.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Columns.php", "line": 423}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/JobCrudController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Admin\\JobCrudController.php", "line": 115}], "start": **********.720812, "duration": 0.02243, "duration_str": "22.43ms", "memory": 0, "memory_str": null, "filename": "ColumnsProtectedMethods.php:312", "source": {"index": 10, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/ColumnsProtectedMethods.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\ColumnsProtectedMethods.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FColumnsProtectedMethods.php&line=312", "ajax": false, "filename": "ColumnsProtectedMethods.php", "line": "312"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select column_name as `column_name` from information_schema.columns where table_schema = ? and table_name = ?", "hash": "990a09a5fd80a4157b96a1522ddde828b43b0f6c71c90db92e7d7f75e92a356e"}, "start_percent": 51.186, "width_percent": 43.621}, {"sql": "select * from `job` order by `id` desc limit 25", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, {"index": 15, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.747051, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Read.php:147", "source": {"index": 14, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=147", "ajax": false, "filename": "Read.php", "line": "147"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job` order by `id` desc limit 25", "hash": "33898ef185da13c5ce59f16123af4b5c8b3e95e21059de8f70a7ad99bd7214cb"}, "start_percent": 94.807, "width_percent": 2.334}, {"sql": "select * from `companies` where `companies`.`id` in (84, 217, 286, 487, 491, 693, 694, 695, 696)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 96}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.755935, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Read.php:147", "source": {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=147", "ajax": false, "filename": "Read.php", "line": "147"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` in (84, 217, 286, 487, 491, 693, 694, 695, 696)", "hash": "321e2bfbc3325cbb6bb65eca5fe86498be1d9fe6cf84cbb97882cc70183c7c6d"}, "start_percent": 97.141, "width_percent": 1.186}, {"sql": "select * from `users` where `users`.`id` in (1723, 4629, 5421, 6139, 6156, 7372, 7412, 7415, 7456)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, {"index": 20, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 96}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.761107, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Read.php:147", "source": {"index": 19, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Read.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Read.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FRead.php&line=147", "ajax": false, "filename": "Read.php", "line": "147"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `users`.`id` in (1723, 4629, 5421, 6139, 6156, 7372, 7412, 7415, 7456)", "hash": "9d2792a82414808293117eb486216efddd2b37d0a392c310745040244eb0b1c1"}, "start_percent": 98.327, "width_percent": 1.147}, {"sql": "select count(*) as total_rows from (select `job`.`id` from `job`) as `job_aggregator`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 277}, {"index": 12, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 197}, {"index": 13, "namespace": null, "name": "vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Http\\Controllers\\Operations\\ListOperation.php", "line": 100}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.765238, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Query.php:277", "source": {"index": 11, "namespace": null, "name": "vendor/backpack/crud/src/app/Library/CrudPanel/Traits/Query.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\backpack\\crud\\src\\app\\Library\\CrudPanel\\Traits\\Query.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FLibrary%2FCrudPanel%2FTraits%2FQuery.php&line=277", "ajax": false, "filename": "Query.php", "line": "277"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as total_rows from (select `job`.`id` from `job`) as `job_aggregator`", "hash": "f8a6b8346fd80e53963cf7704b03898eb9a3825915a68be3d8be145c8fe1110d"}, "start_percent": 99.475, "width_percent": 0.525}]}, "models": {"data": {"App\\Models\\Job": {"retrieved": 25, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 43, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 43}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/admin/jobs-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/admin/jobs-management/search", "action_name": "jobs-management.search", "controller_action": "App\\Http\\Controllers\\Admin\\JobCrudController@search", "uri": "POST admin/jobs-management/search", "operation": "list", "controller": "App\\Http\\Controllers\\Admin\\JobCrudController@search<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fbackpack%2Fcrud%2Fsrc%2Fapp%2FHttp%2FControllers%2FOperations%2FListOperation.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/backpack/crud/src/app/Http/Controllers/Operations/ListOperation.php:70-111</a>", "middleware": "web, admin", "duration": "669ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1320775273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1320775273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1633422621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>4</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>totalEntryCount</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633422621\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1134440587 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1949</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://recland.local/admin/jobs-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,vi;q=0.8,nl;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1257 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InUrRENVRVNmdnM0N1I1TXNrWHJVeHc9PSIsInZhbHVlIjoiWk5XMW1OTWFiOTVBSGVtS0E1Zy9LczVhdFNOQS9mVkRwVFl0YVgwZjIxSW43bjVsd2hYRFprYUgyRFo1LzVTcU5kUDRjZmppdy9QWXBseTdvT0lMSXMzcWh4ODFyYlcxVGdpc3VmczQ3c3hjamZYMkl4OU5yOFNxTUZxRWxWVHZWeGtCWmFLemdzQ3c4alJtZGs2a0RJcGZJUFhFQmgxd2FxNUEzSnhGRXRacUtPRTQyWVlkUWg4MGtGZWFVZU1wYVpVUVdtTFY4anZWOXdmREliemF5V1JuZTY0SkVxdURaeXlib2E5aW15WT0iLCJtYWMiOiIzMGZkNTNmMzAwMjViZDY4NGMxYmU0ZDJhMmNkNDVmMjQ3M2ZiNmJjMmEyZjkzMWJkY2M0ZTc5ODI5MTc4YzczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNLS3M3ek84cUJ6SHE1V1NHWVZvUkE9PSIsInZhbHVlIjoiKzF2bEVud3dVSW55WVpPSG1wS256aFdFNzZtengvbVkybXlzbUFreTdiTU9WRGJJYlpCWnQ3ZlNQYlhZcVAvNDgrTzIwdzVCTzQ3NzJaZ1VhWmF3Q3BvV1E1YjVRUWdBMU43eFVQc3EyTUpOVDl0ZWxEZllFUm1JQkZ6Y0VlN0QiLCJtYWMiOiIyN2VhOWQ4YTBhZDIxODY4MTY5ZDM5MDU5OWFlMmM3Y2M2OGY5MGY1YjkxYjE0NWM3Y2JiMjNmZjcxOGZkMWQyIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IndLTVMyNjZ4ZzIrRkpEcDQya1haaFE9PSIsInZhbHVlIjoieDF3QzFUVHQ4ODBvQUxwdk05MXp6U0NvbS9pWXdHbjZ0bDMyZlY4Ym4zVDlzRmpHTUR6LzRISEhJdzJmUVBtWmZON3NmWGgvdDU2UXgxNHlkc1gxeFpVYzhkWlJyTEtvaXR4NmI4UkxPM2owcG9DNDl1Sk9VU2orNHdYWUdLY0ciLCJtYWMiOiIwNDU5YjFkODJjNmY1MGQ1NjcwNTJiMDdiNDkwNzg1YWJiNWI1MDVjNmJmZTlhYjYzZGJlMDQ3ZDlmYjE5NjA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134440587\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1836338225 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6n7BHowcS6we7JFkDGDZRBjg4hsvpTWKVgnVcqpE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836338225\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-880053984 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 02:02:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880053984\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-944151873 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XUwvxFiNvesCW0XyPwUfd6ZO6UfZ7V8fkPRf2o9i</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://recland.local/admin/jobs-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944151873\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/admin/jobs-management/search", "action_name": "jobs-management.search", "controller_action": "App\\Http\\Controllers\\Admin\\JobCrudController@search"}, "badge": null}}