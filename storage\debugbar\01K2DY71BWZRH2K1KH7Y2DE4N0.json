{"__meta": {"id": "01K2DY71BWZRH2K1KH7Y2DE4N0", "datetime": "2025-08-12 08:27:16", "utime": **********.093121, "method": "POST", "uri": "/job/api/list", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 25, "messages": [{"message": "[08:27:15] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"skill\\\":null,\\\"lang\\\":\\\"vi\\\"}', 'http:\\/\\/recland.local\\/job\\/api\\/list', 'http:\\/\\/recland.local\\/', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"24\\\"],\\\"x-xsrf-token\\\":[\\\"eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0=\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"application\\\\\\/json, text\\\\\\/plain, *\\\\\\/*\\\"],\\\"content-type\\\":[\\\"application\\\\\\/json\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IktsZHNpbWFNNVVSY0lidmt4UWZDZ1E9PSIsInZhbHVlIjoiaExNNUhpMTFYb3d0bkdHWUIvank1eW0vM1BwZ2cwTVBvVEhzQ1lmWnR5TytoL2M1aGtTMExZOXdEU3NMbThDTU15OFp3VUUvRUdZTG1CSFZKUVRuK0ZwRFlLa0czSFZFTlEwZTB4MDRybjVoZ2p0SlFrbHliSVRHU2JweUJZdHIiLCJtYWMiOiJlY2VmZTcwODM2ZmM5NGVjMzY2ZTVlYzVkMDJhMjk0MWMwNjkxMWU2OGYxZGQyOTU4OTZmMTBmZGUzZDE4MjlhIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:15', '2025-08-12 08:27:15')\\n-- \",\n    \"Time:\": 25\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.28428, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:15] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = '1' and `job`.`status` = '1' and `job`.`expire_at` >= '2025-08-12' order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30\\n-- \",\n    \"Time:\": 34.47\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.32307, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:15] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` in (3, 187, 234, 274, 277, 278, 286, 487, 696)\\n-- \",\n    \"Time:\": 0.6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.335199, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:15] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `job_meta` where `job_meta`.`job_id` in (631, 700, 710, 818, 819, 820, 821, 822, 823, 825, 826, 827, 828, 838, 861, 865, 866, 1642, 1643, 1644)\\n-- \",\n    \"Time:\": 1.26\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.33979, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:15] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect skills from `job` where `skills` is not null and `skills` != '' and `is_active` = '1' and `status` = '1' and `expire_at` >= '2025-08-12' group by `skills` order by COUNT(id) DESC limit 8\\n-- \",\n    \"Time:\": 17.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.385105, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1644' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.010448, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1642' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.016654, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '1643' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.37\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.020226, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '631' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.023711, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '700' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.027417, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '821' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.030948, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '865' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.034549, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '866' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.037881, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '861' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.04132, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '838' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.044857, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '825' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.048459, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '826' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.051865, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '827' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.39\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.055693, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '828' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.059093, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '818' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.062789, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '822' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.066323, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '823' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.06962, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '819' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.32\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.072994, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '820' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.43\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.08453, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:16] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = '710' and `type` = 'save'\\n-- \",\n    \"Time:\": 0.31\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.088548, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": 1754962034.762653, "end": **********.093164, "duration": 1.3305108547210693, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1754962034.762653, "relative_start": 0, "end": **********.187437, "relative_end": **********.187437, "duration": 0.*****************, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.187451, "relative_start": 0.*****************, "end": **********.093167, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "906ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.201447, "relative_start": 0.****************, "end": **********.208627, "relative_end": **********.208627, "duration": 0.007179975509643555, "duration_str": "7.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "59MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST job/api/list", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "job.api.list", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Frontend/JobController.php:23-43</a>"}, "queries": {"count": 25, "nb_statements": 25, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08551000000000004, "accumulated_duration_str": "85.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"skill\\\":null,\\\"lang\\\":\\\"vi\\\"}', 'http://recland.local/job/api/list', 'http://recland.local/', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"24\\\"],\\\"x-xsrf-token\\\":[\\\"eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0=\\\"],\\\"x-requested-with\\\":[\\\"XMLHttpRequest\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"application\\/json, text\\/plain, *\\/*\\\"],\\\"content-type\\\":[\\\"application\\/json\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IktsZHNpbWFNNVVSY0lidmt4UWZDZ1E9PSIsInZhbHVlIjoiaExNNUhpMTFYb3d0bkdHWUIvank1eW0vM1BwZ2cwTVBvVEhzQ1lmWnR5TytoL2M1aGtTMExZOXdEU3NMbThDTU15OFp3VUUvRUdZTG1CSFZKUVRuK0ZwRFlLa0czSFZFTlEwZTB4MDRybjVoZ2p0SlFrbHliSVRHU2JweUJZdHIiLCJtYWMiOiJlY2VmZTcwODM2ZmM5NGVjMzY2ZTVlYzVkMDJhMjk0MWMwNjkxMWU2OGYxZGQyOTU4OTZmMTBmZGUzZDE4MjlhIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:15', '2025-08-12 08:27:15')", "type": "query", "params": [], "bindings": ["POST", "{\"skill\":null,\"lang\":\"vi\"}", "http://recland.local/job/api/list", "http://recland.local/", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"24\"],\"x-xsrf-token\":[\"eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0=\"],\"x-requested-with\":[\"XMLHttpRequest\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"application\\/json, text\\/plain, *\\/*\"],\"content-type\":[\"application\\/json\"],\"origin\":[\"http:\\/\\/recland.local\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IktsZHNpbWFNNVVSY0lidmt4UWZDZ1E9PSIsInZhbHVlIjoiaExNNUhpMTFYb3d0bkdHWUIvank1eW0vM1BwZ2cwTVBvVEhzQ1lmWnR5TytoL2M1aGtTMExZOXdEU3NMbThDTU15OFp3VUUvRUdZTG1CSFZKUVRuK0ZwRFlLa0czSFZFTlEwZTB4MDRybjVoZ2p0SlFrbHliSVRHU2JweUJZdHIiLCJtYWMiOiJlY2VmZTcwODM2ZmM5NGVjMzY2ZTVlYzVkMDJhMjk0MWMwNjkxMWU2OGYxZGQyOTU4OTZmMTBmZGUzZDE4MjlhIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:27:15", "2025-08-12 08:27:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.259616, "duration": 0.025, "duration_str": "25ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 29.236}, {"sql": "select `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = 1 and `job`.`status` = 1 and `job`.`expire_at` >= '2025-08-12' order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30", "type": "query", "params": [], "bindings": [1, 1, "2025-08-12"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 71}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.288779, "duration": 0.03447, "duration_str": "34.47ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:162", "source": {"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=162", "ajax": false, "filename": "JobRepository.php", "line": "162"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select `job`.*, `job_meta`.`priority` from `job` inner join `job_meta` on `job`.`id` = `job_meta`.`job_id` where `job`.`is_active` = ? and `job`.`status` = ? and `job`.`expire_at` >= ? order by FIELD(job_meta.priority, 'Premium', 'Hot', 'New'), `job`.`publish_at` desc limit 30", "hash": "51db4e56bfec945cfddd15568647bcf7eca0219aef82a683853ff6399afd1f9e"}, "start_percent": 29.236, "width_percent": 40.311}, {"sql": "select * from `companies` where `companies`.`id` in (3, 187, 234, 274, 277, 278, 286, 487, 696)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 71}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3346748, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "JobRepository.php:162", "source": {"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=162", "ajax": false, "filename": "JobRepository.php", "line": "162"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` in (3, 187, 234, 274, 277, 278, 286, 487, 696)", "hash": "7394ccd09d0fa3400b133ecb95e4226330257b1927b44dccd85fd01cb54d15f0"}, "start_percent": 69.547, "width_percent": 0.702}, {"sql": "select * from `job_meta` where `job_meta`.`job_id` in (631, 700, 710, 818, 819, 820, 821, 822, 823, 825, 826, 827, 828, 838, 861, 865, 866, 1642, 1643, 1644)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, {"index": 20, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 71}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.338593, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:162", "source": {"index": 19, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=162", "ajax": false, "filename": "JobRepository.php", "line": "162"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `job_meta` where `job_meta`.`job_id` in (631, 700, 710, 818, 819, 820, 821, 822, 823, 825, 826, 827, 828, 838, 861, 865, 866, 1642, 1643, 1644)", "hash": "44e9baa7bd889d5b138d212ee35b5b1c9d7c7a24d07cfd8218fb2cd058992f79"}, "start_percent": 70.249, "width_percent": 1.474}, {"sql": "select skills from `job` where `skills` is not null and `skills` != '' and `is_active` = 1 and `status` = 1 and `expire_at` >= '2025-08-12' group by `skills` order by COUNT(id) DESC limit 8", "type": "query", "params": [], "bindings": ["", 1, 1, "2025-08-12"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 441}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/JobService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\JobService.php", "line": 103}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Frontend/JobController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Api\\Frontend\\JobController.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.367856, "duration": 0.01735, "duration_str": "17.35ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:441", "source": {"index": 14, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\JobRepository.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FJobRepository.php&line=441", "ajax": false, "filename": "JobRepository.php", "line": "441"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select skills from `job` where `skills` is not null and `skills` != ? and `is_active` = ? and `status` = ? and `expire_at` >= ? group by `skills` order by COUNT(id) DESC limit 8", "hash": "826af2a1797ae34cd2334ef358a2e7d746dd76ad74e4c8e6ae0dfdbdfbd8d685"}, "start_percent": 71.723, "width_percent": 20.29}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1644 and `type` = 'save'", "type": "query", "params": [], "bindings": [1644, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.010202, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "37cb223e001eb4ebe6d4ad977e897ea212b5d7b37634d3dbf61029cace96ad3d"}, "start_percent": 92.013, "width_percent": 0.608}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1642 and `type` = 'save'", "type": "query", "params": [], "bindings": [1642, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.0163329, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "21f4845ad0691e86083cadbcb755f57cf848010de1485b9b59aa23c8989fceca"}, "start_percent": 92.621, "width_percent": 0.456}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 1643 and `type` = 'save'", "type": "query", "params": [], "bindings": [1643, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.019926, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "a5cdfcd2bfa92b2c59d701bc37b0e7d1450a316a4dba557cc8d45866c1e63b8b"}, "start_percent": 93.077, "width_percent": 0.433}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 631 and `type` = 'save'", "type": "query", "params": [], "bindings": [631, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.0234501, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "8d2643fe6adc6a1a3f7851c3611e56fee43fdad48f936edbf94a49089cc45fd8"}, "start_percent": 93.51, "width_percent": 0.386}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 700 and `type` = 'save'", "type": "query", "params": [], "bindings": [700, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.0271661, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "a4a7688f4d2a14b474eed8d7053ed00e4eba2b8277ef2c32fedb562a44087eeb"}, "start_percent": 93.895, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 821 and `type` = 'save'", "type": "query", "params": [], "bindings": [821, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.030697, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "c755463b56113549e67ed05c69971f07d9dfba0da756ac8fad979e9f529536dd"}, "start_percent": 94.27, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 865 and `type` = 'save'", "type": "query", "params": [], "bindings": [865, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.034307, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "c3ed1104d52112e67f2b97a2c763b016ae8647ece13ca14d2ca02ec050f3d6d2"}, "start_percent": 94.644, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 866 and `type` = 'save'", "type": "query", "params": [], "bindings": [866, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.037656, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "a314d92e438e7ee2ca9f06b6780e6c89b1a6199c0fcfdbf517387a3b85fd4138"}, "start_percent": 95.006, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 861 and `type` = 'save'", "type": "query", "params": [], "bindings": [861, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.0410671, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "0a2d0da7220072e31cb364aadc1bc6f0c60a76719a8df740d5f5f1e291607bb1"}, "start_percent": 95.369, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 838 and `type` = 'save'", "type": "query", "params": [], "bindings": [838, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.044624, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "d23eba12ea035e9077f71f7b56575c0c770e6ac1d830c7f4d7b657dc17ab9d84"}, "start_percent": 95.743, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 825 and `type` = 'save'", "type": "query", "params": [], "bindings": [825, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.048218, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "e98c863d6ec1cb07b51e0b7988e1b292df9800b92eb2997f96609454357d38f7"}, "start_percent": 96.106, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 826 and `type` = 'save'", "type": "query", "params": [], "bindings": [826, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.0516381, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "89fb8cf06a2c1a37653300034eb51e2fcda8537fd65f192bdcf91bed04537427"}, "start_percent": 96.468, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 827 and `type` = 'save'", "type": "query", "params": [], "bindings": [827, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.055372, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "f63424fe268ecad4507b5e91ada88aec8f12067ffe9691b586932fb2490a0f40"}, "start_percent": 96.831, "width_percent": 0.456}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 828 and `type` = 'save'", "type": "query", "params": [], "bindings": [828, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.058841, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "55ac752ae3140a9f7622826834578e6372da9581a62423ce29db221712d9fbad"}, "start_percent": 97.287, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 818 and `type` = 'save'", "type": "query", "params": [], "bindings": [818, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.062547, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "c951b44c80f2f18f4a12f8d18ecb948b038b122fdaa8f883bf946f07b41e2c09"}, "start_percent": 97.661, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 822 and `type` = 'save'", "type": "query", "params": [], "bindings": [822, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.066072, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "c834301e2d7ccc7357d67c8e6f45c988afa305543ccac0315736daefe4389ca1"}, "start_percent": 98.024, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 823 and `type` = 'save'", "type": "query", "params": [], "bindings": [823, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.069379, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "826a49bef4f20d5f45521e5aaaca163a422bbe47b8dce8c154f70eea7afb31ff"}, "start_percent": 98.398, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 819 and `type` = 'save'", "type": "query", "params": [], "bindings": [819, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.072743, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "b64ed6e8cb14fa2323194c1541f961063a3a0f59e6acd954ccf49562afdf12f1"}, "start_percent": 98.76, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 820 and `type` = 'save'", "type": "query", "params": [], "bindings": [820, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.0841959, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "d8f12728523aff3d8c25eedf0214f50adc5db08d436651cd8b03d8f6d85eef1d"}, "start_percent": 99.135, "width_percent": 0.503}, {"sql": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = 710 and `type` = 'save'", "type": "query", "params": [], "bindings": [710, "save"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/UserJobCareService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\UserJobCareService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Helpers/Common.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Helpers\\Common.php", "line": 126}, {"index": 18, "namespace": null, "name": "app/Http/Resources/Frontend/Api/JobResource.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Resources\\Frontend\\Api\\JobResource.php", "line": 60}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 101}], "start": **********.0883071, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserJobCareRepository.php:34", "source": {"index": 15, "namespace": null, "name": "app/Repositories/UserJobCareRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\UserJobCareRepository.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FUserJobCareRepository.php&line=34", "ajax": false, "filename": "UserJobCareRepository.php", "line": "34"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select count(*) as aggregate from `user_job_cares` where `user_id` is null and `job_id` = ? and `type` = ?", "hash": "f920e734ca570aaec12a76ace837c3b12a7580deb157a5b23c5ec1bb3477a8ad"}, "start_percent": 99.637, "width_percent": 0.363}]}, "models": {"data": {"App\\Models\\Job": {"retrieved": 27, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\JobMeta": {"retrieved": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FJobMeta.php&line=1", "ajax": false, "filename": "JobMeta.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}}, "count": 57, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 56}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "512", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/job/api/list", "action_name": "job.api.list", "controller_action": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list", "uri": "POST job/api/list", "controller": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FApi%2FFrontend%2FJobController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Frontend/JobController.php:23-43</a>", "middleware": "web, localization, visit-website", "duration": "1.33s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1656071512 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1656071512\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-301487809 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>skill</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-301487809\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-277076049 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://recland.local/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754907683$o1$g1$t1754909515$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ijh3OUhKdjFaMkZrK3NZQlRJa1JmNlE9PSIsInZhbHVlIjoiK2x5NitaYTg3ZWhUa2xPSFZSWng3S2FGcWV0U2lxak9aTVRqbzVNbCtNVElBVmJ3RDBBTXBYT3lqUUx2cG9uVkNnZkFjamlxMGFRbUJYazdBOGk0ZmZ3WlR3MHcvVTkyZTlOeEZVbCtIT3RNUHJPc2RvOU9nUWVDM1NmRUJZaVEiLCJtYWMiOiI3MTc1ZWQ2YjI0MTViM2E5NTJlMWE1NzhkY2UxOTJhYWQwN2ZkMDIxYWJjNzAyMDZjMGEwNmRjNmQzMjBiYTBjIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IktsZHNpbWFNNVVSY0lidmt4UWZDZ1E9PSIsInZhbHVlIjoiaExNNUhpMTFYb3d0bkdHWUIvank1eW0vM1BwZ2cwTVBvVEhzQ1lmWnR5TytoL2M1aGtTMExZOXdEU3NMbThDTU15OFp3VUUvRUdZTG1CSFZKUVRuK0ZwRFlLa0czSFZFTlEwZTB4MDRybjVoZ2p0SlFrbHliSVRHU2JweUJZdHIiLCJtYWMiOiJlY2VmZTcwODM2ZmM5NGVjMzY2ZTVlYzVkMDJhMjk0MWMwNjkxMWU2OGYxZGQyOTU4OTZmMTBmZGUzZDE4MjlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277076049\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-599483817 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OLDKqoGpbczGmQ3iONEKK3dsTzLGGTm8QWy0Jh3e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599483817\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2023640121 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:27:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023640121\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1915018095 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>512</span>\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915018095\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/job/api/list", "action_name": "job.api.list", "controller_action": "App\\Http\\Controllers\\Api\\Frontend\\JobController@list"}, "badge": null}}