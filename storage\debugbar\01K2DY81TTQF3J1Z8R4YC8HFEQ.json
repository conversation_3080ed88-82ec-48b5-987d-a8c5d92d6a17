{"__meta": {"id": "01K2DY81TTQF3J1Z8R4YC8HFEQ", "datetime": "2025-08-12 08:27:49", "utime": **********.340526, "method": "GET", "uri": "/logout", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 7, "messages": [{"message": "[08:27:48] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.898881, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:48] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.899377, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/logout', 'http:\\/\\/recland.local\\/', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g0$t1754962034$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImhZZVl6d29ZdGlFQ2pZSytQQUhEa3c9PSIsInZhbHVlIjoibXZ6b2lGUlFmazlqYjJYWVRFZnErSWhnazBrcnJzTXRPUnVwTDJNRUMrOXFaV0VJdHZUY3NTTzllVDFWRTMrT3JZSlU1VXUzZEtiZlU3Uk9SSzRzM2Q2K25PcVpWYUpveVg5UE5sQWprVWFNSCt6RHRrZjN5cHdMUURhN0JnUGUiLCJtYWMiOiIxYmIxNzFiZjBmMTM4YTY4YTU2NDg5NGM2N2M2NzliMDA3YTU2NGMwNmMzNDEyNDMyYTQ3NTkwMjAzOTJmYjVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlUvbHA4RXNrZWxxbnlHVk5pV2NkZ2c9PSIsInZhbHVlIjoiYmdyMGl3YmwzNkNNOWVqYWU4ekxGakk0N00yTzl1RXorLzUxWEVBY0tkaXk1eWdMSmY3bFJhRG1vdUgvTkk5cXNmWG9JM0tMQmNXTjBmQjA5SUpENDJJdktiUnduTU5ITlpHOHpjOGFMWDlPRWdDZ2RjNS9keDhFWFQwUTVETloiLCJtYWMiOiI1YTJhYTRkYjkxMGE4YTM1N2EzZjM5MzEwMzkzNTQyMDBjZDdiZGExYWQzZjE3MGVjNmY5NGJiYWE0ZTkwN2Q1IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:48', '2025-08-12 08:27:48')\\n-- \",\n    \"Time:\": 2.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.940642, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '512' limit 1\\n-- \",\n    \"Time:\": 0.74\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.947337, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nupdate `users` set `remember_token` = 'yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8' where `id` = '512'\\n-- \",\n    \"Time:\": 0.44\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.952309, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:49] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1' limit 1\\n-- \",\n    \"Time:\": 0.65\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.11699, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:49] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"remember_token\\\":\\\"ofX19AAMB01Dk38VNDGTNlfq1ZGTjU2D3OupM89jd8tKo1029azwcdFVUtBd\\\"}', '{\\\"remember_token\\\":\\\"yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8\\\"}', 'updated', '512', 'App\\\\Models\\\\User', '1', 'App\\\\Models\\\\User', '', '127.0.0.1', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', 'http:\\/\\/recland.local\\/logout', '2025-08-12 08:27:49', '2025-08-12 08:27:49')\\n-- \",\n    \"Time:\": 0.65\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.224121, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.453165, "end": **********.340575, "duration": 0.8874099254608154, "duration_str": "887ms", "measures": [{"label": "Booting", "start": **********.453165, "relative_start": 0, "end": **********.869055, "relative_end": **********.869055, "duration": 0.****************, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.869067, "relative_start": 0.*****************, "end": **********.340578, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "472ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.882617, "relative_start": 0.*****************, "end": **********.889439, "relative_end": **********.889439, "duration": 0.006822109222412109, "duration_str": "6.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET logout", "middleware": "web, localization, visit-website", "controller": "App\\Http\\Controllers\\Frontend\\UserController@recLogout<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=407\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "as": "rec-logout", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=407\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserController.php:407-412</a>"}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.004999999999999999, "accumulated_duration_str": "5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/logout', 'http://recland.local/', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g0$t1754962034$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImhZZVl6d29ZdGlFQ2pZSytQQUhEa3c9PSIsInZhbHVlIjoibXZ6b2lGUlFmazlqYjJYWVRFZnErSWhnazBrcnJzTXRPUnVwTDJNRUMrOXFaV0VJdHZUY3NTTzllVDFWRTMrT3JZSlU1VXUzZEtiZlU3Uk9SSzRzM2Q2K25PcVpWYUpveVg5UE5sQWprVWFNSCt6RHRrZjN5cHdMUURhN0JnUGUiLCJtYWMiOiIxYmIxNzFiZjBmMTM4YTY4YTU2NDg5NGM2N2M2NzliMDA3YTU2NGMwNmMzNDEyNDMyYTQ3NTkwMjAzOTJmYjVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlUvbHA4RXNrZWxxbnlHVk5pV2NkZ2c9PSIsInZhbHVlIjoiYmdyMGl3YmwzNkNNOWVqYWU4ekxGakk0N00yTzl1RXorLzUxWEVBY0tkaXk1eWdMSmY3bFJhRG1vdUgvTkk5cXNmWG9JM0tMQmNXTjBmQjA5SUpENDJJdktiUnduTU5ITlpHOHpjOGFMWDlPRWdDZ2RjNS9keDhFWFQwUTVETloiLCJtYWMiOiI1YTJhYTRkYjkxMGE4YTM1N2EzZjM5MzEwMzkzNTQyMDBjZDdiZGExYWQzZjE3MGVjNmY5NGJiYWE0ZTkwN2Q1IiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 08:27:48', '2025-08-12 08:27:48')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/logout", "http://recland.local/", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g0$t1754962034$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImhZZVl6d29ZdGlFQ2pZSytQQUhEa3c9PSIsInZhbHVlIjoibXZ6b2lGUlFmazlqYjJYWVRFZnErSWhnazBrcnJzTXRPUnVwTDJNRUMrOXFaV0VJdHZUY3NTTzllVDFWRTMrT3JZSlU1VXUzZEtiZlU3Uk9SSzRzM2Q2K25PcVpWYUpveVg5UE5sQWprVWFNSCt6RHRrZjN5cHdMUURhN0JnUGUiLCJtYWMiOiIxYmIxNzFiZjBmMTM4YTY4YTU2NDg5NGM2N2M2NzliMDA3YTU2NGMwNmMzNDEyNDMyYTQ3NTkwMjAzOTJmYjVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlUvbHA4RXNrZWxxbnlHVk5pV2NkZ2c9PSIsInZhbHVlIjoiYmdyMGl3YmwzNkNNOWVqYWU4ekxGakk0N00yTzl1RXorLzUxWEVBY0tkaXk1eWdMSmY3bFJhRG1vdUgvTkk5cXNmWG9JM0tMQmNXTjBmQjA5SUpENDJJdktiUnduTU5ITlpHOHpjOGFMWDlPRWdDZ2RjNS9keDhFWFQwUTVETloiLCJtYWMiOiI1YTJhYTRkYjkxMGE4YTM1N2EzZjM5MzEwMzkzNTQyMDBjZDdiZGExYWQzZjE3MGVjNmY5NGJiYWE0ZTkwN2Q1IiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 08:27:48", "2025-08-12 08:27:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.938432, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 0, "width_percent": 50.4}, {"sql": "select * from `users` where `id` = 512 limit 1", "type": "query", "params": [], "bindings": [512], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 571}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 409}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.94666, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "f57aeccc1e0beb29028e5e2a4241e4eed6d22790637d6f4352f9bde1e64d2f30"}, "start_percent": 50.4, "width_percent": 14.8}, {"sql": "update `users` set `remember_token` = 'yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8' where `id` = 512", "type": "query", "params": [], "bindings": ["yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8", 512], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 101}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 650}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 576}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 409}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9519331, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:101", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=101", "ajax": false, "filename": "EloquentUserProvider.php", "line": "101"}, "connection": "hri_recland_product", "explain": null, "start_percent": 65.2, "width_percent": 8.8}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Resolvers/UserResolver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Resolvers\\UserResolver.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditable.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditable.php", "line": 409}], "start": **********.1164181, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "ac0ef66d2bf40f3993c8e40de021b0d99da887a13adec01b9fbb0601a2bf69c5"}, "start_percent": 74, "width_percent": 13}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"remember_token\\\":\\\"ofX19AAMB01Dk38VNDGTNlfq1ZGTjU2D3OupM89jd8tKo1029azwcdFVUtBd\\\"}', '{\\\"remember_token\\\":\\\"yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8\\\"}', 'updated', 512, 'App\\Models\\User', 1, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'http://recland.local/logout', '2025-08-12 08:27:49', '2025-08-12 08:27:49')", "type": "query", "params": [], "bindings": ["{\"remember_token\":\"ofX19AAMB01Dk38VNDGTNlfq1ZGTjU2D3OupM89jd8tKo1029azwcdFVUtBd\"}", "{\"remember_token\":\"yT8TiHfsFeW2Aqgojov6dLohGDdaTu6N1XI8KPHogDoamyKakbHg9Ik8ZJH8\"}", "updated", 512, "App\\Models\\User", 1, "App\\Models\\User", null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "http://recland.local/logout", "2025-08-12 08:27:49", "2025-08-12 08:27:49"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 101}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 650}], "start": **********.2235482, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Auditor.php:83", "source": {"index": 23, "namespace": null, "name": "vendor/owen-it/laravel-auditing/src/Auditor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FAuditor.php&line=83", "ajax": false, "filename": "Auditor.php", "line": "83"}, "connection": "hri_recland_product", "explain": null, "start_percent": 87, "width_percent": 13}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 2, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "OwenIt\\Auditing\\Models\\Audit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fowen-it%2Flaravel-auditing%2Fsrc%2FModels%2FAudit.php&line=1", "ajax": false, "filename": "Audit.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 2, "retrieved": 2, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://recland.local/logout", "action_name": "rec-logout", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@recLogout", "uri": "GET logout", "controller": "App\\Http\\Controllers\\Frontend\\UserController@recLogout<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=407\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php&line=407\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserController.php:407-412</a>", "middleware": "web, localization, visit-website", "duration": "892ms", "peak_memory": "42MB", "response": "Redirect to http://recland.local/login", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1088651621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1088651621\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-156871550 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-156871550\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1893250030 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://recland.local/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g0$t1754962034$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImhZZVl6d29ZdGlFQ2pZSytQQUhEa3c9PSIsInZhbHVlIjoibXZ6b2lGUlFmazlqYjJYWVRFZnErSWhnazBrcnJzTXRPUnVwTDJNRUMrOXFaV0VJdHZUY3NTTzllVDFWRTMrT3JZSlU1VXUzZEtiZlU3Uk9SSzRzM2Q2K25PcVpWYUpveVg5UE5sQWprVWFNSCt6RHRrZjN5cHdMUURhN0JnUGUiLCJtYWMiOiIxYmIxNzFiZjBmMTM4YTY4YTU2NDg5NGM2N2M2NzliMDA3YTU2NGMwNmMzNDEyNDMyYTQ3NTkwMjAzOTJmYjVmIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IlUvbHA4RXNrZWxxbnlHVk5pV2NkZ2c9PSIsInZhbHVlIjoiYmdyMGl3YmwzNkNNOWVqYWU4ekxGakk0N00yTzl1RXorLzUxWEVBY0tkaXk1eWdMSmY3bFJhRG1vdUgvTkk5cXNmWG9JM0tMQmNXTjBmQjA5SUpENDJJdktiUnduTU5ITlpHOHpjOGFMWDlPRWdDZ2RjNS9keDhFWFQwUTVETloiLCJtYWMiOiI1YTJhYTRkYjkxMGE4YTM1N2EzZjM5MzEwMzkzNTQyMDBjZDdiZGExYWQzZjE3MGVjNmY5NGJiYWE0ZTkwN2Q1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893250030\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1608256669 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OLDKqoGpbczGmQ3iONEKK3dsTzLGGTm8QWy0Jh3e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608256669\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-369171339 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 01:27:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://recland.local/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369171339\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-371625782 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371625782\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://recland.local/logout", "action_name": "rec-logout", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@recLogout"}, "badge": "302 Found"}}